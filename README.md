![输入图片说明](https://images.gitee.com/uploads/images/2019/0711/174845_6db7724e_5094767.png "商城.png")
Mall4j开源商城，一个基于spring boot、spring oauth2.0、mybatis、redis的轻量级、前后端分离、防范xss攻击、拥有分布式锁，为生产环境多实例完全准备，数据库为b2b2c设计，拥有完整sku和下单流程的开源商城

## Spring以及VUE官方宣布，SpringBoot2与Vue2已在2023年底停止维护。新项目建议使用SpringBoot3+Vue3的组合，本商城已完成升级!!!

## 前言

`Mall4j`商城系统致力于为中小企业打造一个完整、易于维护的开源的电商商城系统，采用现阶段流行技术实现。后台管理系统包含商品管理、订单管理、运费模板、规格管理、会员管理、运营管理、内容管理、统计报表、权限管理、设置等模块。开源版本商城属于B2C单商户商城系统，不含营销活动，如需更多模式的商城请查看[Mall4j商城官网](https://www.mall4j.com)。

## 商城文档

这代码有没有文档呀？ 当然有啦，你已经下载了，在doc这个文件夹上，实在不知道，我就给链接出来咯：

gitee：[https://gitee.com/gz-yami/mall4j/tree/master/doc](https://gitee.com/gz-yami/mall4j/tree/master/doc)

看云：[https://www.kancloud.cn/yami/mall4j](https://www.kancloud.cn/yami/mall4j)

**开发环境搭建视频（推荐先看下文档再看视频）：[https://www.bilibili.com/video/BV1eW4y1V7c1](https://www.bilibili.com/video/BV1eW4y1V7c1)** 

有声音了。如果视频对你有用，记得点赞投币噢。

## 商城授权

除了开源版本，我们商业版有B2C商城、B2B2C商城、O2O商城、S2B2C商城、SAAS商城，多端呈现：小程序 + PC + H5 + APP，更多详情请查看官网 

Mall4j商城官网 [https://www.mall4j.com](https://www.mall4j.com)

Mall4j商城开源版 使用 AGPLv3 开源，请遵守 AGPLv3 的相关条款，或者联系作者获取商业授权([https://www.mall4j.com](https://www.mall4j.com))

## 项目链接

java后台：[https://gitee.com/gz-yami/mall4j](https://gitee.com/gz-yami/mall4j)

vue后台前端：[https://gitee.com/gz-yami/mall4v](https://gitee.com/gz-yami/mall4v)

小程序：[https://gitee.com/gz-yami/mall4m](https://gitee.com/gz-yami/mall4m)

uni-app：[https://gitee.com/gz-yami/mall4uni](https://gitee.com/gz-yami/mall4uni)


## 商城演示地址

 商业版商城小程序演示

![输入图片说明](screenshot/%E5%AE%87%E5%AE%99%E7%89%88%E5%B0%8F%E7%A8%8B%E5%BA%8F.png)

## 商城技术选型

| 技术                  | 版本      | 说明                           |
|---------------------|---------|------------------------------|
| Spring Boot         | 3.0.4   | MVC核心框架                      |
| Spring Security web | 3.0.4   | web应用安全防护                    |
| satoken             | 1.34.0  | 一个轻量级 Java 权限认证框架，取代spring oauth2 |
| MyBatis             | 3.5.10  | ORM框架                        |
| MyBatisPlus         | 3.5.3.1 | 基于mybatis，使用lambda表达式的       |
| spring-doc          | 2.0.0   | 接口文档工具                       |
| jakarta-validation  | 3.0.2   | 验证框架                         |
| redisson            | 3.19.3  | 对redis进行封装、集成分布式锁等           |
| hikari              | 5.0.1   | 数据库连接池                       |
| logback             | 1.4.5   | log日志工具                      |
| lombok              | 1.18.26 | 简化对象封装工具                     |
| hutool              | 5.8.15  | 更适合国人的java工具集                |
| knife4j             | 4.0.0   | 基于swagger，更便于国人使用的swagger ui |


通过阿里的代码规范扫描工具（Alibaba Java Coding Guidelines plugin），扫描无异常：

![规约扫描结果](screenshot/规约.png)

## 部署教程

ps: 如果你不清楚如何启动我们的商城，请仔细阅wiki当中的文档


[https://gitee.com/gz-yami/mall4j/wikis](https://gitee.com/gz-yami/mall4j/wikis)

**开发环境搭建视频（推荐先看下文档再看视频）：[https://www.bilibili.com/video/BV1eW4y1V7c1](https://www.bilibili.com/video/BV1eW4y1V7c1)** 

有声音了。如果视频对你有用，记得点赞投币噢。



## 相关截图




### 1. 后台截图
![商城后台](https://gitee.com/gz-yami/mall4j/raw/master/screenshot/mall4jV.gif)



### 2. 移动端截图

![输入图片说明](https://images.gitee.com/uploads/images/2021/1110/145209_2ec1ad04_5094767.png "商城.png")



## 提交反馈
- Mall4j商城官网 [https://www.mall4j.com](https://www.mall4j.com)


- Mall4j商城官方技术QQ 1群：722835385（3000人群已满）
- Mall4j商城官方技术QQ 2群：729888395（2000人群已满）
- Mall4j商城官方技术QQ 3群：630293864
- 如需购买商城商业版源码，请联系商务微信

![输入图片说明](https://img.mall4j.com/contact.png-v)

## 特别鸣谢

- wxjava: [https://github.com/Wechat-Group/WxJava](https://github.com/Wechat-Group/WxJava)
- sa-token: [https://gitee.com/dromara/sa-token](https://gitee.com/dromara/sa-token)



## mall4cloud微服务商城版本已上线
[https://gitee.com/gz-yami/mall4cloud](https://gitee.com/gz-yami/mall4cloud)

## 更多信息请查看Mall4j商城官网 <[https://www.mall4j.com](https://www.mall4j.com)>
