/*
SQLyog Ultimate v12.5.1 (64 bit)
MySQL - 5.7.23 : Database - yami_shops
*********************************************************************
*/

create database IF NOT EXISTS `yami_shops` default character set utf8mb4 collate utf8mb4_general_ci;

USE yami_shops;

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

/*Table structure for table `tz_area` */

DROP TABLE IF EXISTS `a_test_user`;

CREATE TABLE `a_test_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(50) DEFAULT NULL,
  `password` varchar(50) DEFAULT NULL,
  `hobby` varchar(50) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)  -- 末尾的逗号要去掉
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

INSERT INTO `a_test_user` (`user_name`, `password`, `hobby`) VALUES
    ('user01', 'pass01', '1'),
    ('user02', 'pass02', '2'),
    ('user03', 'pass03', '3'),
    ('user04', 'pass04', '4'),
    ('user05', 'pass05', '5'),
    ('user06', 'pass06', '1'),
    ('user07', 'pass07', '2'),
    ('user08', 'pass08', '3'),
    ('user09', 'pass09', '4'),
    ('user10', 'pass10', '5'),
    ('user11', 'pass11', '1'),
    ('user12', 'pass12', '2'),
    ('user13', 'pass13', '3'),
    ('user14', 'pass14', '4'),
    ('user15', 'pass15', '5'),
    ('user16', 'pass16', '1'),
    ('user17', 'pass17', '2'),
    ('user18', 'pass18', '3'),
    ('user19', 'pass19', '4'),
    ('user20', 'pass20', '5');


DROP TABLE IF EXISTS `tz_area`;

CREATE TABLE `tz_area` (
  `area_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `area_name` varchar(50) DEFAULT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  `level` int(1) DEFAULT NULL,
  PRIMARY KEY (`area_id`),
  KEY `parent_id` (`parent_id`) COMMENT '上级id'
) ENGINE=InnoDB AUTO_INCREMENT=659006000001 DEFAULT CHARSET=utf8;

/*Data for the table `tz_area` */

insert  into `tz_area`(`area_id`,`area_name`,`parent_id`,`level`) values
(110000000000,'北京市',0,1),
(110100000000,'市辖区',110000000000,2),
(110101000000,'东城区',110100000000,3),
(110102000000,'西城区',110100000000,3),
(110105000000,'朝阳区',110100000000,3),
(110106000000,'丰台区',110100000000,3),
(110107000000,'石景山区',110100000000,3),
(110108000000,'海淀区',110100000000,3),
(110109000000,'门头沟区',110100000000,3),
(110111000000,'房山区',110100000000,3),
(110112000000,'通州区',110100000000,3),
(110113000000,'顺义区',110100000000,3),
(110114000000,'昌平区',110100000000,3),
(110115000000,'大兴区',110100000000,3),
(110116000000,'怀柔区',110100000000,3),
(110117000000,'平谷区',110100000000,3),
(110118000000,'密云区',110100000000,3),
(110119000000,'延庆区',110100000000,3),
(120000000000,'天津市',0,1),
(120100000000,'市辖区',120000000000,2),
(120101000000,'和平区',120100000000,3),
(120102000000,'河东区',120100000000,3),
(120103000000,'河西区',120100000000,3),
(120104000000,'南开区',120100000000,3),
(120105000000,'河北区',120100000000,3),
(120106000000,'红桥区',120100000000,3),
(120110000000,'东丽区',120100000000,3),
(120111000000,'西青区',120100000000,3),
(120112000000,'津南区',120100000000,3),
(120113000000,'北辰区',120100000000,3),
(120114000000,'武清区',120100000000,3),
(120115000000,'宝坻区',120100000000,3),
(120116000000,'滨海新区',120100000000,3),
(120117000000,'宁河区',120100000000,3),
(120118000000,'静海区',120100000000,3),
(120119000000,'蓟州区',120100000000,3),
(130000000000,'河北省',0,1),
(130100000000,'石家庄市',130000000000,2),
(130101000000,'市辖区',130100000000,3),
(130102000000,'长安区',130100000000,3),
(130104000000,'桥西区',130100000000,3),
(130105000000,'新华区',130100000000,3),
(130107000000,'井陉矿区',130100000000,3),
(130108000000,'裕华区',130100000000,3),
(130109000000,'藁城区',130100000000,3),
(130110000000,'鹿泉区',130100000000,3),
(130111000000,'栾城区',130100000000,3),
(130121000000,'井陉县',130100000000,3),
(130123000000,'正定县',130100000000,3),
(130125000000,'行唐县',130100000000,3),
(130126000000,'灵寿县',130100000000,3),
(130127000000,'高邑县',130100000000,3),
(130128000000,'深泽县',130100000000,3),
(130129000000,'赞皇县',130100000000,3),
(130130000000,'无极县',130100000000,3),
(130131000000,'平山县',130100000000,3),
(130132000000,'元氏县',130100000000,3),
(130133000000,'赵县',130100000000,3),
(130171000000,'石家庄高新技术产业开发区',130100000000,3),
(130172000000,'石家庄循环化工园区',130100000000,3),
(130181000000,'辛集市',130100000000,3),
(130183000000,'晋州市',130100000000,3),
(130184000000,'新乐市',130100000000,3),
(130200000000,'唐山市',130000000000,2),
(130201000000,'市辖区',130200000000,3),
(130202000000,'路南区',130200000000,3),
(130203000000,'路北区',130200000000,3),
(130204000000,'古冶区',130200000000,3),
(130205000000,'开平区',130200000000,3),
(130207000000,'丰南区',130200000000,3),
(130208000000,'丰润区',130200000000,3),
(130209000000,'曹妃甸区',130200000000,3),
(130224000000,'滦南县',130200000000,3),
(130225000000,'乐亭县',130200000000,3),
(130227000000,'迁西县',130200000000,3),
(130229000000,'玉田县',130200000000,3),
(130271000000,'唐山市芦台经济技术开发区',130200000000,3),
(130272000000,'唐山市汉沽管理区',130200000000,3),
(130273000000,'唐山高新技术产业开发区',130200000000,3),
(130274000000,'河北唐山海港经济开发区',130200000000,3),
(130281000000,'遵化市',130200000000,3),
(130283000000,'迁安市',130200000000,3),
(130284000000,'滦州市',130200000000,3),
(130300000000,'秦皇岛市',130000000000,2),
(130301000000,'市辖区',130300000000,3),
(130302000000,'海港区',130300000000,3),
(130303000000,'山海关区',130300000000,3),
(130304000000,'北戴河区',130300000000,3),
(130306000000,'抚宁区',130300000000,3),
(130321000000,'青龙满族自治县',130300000000,3),
(130322000000,'昌黎县',130300000000,3),
(130324000000,'卢龙县',130300000000,3),
(130371000000,'秦皇岛市经济技术开发区',130300000000,3),
(130372000000,'北戴河新区',130300000000,3),
(130400000000,'邯郸市',130000000000,2),
(130401000000,'市辖区',130400000000,3),
(130402000000,'邯山区',130400000000,3),
(130403000000,'丛台区',130400000000,3),
(130404000000,'复兴区',130400000000,3),
(130406000000,'峰峰矿区',130400000000,3),
(130407000000,'肥乡区',130400000000,3),
(130408000000,'永年区',130400000000,3),
(130423000000,'临漳县',130400000000,3),
(130424000000,'成安县',130400000000,3),
(130425000000,'大名县',130400000000,3),
(130426000000,'涉县',130400000000,3),
(130427000000,'磁县',130400000000,3),
(130430000000,'邱县',130400000000,3),
(130431000000,'鸡泽县',130400000000,3),
(130432000000,'广平县',130400000000,3),
(130433000000,'馆陶县',130400000000,3),
(130434000000,'魏县',130400000000,3),
(130435000000,'曲周县',130400000000,3),
(130471000000,'邯郸经济技术开发区',130400000000,3),
(130473000000,'邯郸冀南新区',130400000000,3),
(130481000000,'武安市',130400000000,3),
(130500000000,'邢台市',130000000000,2),
(130501000000,'市辖区',130500000000,3),
(130502000000,'桥东区',130500000000,3),
(130503000000,'桥西区',130500000000,3),
(130521000000,'邢台县',130500000000,3),
(130522000000,'临城县',130500000000,3),
(130523000000,'内丘县',130500000000,3),
(130524000000,'柏乡县',130500000000,3),
(130525000000,'隆尧县',130500000000,3),
(130526000000,'任县',130500000000,3),
(130527000000,'南和县',130500000000,3),
(130528000000,'宁晋县',130500000000,3),
(130529000000,'巨鹿县',130500000000,3),
(130530000000,'新河县',130500000000,3),
(130531000000,'广宗县',130500000000,3),
(130532000000,'平乡县',130500000000,3),
(130533000000,'威县',130500000000,3),
(130534000000,'清河县',130500000000,3),
(130535000000,'临西县',130500000000,3),
(130571000000,'河北邢台经济开发区',130500000000,3),
(130581000000,'南宫市',130500000000,3),
(130582000000,'沙河市',130500000000,3),
(130600000000,'保定市',130000000000,2),
(130601000000,'市辖区',130600000000,3),
(130602000000,'竞秀区',130600000000,3),
(130606000000,'莲池区',130600000000,3),
(130607000000,'满城区',130600000000,3),
(130608000000,'清苑区',130600000000,3),
(130609000000,'徐水区',130600000000,3),
(130623000000,'涞水县',130600000000,3),
(130624000000,'阜平县',130600000000,3),
(130626000000,'定兴县',130600000000,3),
(130627000000,'唐县',130600000000,3),
(130628000000,'高阳县',130600000000,3),
(130629000000,'容城县',130600000000,3),
(130630000000,'涞源县',130600000000,3),
(130631000000,'望都县',130600000000,3),
(130632000000,'安新县',130600000000,3),
(130633000000,'易县',130600000000,3),
(130634000000,'曲阳县',130600000000,3),
(130635000000,'蠡县',130600000000,3),
(130636000000,'顺平县',130600000000,3),
(130637000000,'博野县',130600000000,3),
(130638000000,'雄县',130600000000,3),
(130671000000,'保定高新技术产业开发区',130600000000,3),
(130672000000,'保定白沟新城',130600000000,3),
(130681000000,'涿州市',130600000000,3),
(130682000000,'定州市',130600000000,3),
(130683000000,'安国市',130600000000,3),
(130684000000,'高碑店市',130600000000,3),
(130700000000,'张家口市',130000000000,2),
(130701000000,'市辖区',130700000000,3),
(130702000000,'桥东区',130700000000,3),
(130703000000,'桥西区',130700000000,3),
(130705000000,'宣化区',130700000000,3),
(130706000000,'下花园区',130700000000,3),
(130708000000,'万全区',130700000000,3),
(130709000000,'崇礼区',130700000000,3),
(130722000000,'张北县',130700000000,3),
(130723000000,'康保县',130700000000,3),
(130724000000,'沽源县',130700000000,3),
(130725000000,'尚义县',130700000000,3),
(130726000000,'蔚县',130700000000,3),
(130727000000,'阳原县',130700000000,3),
(130728000000,'怀安县',130700000000,3),
(130730000000,'怀来县',130700000000,3),
(130731000000,'涿鹿县',130700000000,3),
(130732000000,'赤城县',130700000000,3),
(130771000000,'张家口市高新技术产业开发区',130700000000,3),
(130772000000,'张家口市察北管理区',130700000000,3),
(130773000000,'张家口市塞北管理区',130700000000,3),
(130800000000,'承德市',130000000000,2),
(130801000000,'市辖区',130800000000,3),
(130802000000,'双桥区',130800000000,3),
(130803000000,'双滦区',130800000000,3),
(130804000000,'鹰手营子矿区',130800000000,3),
(130821000000,'承德县',130800000000,3),
(130822000000,'兴隆县',130800000000,3),
(130824000000,'滦平县',130800000000,3),
(130825000000,'隆化县',130800000000,3),
(130826000000,'丰宁满族自治县',130800000000,3),
(130827000000,'宽城满族自治县',130800000000,3),
(130828000000,'围场满族蒙古族自治县',130800000000,3),
(130871000000,'承德高新技术产业开发区',130800000000,3),
(130881000000,'平泉市',130800000000,3),
(130900000000,'沧州市',130000000000,2),
(130901000000,'市辖区',130900000000,3),
(130902000000,'新华区',130900000000,3),
(130903000000,'运河区',130900000000,3),
(130921000000,'沧县',130900000000,3),
(130922000000,'青县',130900000000,3),
(130923000000,'东光县',130900000000,3),
(130924000000,'海兴县',130900000000,3),
(130925000000,'盐山县',130900000000,3),
(130926000000,'肃宁县',130900000000,3),
(130927000000,'南皮县',130900000000,3),
(130928000000,'吴桥县',130900000000,3),
(130929000000,'献县',130900000000,3),
(130930000000,'孟村回族自治县',130900000000,3),
(130971000000,'河北沧州经济开发区',130900000000,3),
(130972000000,'沧州高新技术产业开发区',130900000000,3),
(130973000000,'沧州渤海新区',130900000000,3),
(130981000000,'泊头市',130900000000,3),
(130982000000,'任丘市',130900000000,3),
(130983000000,'黄骅市',130900000000,3),
(130984000000,'河间市',130900000000,3),
(131000000000,'廊坊市',130000000000,2),
(131001000000,'市辖区',131000000000,3),
(131002000000,'安次区',131000000000,3),
(131003000000,'广阳区',131000000000,3),
(131022000000,'固安县',131000000000,3),
(131023000000,'永清县',131000000000,3),
(131024000000,'香河县',131000000000,3),
(131025000000,'大城县',131000000000,3),
(131026000000,'文安县',131000000000,3),
(131028000000,'大厂回族自治县',131000000000,3),
(131071000000,'廊坊经济技术开发区',131000000000,3),
(131081000000,'霸州市',131000000000,3),
(131082000000,'三河市',131000000000,3),
(131100000000,'衡水市',130000000000,2),
(131101000000,'市辖区',131100000000,3),
(131102000000,'桃城区',131100000000,3),
(131103000000,'冀州区',131100000000,3),
(131121000000,'枣强县',131100000000,3),
(131122000000,'武邑县',131100000000,3),
(131123000000,'武强县',131100000000,3),
(131124000000,'饶阳县',131100000000,3),
(131125000000,'安平县',131100000000,3),
(131126000000,'故城县',131100000000,3),
(131127000000,'景县',131100000000,3),
(131128000000,'阜城县',131100000000,3),
(131171000000,'河北衡水高新技术产业开发区',131100000000,3),
(131172000000,'衡水滨湖新区',131100000000,3),
(131182000000,'深州市',131100000000,3),
(140000000000,'山西省',0,1),
(140100000000,'太原市',140000000000,2),
(140101000000,'市辖区',140100000000,3),
(140105000000,'小店区',140100000000,3),
(140106000000,'迎泽区',140100000000,3),
(140107000000,'杏花岭区',140100000000,3),
(140108000000,'尖草坪区',140100000000,3),
(140109000000,'万柏林区',140100000000,3),
(140110000000,'晋源区',140100000000,3),
(140121000000,'清徐县',140100000000,3),
(140122000000,'阳曲县',140100000000,3),
(140123000000,'娄烦县',140100000000,3),
(140171000000,'山西转型综合改革示范区',140100000000,3),
(140181000000,'古交市',140100000000,3),
(140200000000,'大同市',140000000000,2),
(140201000000,'市辖区',140200000000,3),
(140212000000,'新荣区',140200000000,3),
(140213000000,'平城区',140200000000,3),
(140214000000,'云冈区',140200000000,3),
(140215000000,'云州区',140200000000,3),
(140221000000,'阳高县',140200000000,3),
(140222000000,'天镇县',140200000000,3),
(140223000000,'广灵县',140200000000,3),
(140224000000,'灵丘县',140200000000,3),
(140225000000,'浑源县',140200000000,3),
(140226000000,'左云县',140200000000,3),
(140271000000,'山西大同经济开发区',140200000000,3),
(140300000000,'阳泉市',140000000000,2),
(140301000000,'市辖区',140300000000,3),
(140302000000,'城区',140300000000,3),
(140303000000,'矿区',140300000000,3),
(140311000000,'郊区',140300000000,3),
(140321000000,'平定县',140300000000,3),
(140322000000,'盂县',140300000000,3),
(140400000000,'长治市',140000000000,2),
(140401000000,'市辖区',140400000000,3),
(140412000000,'潞州区',140400000000,3),
(140413000000,'上党区',140400000000,3),
(140414000000,'屯留区',140400000000,3),
(140415000000,'潞城区',140400000000,3),
(140423000000,'襄垣县',140400000000,3),
(140425000000,'平顺县',140400000000,3),
(140426000000,'黎城县',140400000000,3),
(140427000000,'壶关县',140400000000,3),
(140428000000,'长子县',140400000000,3),
(140429000000,'武乡县',140400000000,3),
(140430000000,'沁县',140400000000,3),
(140431000000,'沁源县',140400000000,3),
(140471000000,'山西长治高新技术产业园区',140400000000,3),
(140500000000,'晋城市',140000000000,2),
(140501000000,'市辖区',140500000000,3),
(140502000000,'城区',140500000000,3),
(140521000000,'沁水县',140500000000,3),
(140522000000,'阳城县',140500000000,3),
(140524000000,'陵川县',140500000000,3),
(140525000000,'泽州县',140500000000,3),
(140581000000,'高平市',140500000000,3),
(140600000000,'朔州市',140000000000,2),
(140601000000,'市辖区',140600000000,3),
(140602000000,'朔城区',140600000000,3),
(140603000000,'平鲁区',140600000000,3),
(140621000000,'山阴县',140600000000,3),
(140622000000,'应县',140600000000,3),
(140623000000,'右玉县',140600000000,3),
(140671000000,'山西朔州经济开发区',140600000000,3),
(140681000000,'怀仁市',140600000000,3),
(140700000000,'晋中市',140000000000,2),
(140701000000,'市辖区',140700000000,3),
(140702000000,'榆次区',140700000000,3),
(140721000000,'榆社县',140700000000,3),
(140722000000,'左权县',140700000000,3),
(140723000000,'和顺县',140700000000,3),
(140724000000,'昔阳县',140700000000,3),
(140725000000,'寿阳县',140700000000,3),
(140726000000,'太谷县',140700000000,3),
(140727000000,'祁县',140700000000,3),
(140728000000,'平遥县',140700000000,3),
(140729000000,'灵石县',140700000000,3),
(140781000000,'介休市',140700000000,3),
(140800000000,'运城市',140000000000,2),
(140801000000,'市辖区',140800000000,3),
(140802000000,'盐湖区',140800000000,3),
(140821000000,'临猗县',140800000000,3),
(140822000000,'万荣县',140800000000,3),
(140823000000,'闻喜县',140800000000,3),
(140824000000,'稷山县',140800000000,3),
(140825000000,'新绛县',140800000000,3),
(140826000000,'绛县',140800000000,3),
(140827000000,'垣曲县',140800000000,3),
(140828000000,'夏县',140800000000,3),
(140829000000,'平陆县',140800000000,3),
(140830000000,'芮城县',140800000000,3),
(140881000000,'永济市',140800000000,3),
(140882000000,'河津市',140800000000,3),
(140900000000,'忻州市',140000000000,2),
(140901000000,'市辖区',140900000000,3),
(140902000000,'忻府区',140900000000,3),
(140921000000,'定襄县',140900000000,3),
(140922000000,'五台县',140900000000,3),
(140923000000,'代县',140900000000,3),
(140924000000,'繁峙县',140900000000,3),
(140925000000,'宁武县',140900000000,3),
(140926000000,'静乐县',140900000000,3),
(140927000000,'神池县',140900000000,3),
(140928000000,'五寨县',140900000000,3),
(140929000000,'岢岚县',140900000000,3),
(140930000000,'河曲县',140900000000,3),
(140931000000,'保德县',140900000000,3),
(140932000000,'偏关县',140900000000,3),
(140971000000,'五台山风景名胜区',140900000000,3),
(140981000000,'原平市',140900000000,3),
(141000000000,'临汾市',140000000000,2),
(141001000000,'市辖区',141000000000,3),
(141002000000,'尧都区',141000000000,3),
(141021000000,'曲沃县',141000000000,3),
(141022000000,'翼城县',141000000000,3),
(141023000000,'襄汾县',141000000000,3),
(141024000000,'洪洞县',141000000000,3),
(141025000000,'古县',141000000000,3),
(141026000000,'安泽县',141000000000,3),
(141027000000,'浮山县',141000000000,3),
(141028000000,'吉县',141000000000,3),
(141029000000,'乡宁县',141000000000,3),
(141030000000,'大宁县',141000000000,3),
(141031000000,'隰县',141000000000,3),
(141032000000,'永和县',141000000000,3),
(141033000000,'蒲县',141000000000,3),
(141034000000,'汾西县',141000000000,3),
(141081000000,'侯马市',141000000000,3),
(141082000000,'霍州市',141000000000,3),
(141100000000,'吕梁市',140000000000,2),
(141101000000,'市辖区',141100000000,3),
(141102000000,'离石区',141100000000,3),
(141121000000,'文水县',141100000000,3),
(141122000000,'交城县',141100000000,3),
(141123000000,'兴县',141100000000,3),
(141124000000,'临县',141100000000,3),
(141125000000,'柳林县',141100000000,3),
(141126000000,'石楼县',141100000000,3),
(141127000000,'岚县',141100000000,3),
(141128000000,'方山县',141100000000,3),
(141129000000,'中阳县',141100000000,3),
(141130000000,'交口县',141100000000,3),
(141181000000,'孝义市',141100000000,3),
(141182000000,'汾阳市',141100000000,3),
(150000000000,'内蒙古自治区',0,1),
(150100000000,'呼和浩特市',150000000000,2),
(150101000000,'市辖区',150100000000,3),
(150102000000,'新城区',150100000000,3),
(150103000000,'回民区',150100000000,3),
(150104000000,'玉泉区',150100000000,3),
(150105000000,'赛罕区',150100000000,3),
(150121000000,'土默特左旗',150100000000,3),
(150122000000,'托克托县',150100000000,3),
(150123000000,'和林格尔县',150100000000,3),
(150124000000,'清水河县',150100000000,3),
(150125000000,'武川县',150100000000,3),
(150171000000,'呼和浩特金海工业园区',150100000000,3),
(150172000000,'呼和浩特经济技术开发区',150100000000,3),
(150200000000,'包头市',150000000000,2),
(150201000000,'市辖区',150200000000,3),
(150202000000,'东河区',150200000000,3),
(150203000000,'昆都仑区',150200000000,3),
(150204000000,'青山区',150200000000,3),
(150205000000,'石拐区',150200000000,3),
(150206000000,'白云鄂博矿区',150200000000,3),
(150207000000,'九原区',150200000000,3),
(150221000000,'土默特右旗',150200000000,3),
(150222000000,'固阳县',150200000000,3),
(150223000000,'达尔罕茂明安联合旗',150200000000,3),
(150271000000,'包头稀土高新技术产业开发区',150200000000,3),
(150300000000,'乌海市',150000000000,2),
(150301000000,'市辖区',150300000000,3),
(150302000000,'海勃湾区',150300000000,3),
(150303000000,'海南区',150300000000,3),
(150304000000,'乌达区',150300000000,3),
(150400000000,'赤峰市',150000000000,2),
(150401000000,'市辖区',150400000000,3),
(150402000000,'红山区',150400000000,3),
(150403000000,'元宝山区',150400000000,3),
(150404000000,'松山区',150400000000,3),
(150421000000,'阿鲁科尔沁旗',150400000000,3),
(150422000000,'巴林左旗',150400000000,3),
(150423000000,'巴林右旗',150400000000,3),
(150424000000,'林西县',150400000000,3),
(150425000000,'克什克腾旗',150400000000,3),
(150426000000,'翁牛特旗',150400000000,3),
(150428000000,'喀喇沁旗',150400000000,3),
(150429000000,'宁城县',150400000000,3),
(150430000000,'敖汉旗',150400000000,3),
(150500000000,'通辽市',150000000000,2),
(150501000000,'市辖区',150500000000,3),
(150502000000,'科尔沁区',150500000000,3),
(150521000000,'科尔沁左翼中旗',150500000000,3),
(150522000000,'科尔沁左翼后旗',150500000000,3),
(150523000000,'开鲁县',150500000000,3),
(150524000000,'库伦旗',150500000000,3),
(150525000000,'奈曼旗',150500000000,3),
(150526000000,'扎鲁特旗',150500000000,3),
(150571000000,'通辽经济技术开发区',150500000000,3),
(150581000000,'霍林郭勒市',150500000000,3),
(150600000000,'鄂尔多斯市',150000000000,2),
(150601000000,'市辖区',150600000000,3),
(150602000000,'东胜区',150600000000,3),
(150603000000,'康巴什区',150600000000,3),
(150621000000,'达拉特旗',150600000000,3),
(150622000000,'准格尔旗',150600000000,3),
(150623000000,'鄂托克前旗',150600000000,3),
(150624000000,'鄂托克旗',150600000000,3),
(150625000000,'杭锦旗',150600000000,3),
(150626000000,'乌审旗',150600000000,3),
(150627000000,'伊金霍洛旗',150600000000,3),
(150700000000,'呼伦贝尔市',150000000000,2),
(150701000000,'市辖区',150700000000,3),
(150702000000,'海拉尔区',150700000000,3),
(150703000000,'扎赉诺尔区',150700000000,3),
(150721000000,'阿荣旗',150700000000,3),
(150722000000,'莫力达瓦达斡尔族自治旗',150700000000,3),
(150723000000,'鄂伦春自治旗',150700000000,3),
(150724000000,'鄂温克族自治旗',150700000000,3),
(150725000000,'陈巴尔虎旗',150700000000,3),
(150726000000,'新巴尔虎左旗',150700000000,3),
(150727000000,'新巴尔虎右旗',150700000000,3),
(150781000000,'满洲里市',150700000000,3),
(150782000000,'牙克石市',150700000000,3),
(150783000000,'扎兰屯市',150700000000,3),
(150784000000,'额尔古纳市',150700000000,3),
(150785000000,'根河市',150700000000,3),
(150800000000,'巴彦淖尔市',150000000000,2),
(150801000000,'市辖区',150800000000,3),
(150802000000,'临河区',150800000000,3),
(150821000000,'五原县',150800000000,3),
(150822000000,'磴口县',150800000000,3),
(150823000000,'乌拉特前旗',150800000000,3),
(150824000000,'乌拉特中旗',150800000000,3),
(150825000000,'乌拉特后旗',150800000000,3),
(150826000000,'杭锦后旗',150800000000,3),
(150900000000,'乌兰察布市',150000000000,2),
(150901000000,'市辖区',150900000000,3),
(150902000000,'集宁区',150900000000,3),
(150921000000,'卓资县',150900000000,3),
(150922000000,'化德县',150900000000,3),
(150923000000,'商都县',150900000000,3),
(150924000000,'兴和县',150900000000,3),
(150925000000,'凉城县',150900000000,3),
(150926000000,'察哈尔右翼前旗',150900000000,3),
(150927000000,'察哈尔右翼中旗',150900000000,3),
(150928000000,'察哈尔右翼后旗',150900000000,3),
(150929000000,'四子王旗',150900000000,3),
(150981000000,'丰镇市',150900000000,3),
(152200000000,'兴安盟',150000000000,2),
(152201000000,'乌兰浩特市',152200000000,3),
(152202000000,'阿尔山市',152200000000,3),
(152221000000,'科尔沁右翼前旗',152200000000,3),
(152222000000,'科尔沁右翼中旗',152200000000,3),
(152223000000,'扎赉特旗',152200000000,3),
(152224000000,'突泉县',152200000000,3),
(152500000000,'锡林郭勒盟',150000000000,2),
(152501000000,'二连浩特市',152500000000,3),
(152502000000,'锡林浩特市',152500000000,3),
(152522000000,'阿巴嘎旗',152500000000,3),
(152523000000,'苏尼特左旗',152500000000,3),
(152524000000,'苏尼特右旗',152500000000,3),
(152525000000,'东乌珠穆沁旗',152500000000,3),
(152526000000,'西乌珠穆沁旗',152500000000,3),
(152527000000,'太仆寺旗',152500000000,3),
(152528000000,'镶黄旗',152500000000,3),
(152529000000,'正镶白旗',152500000000,3),
(152530000000,'正蓝旗',152500000000,3),
(152531000000,'多伦县',152500000000,3),
(152571000000,'乌拉盖管委会',152500000000,3),
(152900000000,'阿拉善盟',150000000000,2),
(152921000000,'阿拉善左旗',152900000000,3),
(152922000000,'阿拉善右旗',152900000000,3),
(152923000000,'额济纳旗',152900000000,3),
(152971000000,'内蒙古阿拉善经济开发区',152900000000,3),
(210000000000,'辽宁省',0,1),
(210100000000,'沈阳市',210000000000,2),
(210101000000,'市辖区',210100000000,3),
(210102000000,'和平区',210100000000,3),
(210103000000,'沈河区',210100000000,3),
(210104000000,'大东区',210100000000,3),
(210105000000,'皇姑区',210100000000,3),
(210106000000,'铁西区',210100000000,3),
(210111000000,'苏家屯区',210100000000,3),
(210112000000,'浑南区',210100000000,3),
(210113000000,'沈北新区',210100000000,3),
(210114000000,'于洪区',210100000000,3),
(210115000000,'辽中区',210100000000,3),
(210123000000,'康平县',210100000000,3),
(210124000000,'法库县',210100000000,3),
(210181000000,'新民市',210100000000,3),
(210200000000,'大连市',210000000000,2),
(210201000000,'市辖区',210200000000,3),
(210202000000,'中山区',210200000000,3),
(210203000000,'西岗区',210200000000,3),
(210204000000,'沙河口区',210200000000,3),
(210211000000,'甘井子区',210200000000,3),
(210212000000,'旅顺口区',210200000000,3),
(210213000000,'金州区',210200000000,3),
(210214000000,'普兰店区',210200000000,3),
(210224000000,'长海县',210200000000,3),
(210281000000,'瓦房店市',210200000000,3),
(210283000000,'庄河市',210200000000,3),
(210300000000,'鞍山市',210000000000,2),
(210301000000,'市辖区',210300000000,3),
(210302000000,'铁东区',210300000000,3),
(210303000000,'铁西区',210300000000,3),
(210304000000,'立山区',210300000000,3),
(210311000000,'千山区',210300000000,3),
(210321000000,'台安县',210300000000,3),
(210323000000,'岫岩满族自治县',210300000000,3),
(210381000000,'海城市',210300000000,3),
(210400000000,'抚顺市',210000000000,2),
(210401000000,'市辖区',210400000000,3),
(210402000000,'新抚区',210400000000,3),
(210403000000,'东洲区',210400000000,3),
(210404000000,'望花区',210400000000,3),
(210411000000,'顺城区',210400000000,3),
(210421000000,'抚顺县',210400000000,3),
(210422000000,'新宾满族自治县',210400000000,3),
(210423000000,'清原满族自治县',210400000000,3),
(210500000000,'本溪市',210000000000,2),
(210501000000,'市辖区',210500000000,3),
(210502000000,'平山区',210500000000,3),
(210503000000,'溪湖区',210500000000,3),
(210504000000,'明山区',210500000000,3),
(210505000000,'南芬区',210500000000,3),
(210521000000,'本溪满族自治县',210500000000,3),
(210522000000,'桓仁满族自治县',210500000000,3),
(210600000000,'丹东市',210000000000,2),
(210601000000,'市辖区',210600000000,3),
(210602000000,'元宝区',210600000000,3),
(210603000000,'振兴区',210600000000,3),
(210604000000,'振安区',210600000000,3),
(210624000000,'宽甸满族自治县',210600000000,3),
(210681000000,'东港市',210600000000,3),
(210682000000,'凤城市',210600000000,3),
(210700000000,'锦州市',210000000000,2),
(210701000000,'市辖区',210700000000,3),
(210702000000,'古塔区',210700000000,3),
(210703000000,'凌河区',210700000000,3),
(210711000000,'太和区',210700000000,3),
(210726000000,'黑山县',210700000000,3),
(210727000000,'义县',210700000000,3),
(210781000000,'凌海市',210700000000,3),
(210782000000,'北镇市',210700000000,3),
(210800000000,'营口市',210000000000,2),
(210801000000,'市辖区',210800000000,3),
(210802000000,'站前区',210800000000,3),
(210803000000,'西市区',210800000000,3),
(210804000000,'鲅鱼圈区',210800000000,3),
(210811000000,'老边区',210800000000,3),
(210881000000,'盖州市',210800000000,3),
(210882000000,'大石桥市',210800000000,3),
(210900000000,'阜新市',210000000000,2),
(210901000000,'市辖区',210900000000,3),
(210902000000,'海州区',210900000000,3),
(210903000000,'新邱区',210900000000,3),
(210904000000,'太平区',210900000000,3),
(210905000000,'清河门区',210900000000,3),
(210911000000,'细河区',210900000000,3),
(210921000000,'阜新蒙古族自治县',210900000000,3),
(210922000000,'彰武县',210900000000,3),
(211000000000,'辽阳市',210000000000,2),
(211001000000,'市辖区',211000000000,3),
(211002000000,'白塔区',211000000000,3),
(211003000000,'文圣区',211000000000,3),
(211004000000,'宏伟区',211000000000,3),
(211005000000,'弓长岭区',211000000000,3),
(211011000000,'太子河区',211000000000,3),
(211021000000,'辽阳县',211000000000,3),
(211081000000,'灯塔市',211000000000,3),
(211100000000,'盘锦市',210000000000,2),
(211101000000,'市辖区',211100000000,3),
(211102000000,'双台子区',211100000000,3),
(211103000000,'兴隆台区',211100000000,3),
(211104000000,'大洼区',211100000000,3),
(211122000000,'盘山县',211100000000,3),
(211200000000,'铁岭市',210000000000,2),
(211201000000,'市辖区',211200000000,3),
(211202000000,'银州区',211200000000,3),
(211204000000,'清河区',211200000000,3),
(211221000000,'铁岭县',211200000000,3),
(211223000000,'西丰县',211200000000,3),
(211224000000,'昌图县',211200000000,3),
(211281000000,'调兵山市',211200000000,3),
(211282000000,'开原市',211200000000,3),
(211300000000,'朝阳市',210000000000,2),
(211301000000,'市辖区',211300000000,3),
(211302000000,'双塔区',211300000000,3),
(211303000000,'龙城区',211300000000,3),
(211321000000,'朝阳县',211300000000,3),
(211322000000,'建平县',211300000000,3),
(211324000000,'喀喇沁左翼蒙古族自治县',211300000000,3),
(211381000000,'北票市',211300000000,3),
(211382000000,'凌源市',211300000000,3),
(211400000000,'葫芦岛市',210000000000,2),
(211401000000,'市辖区',211400000000,3),
(211402000000,'连山区',211400000000,3),
(211403000000,'龙港区',211400000000,3),
(211404000000,'南票区',211400000000,3),
(211421000000,'绥中县',211400000000,3),
(211422000000,'建昌县',211400000000,3),
(211481000000,'兴城市',211400000000,3),
(220000000000,'吉林省',0,1),
(220100000000,'长春市',220000000000,2),
(220101000000,'市辖区',220100000000,3),
(220102000000,'南关区',220100000000,3),
(220103000000,'宽城区',220100000000,3),
(220104000000,'朝阳区',220100000000,3),
(220105000000,'二道区',220100000000,3),
(220106000000,'绿园区',220100000000,3),
(220112000000,'双阳区',220100000000,3),
(220113000000,'九台区',220100000000,3),
(220122000000,'农安县',220100000000,3),
(220171000000,'长春经济技术开发区',220100000000,3),
(220172000000,'长春净月高新技术产业开发区',220100000000,3),
(220173000000,'长春高新技术产业开发区',220100000000,3),
(220174000000,'长春汽车经济技术开发区',220100000000,3),
(220182000000,'榆树市',220100000000,3),
(220183000000,'德惠市',220100000000,3),
(220200000000,'吉林市',220000000000,2),
(220201000000,'市辖区',220200000000,3),
(220202000000,'昌邑区',220200000000,3),
(220203000000,'龙潭区',220200000000,3),
(220204000000,'船营区',220200000000,3),
(220211000000,'丰满区',220200000000,3),
(220221000000,'永吉县',220200000000,3),
(220271000000,'吉林经济开发区',220200000000,3),
(220272000000,'吉林高新技术产业开发区',220200000000,3),
(220273000000,'吉林中国新加坡食品区',220200000000,3),
(220281000000,'蛟河市',220200000000,3),
(220282000000,'桦甸市',220200000000,3),
(220283000000,'舒兰市',220200000000,3),
(220284000000,'磐石市',220200000000,3),
(220300000000,'四平市',220000000000,2),
(220301000000,'市辖区',220300000000,3),
(220302000000,'铁西区',220300000000,3),
(220303000000,'铁东区',220300000000,3),
(220322000000,'梨树县',220300000000,3),
(220323000000,'伊通满族自治县',220300000000,3),
(220381000000,'公主岭市',220300000000,3),
(220382000000,'双辽市',220300000000,3),
(220400000000,'辽源市',220000000000,2),
(220401000000,'市辖区',220400000000,3),
(220402000000,'龙山区',220400000000,3),
(220403000000,'西安区',220400000000,3),
(220421000000,'东丰县',220400000000,3),
(220422000000,'东辽县',220400000000,3),
(220500000000,'通化市',220000000000,2),
(220501000000,'市辖区',220500000000,3),
(220502000000,'东昌区',220500000000,3),
(220503000000,'二道江区',220500000000,3),
(220521000000,'通化县',220500000000,3),
(220523000000,'辉南县',220500000000,3),
(220524000000,'柳河县',220500000000,3),
(220581000000,'梅河口市',220500000000,3),
(220582000000,'集安市',220500000000,3),
(220600000000,'白山市',220000000000,2),
(220601000000,'市辖区',220600000000,3),
(220602000000,'浑江区',220600000000,3),
(220605000000,'江源区',220600000000,3),
(220621000000,'抚松县',220600000000,3),
(220622000000,'靖宇县',220600000000,3),
(220623000000,'长白朝鲜族自治县',220600000000,3),
(220681000000,'临江市',220600000000,3),
(220700000000,'松原市',220000000000,2),
(220701000000,'市辖区',220700000000,3),
(220702000000,'宁江区',220700000000,3),
(220721000000,'前郭尔罗斯蒙古族自治县',220700000000,3),
(220722000000,'长岭县',220700000000,3),
(220723000000,'乾安县',220700000000,3),
(220771000000,'吉林松原经济开发区',220700000000,3),
(220781000000,'扶余市',220700000000,3),
(220800000000,'白城市',220000000000,2),
(220801000000,'市辖区',220800000000,3),
(220802000000,'洮北区',220800000000,3),
(220821000000,'镇赉县',220800000000,3),
(220822000000,'通榆县',220800000000,3),
(220871000000,'吉林白城经济开发区',220800000000,3),
(220881000000,'洮南市',220800000000,3),
(220882000000,'大安市',220800000000,3),
(222400000000,'延边朝鲜族自治州',220000000000,2),
(222401000000,'延吉市',222400000000,3),
(222402000000,'图们市',222400000000,3),
(222403000000,'敦化市',222400000000,3),
(222404000000,'珲春市',222400000000,3),
(222405000000,'龙井市',222400000000,3),
(222406000000,'和龙市',222400000000,3),
(222424000000,'汪清县',222400000000,3),
(222426000000,'安图县',222400000000,3),
(230000000000,'黑龙江省',0,1),
(230100000000,'哈尔滨市',230000000000,2),
(230101000000,'市辖区',230100000000,3),
(230102000000,'道里区',230100000000,3),
(230103000000,'南岗区',230100000000,3),
(230104000000,'道外区',230100000000,3),
(230108000000,'平房区',230100000000,3),
(230109000000,'松北区',230100000000,3),
(230110000000,'香坊区',230100000000,3),
(230111000000,'呼兰区',230100000000,3),
(230112000000,'阿城区',230100000000,3),
(230113000000,'双城区',230100000000,3),
(230123000000,'依兰县',230100000000,3),
(230124000000,'方正县',230100000000,3),
(230125000000,'宾县',230100000000,3),
(230126000000,'巴彦县',230100000000,3),
(230127000000,'木兰县',230100000000,3),
(230128000000,'通河县',230100000000,3),
(230129000000,'延寿县',230100000000,3),
(230183000000,'尚志市',230100000000,3),
(230184000000,'五常市',230100000000,3),
(230200000000,'齐齐哈尔市',230000000000,2),
(230201000000,'市辖区',230200000000,3),
(230202000000,'龙沙区',230200000000,3),
(230203000000,'建华区',230200000000,3),
(230204000000,'铁锋区',230200000000,3),
(230205000000,'昂昂溪区',230200000000,3),
(230206000000,'富拉尔基区',230200000000,3),
(230207000000,'碾子山区',230200000000,3),
(230208000000,'梅里斯达斡尔族区',230200000000,3),
(230221000000,'龙江县',230200000000,3),
(230223000000,'依安县',230200000000,3),
(230224000000,'泰来县',230200000000,3),
(230225000000,'甘南县',230200000000,3),
(230227000000,'富裕县',230200000000,3),
(230229000000,'克山县',230200000000,3),
(230230000000,'克东县',230200000000,3),
(230231000000,'拜泉县',230200000000,3),
(230281000000,'讷河市',230200000000,3),
(230300000000,'鸡西市',230000000000,2),
(230301000000,'市辖区',230300000000,3),
(230302000000,'鸡冠区',230300000000,3),
(230303000000,'恒山区',230300000000,3),
(230304000000,'滴道区',230300000000,3),
(230305000000,'梨树区',230300000000,3),
(230306000000,'城子河区',230300000000,3),
(230307000000,'麻山区',230300000000,3),
(230321000000,'鸡东县',230300000000,3),
(230381000000,'虎林市',230300000000,3),
(230382000000,'密山市',230300000000,3),
(230400000000,'鹤岗市',230000000000,2),
(230401000000,'市辖区',230400000000,3),
(230402000000,'向阳区',230400000000,3),
(230403000000,'工农区',230400000000,3),
(230404000000,'南山区',230400000000,3),
(230405000000,'兴安区',230400000000,3),
(230406000000,'东山区',230400000000,3),
(230407000000,'兴山区',230400000000,3),
(230421000000,'萝北县',230400000000,3),
(230422000000,'绥滨县',230400000000,3),
(230500000000,'双鸭山市',230000000000,2),
(230501000000,'市辖区',230500000000,3),
(230502000000,'尖山区',230500000000,3),
(230503000000,'岭东区',230500000000,3),
(230505000000,'四方台区',230500000000,3),
(230506000000,'宝山区',230500000000,3),
(230521000000,'集贤县',230500000000,3),
(230522000000,'友谊县',230500000000,3),
(230523000000,'宝清县',230500000000,3),
(230524000000,'饶河县',230500000000,3),
(230600000000,'大庆市',230000000000,2),
(230601000000,'市辖区',230600000000,3),
(230602000000,'萨尔图区',230600000000,3),
(230603000000,'龙凤区',230600000000,3),
(230604000000,'让胡路区',230600000000,3),
(230605000000,'红岗区',230600000000,3),
(230606000000,'大同区',230600000000,3),
(230621000000,'肇州县',230600000000,3),
(230622000000,'肇源县',230600000000,3),
(230623000000,'林甸县',230600000000,3),
(230624000000,'杜尔伯特蒙古族自治县',230600000000,3),
(230671000000,'大庆高新技术产业开发区',230600000000,3),
(230700000000,'伊春市',230000000000,2),
(230701000000,'市辖区',230700000000,3),
(230702000000,'伊春区',230700000000,3),
(230703000000,'南岔区',230700000000,3),
(230704000000,'友好区',230700000000,3),
(230705000000,'西林区',230700000000,3),
(230706000000,'翠峦区',230700000000,3),
(230707000000,'新青区',230700000000,3),
(230708000000,'美溪区',230700000000,3),
(230709000000,'金山屯区',230700000000,3),
(230710000000,'五营区',230700000000,3),
(230711000000,'乌马河区',230700000000,3),
(230712000000,'汤旺河区',230700000000,3),
(230713000000,'带岭区',230700000000,3),
(230714000000,'乌伊岭区',230700000000,3),
(230715000000,'红星区',230700000000,3),
(230716000000,'上甘岭区',230700000000,3),
(230722000000,'嘉荫县',230700000000,3),
(230781000000,'铁力市',230700000000,3),
(230800000000,'佳木斯市',230000000000,2),
(230801000000,'市辖区',230800000000,3),
(230803000000,'向阳区',230800000000,3),
(230804000000,'前进区',230800000000,3),
(230805000000,'东风区',230800000000,3),
(230811000000,'郊区',230800000000,3),
(230822000000,'桦南县',230800000000,3),
(230826000000,'桦川县',230800000000,3),
(230828000000,'汤原县',230800000000,3),
(230881000000,'同江市',230800000000,3),
(230882000000,'富锦市',230800000000,3),
(230883000000,'抚远市',230800000000,3),
(230900000000,'七台河市',230000000000,2),
(230901000000,'市辖区',230900000000,3),
(230902000000,'新兴区',230900000000,3),
(230903000000,'桃山区',230900000000,3),
(230904000000,'茄子河区',230900000000,3),
(230921000000,'勃利县',230900000000,3),
(231000000000,'牡丹江市',230000000000,2),
(231001000000,'市辖区',231000000000,3),
(231002000000,'东安区',231000000000,3),
(231003000000,'阳明区',231000000000,3),
(231004000000,'爱民区',231000000000,3),
(231005000000,'西安区',231000000000,3),
(231025000000,'林口县',231000000000,3),
(231071000000,'牡丹江经济技术开发区',231000000000,3),
(231081000000,'绥芬河市',231000000000,3),
(231083000000,'海林市',231000000000,3),
(231084000000,'宁安市',231000000000,3),
(231085000000,'穆棱市',231000000000,3),
(231086000000,'东宁市',231000000000,3),
(231100000000,'黑河市',230000000000,2),
(231101000000,'市辖区',231100000000,3),
(231102000000,'爱辉区',231100000000,3),
(231121000000,'嫩江县',231100000000,3),
(231123000000,'逊克县',231100000000,3),
(231124000000,'孙吴县',231100000000,3),
(231181000000,'北安市',231100000000,3),
(231182000000,'五大连池市',231100000000,3),
(231200000000,'绥化市',230000000000,2),
(231201000000,'市辖区',231200000000,3),
(231202000000,'北林区',231200000000,3),
(231221000000,'望奎县',231200000000,3),
(231222000000,'兰西县',231200000000,3),
(231223000000,'青冈县',231200000000,3),
(231224000000,'庆安县',231200000000,3),
(231225000000,'明水县',231200000000,3),
(231226000000,'绥棱县',231200000000,3),
(231281000000,'安达市',231200000000,3),
(231282000000,'肇东市',231200000000,3),
(231283000000,'海伦市',231200000000,3),
(232700000000,'大兴安岭地区',230000000000,2),
(232701000000,'漠河市',232700000000,3),
(232721000000,'呼玛县',232700000000,3),
(232722000000,'塔河县',232700000000,3),
(232761000000,'加格达奇区',232700000000,3),
(232762000000,'松岭区',232700000000,3),
(232763000000,'新林区',232700000000,3),
(232764000000,'呼中区',232700000000,3),
(310000000000,'上海市',0,1),
(310100000000,'市辖区',310000000000,2),
(310101000000,'黄浦区',310100000000,3),
(310104000000,'徐汇区',310100000000,3),
(310105000000,'长宁区',310100000000,3),
(310106000000,'静安区',310100000000,3),
(310107000000,'普陀区',310100000000,3),
(310109000000,'虹口区',310100000000,3),
(310110000000,'杨浦区',310100000000,3),
(310112000000,'闵行区',310100000000,3),
(310113000000,'宝山区',310100000000,3),
(310114000000,'嘉定区',310100000000,3),
(310115000000,'浦东新区',310100000000,3),
(310116000000,'金山区',310100000000,3),
(310117000000,'松江区',310100000000,3),
(310118000000,'青浦区',310100000000,3),
(310120000000,'奉贤区',310100000000,3),
(310151000000,'崇明区',310100000000,3),
(320000000000,'江苏省',0,1),
(320100000000,'南京市',320000000000,2),
(320101000000,'市辖区',320100000000,3),
(320102000000,'玄武区',320100000000,3),
(320104000000,'秦淮区',320100000000,3),
(320105000000,'建邺区',320100000000,3),
(320106000000,'鼓楼区',320100000000,3),
(320111000000,'浦口区',320100000000,3),
(320113000000,'栖霞区',320100000000,3),
(320114000000,'雨花台区',320100000000,3),
(320115000000,'江宁区',320100000000,3),
(320116000000,'六合区',320100000000,3),
(320117000000,'溧水区',320100000000,3),
(320118000000,'高淳区',320100000000,3),
(320200000000,'无锡市',320000000000,2),
(320201000000,'市辖区',320200000000,3),
(320205000000,'锡山区',320200000000,3),
(320206000000,'惠山区',320200000000,3),
(320211000000,'滨湖区',320200000000,3),
(320213000000,'梁溪区',320200000000,3),
(320214000000,'新吴区',320200000000,3),
(320281000000,'江阴市',320200000000,3),
(320282000000,'宜兴市',320200000000,3),
(320300000000,'徐州市',320000000000,2),
(320301000000,'市辖区',320300000000,3),
(320302000000,'鼓楼区',320300000000,3),
(320303000000,'云龙区',320300000000,3),
(320305000000,'贾汪区',320300000000,3),
(320311000000,'泉山区',320300000000,3),
(320312000000,'铜山区',320300000000,3),
(320321000000,'丰县',320300000000,3),
(320322000000,'沛县',320300000000,3),
(320324000000,'睢宁县',320300000000,3),
(320371000000,'徐州经济技术开发区',320300000000,3),
(320381000000,'新沂市',320300000000,3),
(320382000000,'邳州市',320300000000,3),
(320400000000,'常州市',320000000000,2),
(320401000000,'市辖区',320400000000,3),
(320402000000,'天宁区',320400000000,3),
(320404000000,'钟楼区',320400000000,3),
(320411000000,'新北区',320400000000,3),
(320412000000,'武进区',320400000000,3),
(320413000000,'金坛区',320400000000,3),
(320481000000,'溧阳市',320400000000,3),
(320500000000,'苏州市',320000000000,2),
(320501000000,'市辖区',320500000000,3),
(320505000000,'虎丘区',320500000000,3),
(320506000000,'吴中区',320500000000,3),
(320507000000,'相城区',320500000000,3),
(320508000000,'姑苏区',320500000000,3),
(320509000000,'吴江区',320500000000,3),
(320571000000,'苏州工业园区',320500000000,3),
(320581000000,'常熟市',320500000000,3),
(320582000000,'张家港市',320500000000,3),
(320583000000,'昆山市',320500000000,3),
(320585000000,'太仓市',320500000000,3),
(320600000000,'南通市',320000000000,2),
(320601000000,'市辖区',320600000000,3),
(320602000000,'崇川区',320600000000,3),
(320611000000,'港闸区',320600000000,3),
(320612000000,'通州区',320600000000,3),
(320623000000,'如东县',320600000000,3),
(320671000000,'南通经济技术开发区',320600000000,3),
(320681000000,'启东市',320600000000,3),
(320682000000,'如皋市',320600000000,3),
(320684000000,'海门市',320600000000,3),
(320685000000,'海安市',320600000000,3),
(320700000000,'连云港市',320000000000,2),
(320701000000,'市辖区',320700000000,3),
(320703000000,'连云区',320700000000,3),
(320706000000,'海州区',320700000000,3),
(320707000000,'赣榆区',320700000000,3),
(320722000000,'东海县',320700000000,3),
(320723000000,'灌云县',320700000000,3),
(320724000000,'灌南县',320700000000,3),
(320771000000,'连云港经济技术开发区',320700000000,3),
(320772000000,'连云港高新技术产业开发区',320700000000,3),
(320800000000,'淮安市',320000000000,2),
(320801000000,'市辖区',320800000000,3),
(320803000000,'淮安区',320800000000,3),
(320804000000,'淮阴区',320800000000,3),
(320812000000,'清江浦区',320800000000,3),
(320813000000,'洪泽区',320800000000,3),
(320826000000,'涟水县',320800000000,3),
(320830000000,'盱眙县',320800000000,3),
(320831000000,'金湖县',320800000000,3),
(320871000000,'淮安经济技术开发区',320800000000,3),
(320900000000,'盐城市',320000000000,2),
(320901000000,'市辖区',320900000000,3),
(320902000000,'亭湖区',320900000000,3),
(320903000000,'盐都区',320900000000,3),
(320904000000,'大丰区',320900000000,3),
(320921000000,'响水县',320900000000,3),
(320922000000,'滨海县',320900000000,3),
(320923000000,'阜宁县',320900000000,3),
(320924000000,'射阳县',320900000000,3),
(320925000000,'建湖县',320900000000,3),
(320971000000,'盐城经济技术开发区',320900000000,3),
(320981000000,'东台市',320900000000,3),
(321000000000,'扬州市',320000000000,2),
(321001000000,'市辖区',321000000000,3),
(321002000000,'广陵区',321000000000,3),
(321003000000,'邗江区',321000000000,3),
(321012000000,'江都区',321000000000,3),
(321023000000,'宝应县',321000000000,3),
(321071000000,'扬州经济技术开发区',321000000000,3),
(321081000000,'仪征市',321000000000,3),
(321084000000,'高邮市',321000000000,3),
(321100000000,'镇江市',320000000000,2),
(321101000000,'市辖区',321100000000,3),
(321102000000,'京口区',321100000000,3),
(321111000000,'润州区',321100000000,3),
(321112000000,'丹徒区',321100000000,3),
(321171000000,'镇江新区',321100000000,3),
(321181000000,'丹阳市',321100000000,3),
(321182000000,'扬中市',321100000000,3),
(321183000000,'句容市',321100000000,3),
(321200000000,'泰州市',320000000000,2),
(321201000000,'市辖区',321200000000,3),
(321202000000,'海陵区',321200000000,3),
(321203000000,'高港区',321200000000,3),
(321204000000,'姜堰区',321200000000,3),
(321271000000,'泰州医药高新技术产业开发区',321200000000,3),
(321281000000,'兴化市',321200000000,3),
(321282000000,'靖江市',321200000000,3),
(321283000000,'泰兴市',321200000000,3),
(321300000000,'宿迁市',320000000000,2),
(321301000000,'市辖区',321300000000,3),
(321302000000,'宿城区',321300000000,3),
(321311000000,'宿豫区',321300000000,3),
(321322000000,'沭阳县',321300000000,3),
(321323000000,'泗阳县',321300000000,3),
(321324000000,'泗洪县',321300000000,3),
(321371000000,'宿迁经济技术开发区',321300000000,3),
(330000000000,'浙江省',0,1),
(330100000000,'杭州市',330000000000,2),
(330101000000,'市辖区',330100000000,3),
(330102000000,'上城区',330100000000,3),
(330103000000,'下城区',330100000000,3),
(330104000000,'江干区',330100000000,3),
(330105000000,'拱墅区',330100000000,3),
(330106000000,'西湖区',330100000000,3),
(330108000000,'滨江区',330100000000,3),
(330109000000,'萧山区',330100000000,3),
(330110000000,'余杭区',330100000000,3),
(330111000000,'富阳区',330100000000,3),
(330112000000,'临安区',330100000000,3),
(330122000000,'桐庐县',330100000000,3),
(330127000000,'淳安县',330100000000,3),
(330182000000,'建德市',330100000000,3),
(330200000000,'宁波市',330000000000,2),
(330201000000,'市辖区',330200000000,3),
(330203000000,'海曙区',330200000000,3),
(330205000000,'江北区',330200000000,3),
(330206000000,'北仑区',330200000000,3),
(330211000000,'镇海区',330200000000,3),
(330212000000,'鄞州区',330200000000,3),
(330213000000,'奉化区',330200000000,3),
(330225000000,'象山县',330200000000,3),
(330226000000,'宁海县',330200000000,3),
(330281000000,'余姚市',330200000000,3),
(330282000000,'慈溪市',330200000000,3),
(330300000000,'温州市',330000000000,2),
(330301000000,'市辖区',330300000000,3),
(330302000000,'鹿城区',330300000000,3),
(330303000000,'龙湾区',330300000000,3),
(330304000000,'瓯海区',330300000000,3),
(330305000000,'洞头区',330300000000,3),
(330324000000,'永嘉县',330300000000,3),
(330326000000,'平阳县',330300000000,3),
(330327000000,'苍南县',330300000000,3),
(330328000000,'文成县',330300000000,3),
(330329000000,'泰顺县',330300000000,3),
(330371000000,'温州经济技术开发区',330300000000,3),
(330381000000,'瑞安市',330300000000,3),
(330382000000,'乐清市',330300000000,3),
(330400000000,'嘉兴市',330000000000,2),
(330401000000,'市辖区',330400000000,3),
(330402000000,'南湖区',330400000000,3),
(330411000000,'秀洲区',330400000000,3),
(330421000000,'嘉善县',330400000000,3),
(330424000000,'海盐县',330400000000,3),
(330481000000,'海宁市',330400000000,3),
(330482000000,'平湖市',330400000000,3),
(330483000000,'桐乡市',330400000000,3),
(330500000000,'湖州市',330000000000,2),
(330501000000,'市辖区',330500000000,3),
(330502000000,'吴兴区',330500000000,3),
(330503000000,'南浔区',330500000000,3),
(330521000000,'德清县',330500000000,3),
(330522000000,'长兴县',330500000000,3),
(330523000000,'安吉县',330500000000,3),
(330600000000,'绍兴市',330000000000,2),
(330601000000,'市辖区',330600000000,3),
(330602000000,'越城区',330600000000,3),
(330603000000,'柯桥区',330600000000,3),
(330604000000,'上虞区',330600000000,3),
(330624000000,'新昌县',330600000000,3),
(330681000000,'诸暨市',330600000000,3),
(330683000000,'嵊州市',330600000000,3),
(330700000000,'金华市',330000000000,2),
(330701000000,'市辖区',330700000000,3),
(330702000000,'婺城区',330700000000,3),
(330703000000,'金东区',330700000000,3),
(330723000000,'武义县',330700000000,3),
(330726000000,'浦江县',330700000000,3),
(330727000000,'磐安县',330700000000,3),
(330781000000,'兰溪市',330700000000,3),
(330782000000,'义乌市',330700000000,3),
(330783000000,'东阳市',330700000000,3),
(330784000000,'永康市',330700000000,3),
(330800000000,'衢州市',330000000000,2),
(330801000000,'市辖区',330800000000,3),
(330802000000,'柯城区',330800000000,3),
(330803000000,'衢江区',330800000000,3),
(330822000000,'常山县',330800000000,3),
(330824000000,'开化县',330800000000,3),
(330825000000,'龙游县',330800000000,3),
(330881000000,'江山市',330800000000,3),
(330900000000,'舟山市',330000000000,2),
(330901000000,'市辖区',330900000000,3),
(330902000000,'定海区',330900000000,3),
(330903000000,'普陀区',330900000000,3),
(330921000000,'岱山县',330900000000,3),
(330922000000,'嵊泗县',330900000000,3),
(331000000000,'台州市',330000000000,2),
(331001000000,'市辖区',331000000000,3),
(331002000000,'椒江区',331000000000,3),
(331003000000,'黄岩区',331000000000,3),
(331004000000,'路桥区',331000000000,3),
(331022000000,'三门县',331000000000,3),
(331023000000,'天台县',331000000000,3),
(331024000000,'仙居县',331000000000,3),
(331081000000,'温岭市',331000000000,3),
(331082000000,'临海市',331000000000,3),
(331083000000,'玉环市',331000000000,3),
(331100000000,'丽水市',330000000000,2),
(331101000000,'市辖区',331100000000,3),
(331102000000,'莲都区',331100000000,3),
(331121000000,'青田县',331100000000,3),
(331122000000,'缙云县',331100000000,3),
(331123000000,'遂昌县',331100000000,3),
(331124000000,'松阳县',331100000000,3),
(331125000000,'云和县',331100000000,3),
(331126000000,'庆元县',331100000000,3),
(331127000000,'景宁畲族自治县',331100000000,3),
(331181000000,'龙泉市',331100000000,3),
(340000000000,'安徽省',0,1),
(340100000000,'合肥市',340000000000,2),
(340101000000,'市辖区',340100000000,3),
(340102000000,'瑶海区',340100000000,3),
(340103000000,'庐阳区',340100000000,3),
(340104000000,'蜀山区',340100000000,3),
(340111000000,'包河区',340100000000,3),
(340121000000,'长丰县',340100000000,3),
(340122000000,'肥东县',340100000000,3),
(340123000000,'肥西县',340100000000,3),
(340124000000,'庐江县',340100000000,3),
(340171000000,'合肥高新技术产业开发区',340100000000,3),
(340172000000,'合肥经济技术开发区',340100000000,3),
(340173000000,'合肥新站高新技术产业开发区',340100000000,3),
(340181000000,'巢湖市',340100000000,3),
(340200000000,'芜湖市',340000000000,2),
(340201000000,'市辖区',340200000000,3),
(340202000000,'镜湖区',340200000000,3),
(340203000000,'弋江区',340200000000,3),
(340207000000,'鸠江区',340200000000,3),
(340208000000,'三山区',340200000000,3),
(340221000000,'芜湖县',340200000000,3),
(340222000000,'繁昌县',340200000000,3),
(340223000000,'南陵县',340200000000,3),
(340225000000,'无为县',340200000000,3),
(340271000000,'芜湖经济技术开发区',340200000000,3),
(340272000000,'安徽芜湖长江大桥经济开发区',340200000000,3),
(340300000000,'蚌埠市',340000000000,2),
(340301000000,'市辖区',340300000000,3),
(340302000000,'龙子湖区',340300000000,3),
(340303000000,'蚌山区',340300000000,3),
(340304000000,'禹会区',340300000000,3),
(340311000000,'淮上区',340300000000,3),
(340321000000,'怀远县',340300000000,3),
(340322000000,'五河县',340300000000,3),
(340323000000,'固镇县',340300000000,3),
(340371000000,'蚌埠市高新技术开发区',340300000000,3),
(340372000000,'蚌埠市经济开发区',340300000000,3),
(340400000000,'淮南市',340000000000,2),
(340401000000,'市辖区',340400000000,3),
(340402000000,'大通区',340400000000,3),
(340403000000,'田家庵区',340400000000,3),
(340404000000,'谢家集区',340400000000,3),
(340405000000,'八公山区',340400000000,3),
(340406000000,'潘集区',340400000000,3),
(340421000000,'凤台县',340400000000,3),
(340422000000,'寿县',340400000000,3),
(340500000000,'马鞍山市',340000000000,2),
(340501000000,'市辖区',340500000000,3),
(340503000000,'花山区',340500000000,3),
(340504000000,'雨山区',340500000000,3),
(340506000000,'博望区',340500000000,3),
(340521000000,'当涂县',340500000000,3),
(340522000000,'含山县',340500000000,3),
(340523000000,'和县',340500000000,3),
(340600000000,'淮北市',340000000000,2),
(340601000000,'市辖区',340600000000,3),
(340602000000,'杜集区',340600000000,3),
(340603000000,'相山区',340600000000,3),
(340604000000,'烈山区',340600000000,3),
(340621000000,'濉溪县',340600000000,3),
(340700000000,'铜陵市',340000000000,2),
(340701000000,'市辖区',340700000000,3),
(340705000000,'铜官区',340700000000,3),
(340706000000,'义安区',340700000000,3),
(340711000000,'郊区',340700000000,3),
(340722000000,'枞阳县',340700000000,3),
(340800000000,'安庆市',340000000000,2),
(340801000000,'市辖区',340800000000,3),
(340802000000,'迎江区',340800000000,3),
(340803000000,'大观区',340800000000,3),
(340811000000,'宜秀区',340800000000,3),
(340822000000,'怀宁县',340800000000,3),
(340825000000,'太湖县',340800000000,3),
(340826000000,'宿松县',340800000000,3),
(340827000000,'望江县',340800000000,3),
(340828000000,'岳西县',340800000000,3),
(340871000000,'安徽安庆经济开发区',340800000000,3),
(340881000000,'桐城市',340800000000,3),
(340882000000,'潜山市',340800000000,3),
(341000000000,'黄山市',340000000000,2),
(341001000000,'市辖区',341000000000,3),
(341002000000,'屯溪区',341000000000,3),
(341003000000,'黄山区',341000000000,3),
(341004000000,'徽州区',341000000000,3),
(341021000000,'歙县',341000000000,3),
(341022000000,'休宁县',341000000000,3),
(341023000000,'黟县',341000000000,3),
(341024000000,'祁门县',341000000000,3),
(341100000000,'滁州市',340000000000,2),
(341101000000,'市辖区',341100000000,3),
(341102000000,'琅琊区',341100000000,3),
(341103000000,'南谯区',341100000000,3),
(341122000000,'来安县',341100000000,3),
(341124000000,'全椒县',341100000000,3),
(341125000000,'定远县',341100000000,3),
(341126000000,'凤阳县',341100000000,3),
(341171000000,'苏滁现代产业园',341100000000,3),
(341172000000,'滁州经济技术开发区',341100000000,3),
(341181000000,'天长市',341100000000,3),
(341182000000,'明光市',341100000000,3),
(341200000000,'阜阳市',340000000000,2),
(341201000000,'市辖区',341200000000,3),
(341202000000,'颍州区',341200000000,3),
(341203000000,'颍东区',341200000000,3),
(341204000000,'颍泉区',341200000000,3),
(341221000000,'临泉县',341200000000,3),
(341222000000,'太和县',341200000000,3),
(341225000000,'阜南县',341200000000,3),
(341226000000,'颍上县',341200000000,3),
(341271000000,'阜阳合肥现代产业园区',341200000000,3),
(341272000000,'阜阳经济技术开发区',341200000000,3),
(341282000000,'界首市',341200000000,3),
(341300000000,'宿州市',340000000000,2),
(341301000000,'市辖区',341300000000,3),
(341302000000,'埇桥区',341300000000,3),
(341321000000,'砀山县',341300000000,3),
(341322000000,'萧县',341300000000,3),
(341323000000,'灵璧县',341300000000,3),
(341324000000,'泗县',341300000000,3),
(341371000000,'宿州马鞍山现代产业园区',341300000000,3),
(341372000000,'宿州经济技术开发区',341300000000,3),
(341500000000,'六安市',340000000000,2),
(341501000000,'市辖区',341500000000,3),
(341502000000,'金安区',341500000000,3),
(341503000000,'裕安区',341500000000,3),
(341504000000,'叶集区',341500000000,3),
(341522000000,'霍邱县',341500000000,3),
(341523000000,'舒城县',341500000000,3),
(341524000000,'金寨县',341500000000,3),
(341525000000,'霍山县',341500000000,3),
(341600000000,'亳州市',340000000000,2),
(341601000000,'市辖区',341600000000,3),
(341602000000,'谯城区',341600000000,3),
(341621000000,'涡阳县',341600000000,3),
(341622000000,'蒙城县',341600000000,3),
(341623000000,'利辛县',341600000000,3),
(341700000000,'池州市',340000000000,2),
(341701000000,'市辖区',341700000000,3),
(341702000000,'贵池区',341700000000,3),
(341721000000,'东至县',341700000000,3),
(341722000000,'石台县',341700000000,3),
(341723000000,'青阳县',341700000000,3),
(341800000000,'宣城市',340000000000,2),
(341801000000,'市辖区',341800000000,3),
(341802000000,'宣州区',341800000000,3),
(341821000000,'郎溪县',341800000000,3),
(341822000000,'广德县',341800000000,3),
(341823000000,'泾县',341800000000,3),
(341824000000,'绩溪县',341800000000,3),
(341825000000,'旌德县',341800000000,3),
(341871000000,'宣城市经济开发区',341800000000,3),
(341881000000,'宁国市',341800000000,3),
(350000000000,'福建省',0,1),
(350100000000,'福州市',350000000000,2),
(350101000000,'市辖区',350100000000,3),
(350102000000,'鼓楼区',350100000000,3),
(350103000000,'台江区',350100000000,3),
(350104000000,'仓山区',350100000000,3),
(350105000000,'马尾区',350100000000,3),
(350111000000,'晋安区',350100000000,3),
(350112000000,'长乐区',350100000000,3),
(350121000000,'闽侯县',350100000000,3),
(350122000000,'连江县',350100000000,3),
(350123000000,'罗源县',350100000000,3),
(350124000000,'闽清县',350100000000,3),
(350125000000,'永泰县',350100000000,3),
(350128000000,'平潭县',350100000000,3),
(350181000000,'福清市',350100000000,3),
(350200000000,'厦门市',350000000000,2),
(350201000000,'市辖区',350200000000,3),
(350203000000,'思明区',350200000000,3),
(350205000000,'海沧区',350200000000,3),
(350206000000,'湖里区',350200000000,3),
(350211000000,'集美区',350200000000,3),
(350212000000,'同安区',350200000000,3),
(350213000000,'翔安区',350200000000,3),
(350300000000,'莆田市',350000000000,2),
(350301000000,'市辖区',350300000000,3),
(350302000000,'城厢区',350300000000,3),
(350303000000,'涵江区',350300000000,3),
(350304000000,'荔城区',350300000000,3),
(350305000000,'秀屿区',350300000000,3),
(350322000000,'仙游县',350300000000,3),
(350400000000,'三明市',350000000000,2),
(350401000000,'市辖区',350400000000,3),
(350402000000,'梅列区',350400000000,3),
(350403000000,'三元区',350400000000,3),
(350421000000,'明溪县',350400000000,3),
(350423000000,'清流县',350400000000,3),
(350424000000,'宁化县',350400000000,3),
(350425000000,'大田县',350400000000,3),
(350426000000,'尤溪县',350400000000,3),
(350427000000,'沙县',350400000000,3),
(350428000000,'将乐县',350400000000,3),
(350429000000,'泰宁县',350400000000,3),
(350430000000,'建宁县',350400000000,3),
(350481000000,'永安市',350400000000,3),
(350500000000,'泉州市',350000000000,2),
(350501000000,'市辖区',350500000000,3),
(350502000000,'鲤城区',350500000000,3),
(350503000000,'丰泽区',350500000000,3),
(350504000000,'洛江区',350500000000,3),
(350505000000,'泉港区',350500000000,3),
(350521000000,'惠安县',350500000000,3),
(350524000000,'安溪县',350500000000,3),
(350525000000,'永春县',350500000000,3),
(350526000000,'德化县',350500000000,3),
(350527000000,'金门县',350500000000,3),
(350581000000,'石狮市',350500000000,3),
(350582000000,'晋江市',350500000000,3),
(350583000000,'南安市',350500000000,3),
(350600000000,'漳州市',350000000000,2),
(350601000000,'市辖区',350600000000,3),
(350602000000,'芗城区',350600000000,3),
(350603000000,'龙文区',350600000000,3),
(350622000000,'云霄县',350600000000,3),
(350623000000,'漳浦县',350600000000,3),
(350624000000,'诏安县',350600000000,3),
(350625000000,'长泰县',350600000000,3),
(350626000000,'东山县',350600000000,3),
(350627000000,'南靖县',350600000000,3),
(350628000000,'平和县',350600000000,3),
(350629000000,'华安县',350600000000,3),
(350681000000,'龙海市',350600000000,3),
(350700000000,'南平市',350000000000,2),
(350701000000,'市辖区',350700000000,3),
(350702000000,'延平区',350700000000,3),
(350703000000,'建阳区',350700000000,3),
(350721000000,'顺昌县',350700000000,3),
(350722000000,'浦城县',350700000000,3),
(350723000000,'光泽县',350700000000,3),
(350724000000,'松溪县',350700000000,3),
(350725000000,'政和县',350700000000,3),
(350781000000,'邵武市',350700000000,3),
(350782000000,'武夷山市',350700000000,3),
(350783000000,'建瓯市',350700000000,3),
(350800000000,'龙岩市',350000000000,2),
(350801000000,'市辖区',350800000000,3),
(350802000000,'新罗区',350800000000,3),
(350803000000,'永定区',350800000000,3),
(350821000000,'长汀县',350800000000,3),
(350823000000,'上杭县',350800000000,3),
(350824000000,'武平县',350800000000,3),
(350825000000,'连城县',350800000000,3),
(350881000000,'漳平市',350800000000,3),
(350900000000,'宁德市',350000000000,2),
(350901000000,'市辖区',350900000000,3),
(350902000000,'蕉城区',350900000000,3),
(350921000000,'霞浦县',350900000000,3),
(350922000000,'古田县',350900000000,3),
(350923000000,'屏南县',350900000000,3),
(350924000000,'寿宁县',350900000000,3),
(350925000000,'周宁县',350900000000,3),
(350926000000,'柘荣县',350900000000,3),
(350981000000,'福安市',350900000000,3),
(350982000000,'福鼎市',350900000000,3),
(360000000000,'江西省',0,1),
(360100000000,'南昌市',360000000000,2),
(360101000000,'市辖区',360100000000,3),
(360102000000,'东湖区',360100000000,3),
(360103000000,'西湖区',360100000000,3),
(360104000000,'青云谱区',360100000000,3),
(360105000000,'湾里区',360100000000,3),
(360111000000,'青山湖区',360100000000,3),
(360112000000,'新建区',360100000000,3),
(360121000000,'南昌县',360100000000,3),
(360123000000,'安义县',360100000000,3),
(360124000000,'进贤县',360100000000,3),
(360200000000,'景德镇市',360000000000,2),
(360201000000,'市辖区',360200000000,3),
(360202000000,'昌江区',360200000000,3),
(360203000000,'珠山区',360200000000,3),
(360222000000,'浮梁县',360200000000,3),
(360281000000,'乐平市',360200000000,3),
(360300000000,'萍乡市',360000000000,2),
(360301000000,'市辖区',360300000000,3),
(360302000000,'安源区',360300000000,3),
(360313000000,'湘东区',360300000000,3),
(360321000000,'莲花县',360300000000,3),
(360322000000,'上栗县',360300000000,3),
(360323000000,'芦溪县',360300000000,3),
(360400000000,'九江市',360000000000,2),
(360401000000,'市辖区',360400000000,3),
(360402000000,'濂溪区',360400000000,3),
(360403000000,'浔阳区',360400000000,3),
(360404000000,'柴桑区',360400000000,3),
(360423000000,'武宁县',360400000000,3),
(360424000000,'修水县',360400000000,3),
(360425000000,'永修县',360400000000,3),
(360426000000,'德安县',360400000000,3),
(360428000000,'都昌县',360400000000,3),
(360429000000,'湖口县',360400000000,3),
(360430000000,'彭泽县',360400000000,3),
(360481000000,'瑞昌市',360400000000,3),
(360482000000,'共青城市',360400000000,3),
(360483000000,'庐山市',360400000000,3),
(360500000000,'新余市',360000000000,2),
(360501000000,'市辖区',360500000000,3),
(360502000000,'渝水区',360500000000,3),
(360521000000,'分宜县',360500000000,3),
(360600000000,'鹰潭市',360000000000,2),
(360601000000,'市辖区',360600000000,3),
(360602000000,'月湖区',360600000000,3),
(360603000000,'余江区',360600000000,3),
(360681000000,'贵溪市',360600000000,3),
(360700000000,'赣州市',360000000000,2),
(360701000000,'市辖区',360700000000,3),
(360702000000,'章贡区',360700000000,3),
(360703000000,'南康区',360700000000,3),
(360704000000,'赣县区',360700000000,3),
(360722000000,'信丰县',360700000000,3),
(360723000000,'大余县',360700000000,3),
(360724000000,'上犹县',360700000000,3),
(360725000000,'崇义县',360700000000,3),
(360726000000,'安远县',360700000000,3),
(360727000000,'龙南县',360700000000,3),
(360728000000,'定南县',360700000000,3),
(360729000000,'全南县',360700000000,3),
(360730000000,'宁都县',360700000000,3),
(360731000000,'于都县',360700000000,3),
(360732000000,'兴国县',360700000000,3),
(360733000000,'会昌县',360700000000,3),
(360734000000,'寻乌县',360700000000,3),
(360735000000,'石城县',360700000000,3),
(360781000000,'瑞金市',360700000000,3),
(360800000000,'吉安市',360000000000,2),
(360801000000,'市辖区',360800000000,3),
(360802000000,'吉州区',360800000000,3),
(360803000000,'青原区',360800000000,3),
(360821000000,'吉安县',360800000000,3),
(360822000000,'吉水县',360800000000,3),
(360823000000,'峡江县',360800000000,3),
(360824000000,'新干县',360800000000,3),
(360825000000,'永丰县',360800000000,3),
(360826000000,'泰和县',360800000000,3),
(360827000000,'遂川县',360800000000,3),
(360828000000,'万安县',360800000000,3),
(360829000000,'安福县',360800000000,3),
(360830000000,'永新县',360800000000,3),
(360881000000,'井冈山市',360800000000,3),
(360900000000,'宜春市',360000000000,2),
(360901000000,'市辖区',360900000000,3),
(360902000000,'袁州区',360900000000,3),
(360921000000,'奉新县',360900000000,3),
(360922000000,'万载县',360900000000,3),
(360923000000,'上高县',360900000000,3),
(360924000000,'宜丰县',360900000000,3),
(360925000000,'靖安县',360900000000,3),
(360926000000,'铜鼓县',360900000000,3),
(360981000000,'丰城市',360900000000,3),
(360982000000,'樟树市',360900000000,3),
(360983000000,'高安市',360900000000,3),
(361000000000,'抚州市',360000000000,2),
(361001000000,'市辖区',361000000000,3),
(361002000000,'临川区',361000000000,3),
(361003000000,'东乡区',361000000000,3),
(361021000000,'南城县',361000000000,3),
(361022000000,'黎川县',361000000000,3),
(361023000000,'南丰县',361000000000,3),
(361024000000,'崇仁县',361000000000,3),
(361025000000,'乐安县',361000000000,3),
(361026000000,'宜黄县',361000000000,3),
(361027000000,'金溪县',361000000000,3),
(361028000000,'资溪县',361000000000,3),
(361030000000,'广昌县',361000000000,3),
(361100000000,'上饶市',360000000000,2),
(361101000000,'市辖区',361100000000,3),
(361102000000,'信州区',361100000000,3),
(361103000000,'广丰区',361100000000,3),
(361121000000,'上饶县',361100000000,3),
(361123000000,'玉山县',361100000000,3),
(361124000000,'铅山县',361100000000,3),
(361125000000,'横峰县',361100000000,3),
(361126000000,'弋阳县',361100000000,3),
(361127000000,'余干县',361100000000,3),
(361128000000,'鄱阳县',361100000000,3),
(361129000000,'万年县',361100000000,3),
(361130000000,'婺源县',361100000000,3),
(361181000000,'德兴市',361100000000,3),
(370000000000,'山东省',0,1),
(370100000000,'济南市',370000000000,2),
(370101000000,'市辖区',370100000000,3),
(370102000000,'历下区',370100000000,3),
(370103000000,'市中区',370100000000,3),
(370104000000,'槐荫区',370100000000,3),
(370105000000,'天桥区',370100000000,3),
(370112000000,'历城区',370100000000,3),
(370113000000,'长清区',370100000000,3),
(370114000000,'章丘区',370100000000,3),
(370115000000,'济阳区',370100000000,3),
(370124000000,'平阴县',370100000000,3),
(370126000000,'商河县',370100000000,3),
(370171000000,'济南高新技术产业开发区',370100000000,3),
(370200000000,'青岛市',370000000000,2),
(370201000000,'市辖区',370200000000,3),
(370202000000,'市南区',370200000000,3),
(370203000000,'市北区',370200000000,3),
(370211000000,'黄岛区',370200000000,3),
(370212000000,'崂山区',370200000000,3),
(370213000000,'李沧区',370200000000,3),
(370214000000,'城阳区',370200000000,3),
(370215000000,'即墨区',370200000000,3),
(370271000000,'青岛高新技术产业开发区',370200000000,3),
(370281000000,'胶州市',370200000000,3),
(370283000000,'平度市',370200000000,3),
(370285000000,'莱西市',370200000000,3),
(370300000000,'淄博市',370000000000,2),
(370301000000,'市辖区',370300000000,3),
(370302000000,'淄川区',370300000000,3),
(370303000000,'张店区',370300000000,3),
(370304000000,'博山区',370300000000,3),
(370305000000,'临淄区',370300000000,3),
(370306000000,'周村区',370300000000,3),
(370321000000,'桓台县',370300000000,3),
(370322000000,'高青县',370300000000,3),
(370323000000,'沂源县',370300000000,3),
(370400000000,'枣庄市',370000000000,2),
(370401000000,'市辖区',370400000000,3),
(370402000000,'市中区',370400000000,3),
(370403000000,'薛城区',370400000000,3),
(370404000000,'峄城区',370400000000,3),
(370405000000,'台儿庄区',370400000000,3),
(370406000000,'山亭区',370400000000,3),
(370481000000,'滕州市',370400000000,3),
(370500000000,'东营市',370000000000,2),
(370501000000,'市辖区',370500000000,3),
(370502000000,'东营区',370500000000,3),
(370503000000,'河口区',370500000000,3),
(370505000000,'垦利区',370500000000,3),
(370522000000,'利津县',370500000000,3),
(370523000000,'广饶县',370500000000,3),
(370571000000,'东营经济技术开发区',370500000000,3),
(370572000000,'东营港经济开发区',370500000000,3),
(370600000000,'烟台市',370000000000,2),
(370601000000,'市辖区',370600000000,3),
(370602000000,'芝罘区',370600000000,3),
(370611000000,'福山区',370600000000,3),
(370612000000,'牟平区',370600000000,3),
(370613000000,'莱山区',370600000000,3),
(370634000000,'长岛县',370600000000,3),
(370671000000,'烟台高新技术产业开发区',370600000000,3),
(370672000000,'烟台经济技术开发区',370600000000,3),
(370681000000,'龙口市',370600000000,3),
(370682000000,'莱阳市',370600000000,3),
(370683000000,'莱州市',370600000000,3),
(370684000000,'蓬莱市',370600000000,3),
(370685000000,'招远市',370600000000,3),
(370686000000,'栖霞市',370600000000,3),
(370687000000,'海阳市',370600000000,3),
(370700000000,'潍坊市',370000000000,2),
(370701000000,'市辖区',370700000000,3),
(370702000000,'潍城区',370700000000,3),
(370703000000,'寒亭区',370700000000,3),
(370704000000,'坊子区',370700000000,3),
(370705000000,'奎文区',370700000000,3),
(370724000000,'临朐县',370700000000,3),
(370725000000,'昌乐县',370700000000,3),
(370772000000,'潍坊滨海经济技术开发区',370700000000,3),
(370781000000,'青州市',370700000000,3),
(370782000000,'诸城市',370700000000,3),
(370783000000,'寿光市',370700000000,3),
(370784000000,'安丘市',370700000000,3),
(370785000000,'高密市',370700000000,3),
(370786000000,'昌邑市',370700000000,3),
(370800000000,'济宁市',370000000000,2),
(370801000000,'市辖区',370800000000,3),
(370811000000,'任城区',370800000000,3),
(370812000000,'兖州区',370800000000,3),
(370826000000,'微山县',370800000000,3),
(370827000000,'鱼台县',370800000000,3),
(370828000000,'金乡县',370800000000,3),
(370829000000,'嘉祥县',370800000000,3),
(370830000000,'汶上县',370800000000,3),
(370831000000,'泗水县',370800000000,3),
(370832000000,'梁山县',370800000000,3),
(370871000000,'济宁高新技术产业开发区',370800000000,3),
(370881000000,'曲阜市',370800000000,3),
(370883000000,'邹城市',370800000000,3),
(370900000000,'泰安市',370000000000,2),
(370901000000,'市辖区',370900000000,3),
(370902000000,'泰山区',370900000000,3),
(370911000000,'岱岳区',370900000000,3),
(370921000000,'宁阳县',370900000000,3),
(370923000000,'东平县',370900000000,3),
(370982000000,'新泰市',370900000000,3),
(370983000000,'肥城市',370900000000,3),
(371000000000,'威海市',370000000000,2),
(371001000000,'市辖区',371000000000,3),
(371002000000,'环翠区',371000000000,3),
(371003000000,'文登区',371000000000,3),
(371071000000,'威海火炬高技术产业开发区',371000000000,3),
(371072000000,'威海经济技术开发区',371000000000,3),
(371073000000,'威海临港经济技术开发区',371000000000,3),
(371082000000,'荣成市',371000000000,3),
(371083000000,'乳山市',371000000000,3),
(371100000000,'日照市',370000000000,2),
(371101000000,'市辖区',371100000000,3),
(371102000000,'东港区',371100000000,3),
(371103000000,'岚山区',371100000000,3),
(371121000000,'五莲县',371100000000,3),
(371122000000,'莒县',371100000000,3),
(371171000000,'日照经济技术开发区',371100000000,3),
(371200000000,'莱芜市',370000000000,2),
(371201000000,'市辖区',371200000000,3),
(371202000000,'莱城区',371200000000,3),
(371203000000,'钢城区',371200000000,3),
(371300000000,'临沂市',370000000000,2),
(371301000000,'市辖区',371300000000,3),
(371302000000,'兰山区',371300000000,3),
(371311000000,'罗庄区',371300000000,3),
(371312000000,'河东区',371300000000,3),
(371321000000,'沂南县',371300000000,3),
(371322000000,'郯城县',371300000000,3),
(371323000000,'沂水县',371300000000,3),
(371324000000,'兰陵县',371300000000,3),
(371325000000,'费县',371300000000,3),
(371326000000,'平邑县',371300000000,3),
(371327000000,'莒南县',371300000000,3),
(371328000000,'蒙阴县',371300000000,3),
(371329000000,'临沭县',371300000000,3),
(371371000000,'临沂高新技术产业开发区',371300000000,3),
(371372000000,'临沂经济技术开发区',371300000000,3),
(371373000000,'临沂临港经济开发区',371300000000,3),
(371400000000,'德州市',370000000000,2),
(371401000000,'市辖区',371400000000,3),
(371402000000,'德城区',371400000000,3),
(371403000000,'陵城区',371400000000,3),
(371422000000,'宁津县',371400000000,3),
(371423000000,'庆云县',371400000000,3),
(371424000000,'临邑县',371400000000,3),
(371425000000,'齐河县',371400000000,3),
(371426000000,'平原县',371400000000,3),
(371427000000,'夏津县',371400000000,3),
(371428000000,'武城县',371400000000,3),
(371471000000,'德州经济技术开发区',371400000000,3),
(371472000000,'德州运河经济开发区',371400000000,3),
(371481000000,'乐陵市',371400000000,3),
(371482000000,'禹城市',371400000000,3),
(371500000000,'聊城市',370000000000,2),
(371501000000,'市辖区',371500000000,3),
(371502000000,'东昌府区',371500000000,3),
(371521000000,'阳谷县',371500000000,3),
(371522000000,'莘县',371500000000,3),
(371523000000,'茌平县',371500000000,3),
(371524000000,'东阿县',371500000000,3),
(371525000000,'冠县',371500000000,3),
(371526000000,'高唐县',371500000000,3),
(371581000000,'临清市',371500000000,3),
(371600000000,'滨州市',370000000000,2),
(371601000000,'市辖区',371600000000,3),
(371602000000,'滨城区',371600000000,3),
(371603000000,'沾化区',371600000000,3),
(371621000000,'惠民县',371600000000,3),
(371622000000,'阳信县',371600000000,3),
(371623000000,'无棣县',371600000000,3),
(371625000000,'博兴县',371600000000,3),
(371681000000,'邹平市',371600000000,3),
(371700000000,'菏泽市',370000000000,2),
(371701000000,'市辖区',371700000000,3),
(371702000000,'牡丹区',371700000000,3),
(371703000000,'定陶区',371700000000,3),
(371721000000,'曹县',371700000000,3),
(371722000000,'单县',371700000000,3),
(371723000000,'成武县',371700000000,3),
(371724000000,'巨野县',371700000000,3),
(371725000000,'郓城县',371700000000,3),
(371726000000,'鄄城县',371700000000,3),
(371728000000,'东明县',371700000000,3),
(371771000000,'菏泽经济技术开发区',371700000000,3),
(371772000000,'菏泽高新技术开发区',371700000000,3),
(410000000000,'河南省',0,1),
(410100000000,'郑州市',410000000000,2),
(410101000000,'市辖区',410100000000,3),
(410102000000,'中原区',410100000000,3),
(410103000000,'二七区',410100000000,3),
(410104000000,'管城回族区',410100000000,3),
(410105000000,'金水区',410100000000,3),
(410106000000,'上街区',410100000000,3),
(410108000000,'惠济区',410100000000,3),
(410122000000,'中牟县',410100000000,3),
(410171000000,'郑州经济技术开发区',410100000000,3),
(410172000000,'郑州高新技术产业开发区',410100000000,3),
(410173000000,'郑州航空港经济综合实验区',410100000000,3),
(410181000000,'巩义市',410100000000,3),
(410182000000,'荥阳市',410100000000,3),
(410183000000,'新密市',410100000000,3),
(410184000000,'新郑市',410100000000,3),
(410185000000,'登封市',410100000000,3),
(410200000000,'开封市',410000000000,2),
(410201000000,'市辖区',410200000000,3),
(410202000000,'龙亭区',410200000000,3),
(410203000000,'顺河回族区',410200000000,3),
(410204000000,'鼓楼区',410200000000,3),
(410205000000,'禹王台区',410200000000,3),
(410212000000,'祥符区',410200000000,3),
(410221000000,'杞县',410200000000,3),
(410222000000,'通许县',410200000000,3),
(410223000000,'尉氏县',410200000000,3),
(410225000000,'兰考县',410200000000,3),
(410300000000,'洛阳市',410000000000,2),
(410301000000,'市辖区',410300000000,3),
(410302000000,'老城区',410300000000,3),
(410303000000,'西工区',410300000000,3),
(410304000000,'瀍河回族区',410300000000,3),
(410305000000,'涧西区',410300000000,3),
(410306000000,'吉利区',410300000000,3),
(410311000000,'洛龙区',410300000000,3),
(410322000000,'孟津县',410300000000,3),
(410323000000,'新安县',410300000000,3),
(410324000000,'栾川县',410300000000,3),
(410325000000,'嵩县',410300000000,3),
(410326000000,'汝阳县',410300000000,3),
(410327000000,'宜阳县',410300000000,3),
(410328000000,'洛宁县',410300000000,3),
(410329000000,'伊川县',410300000000,3),
(410371000000,'洛阳高新技术产业开发区',410300000000,3),
(410381000000,'偃师市',410300000000,3),
(410400000000,'平顶山市',410000000000,2),
(410401000000,'市辖区',410400000000,3),
(410402000000,'新华区',410400000000,3),
(410403000000,'卫东区',410400000000,3),
(410404000000,'石龙区',410400000000,3),
(410411000000,'湛河区',410400000000,3),
(410421000000,'宝丰县',410400000000,3),
(410422000000,'叶县',410400000000,3),
(410423000000,'鲁山县',410400000000,3),
(410425000000,'郏县',410400000000,3),
(410471000000,'平顶山高新技术产业开发区',410400000000,3),
(410472000000,'平顶山市新城区',410400000000,3),
(410481000000,'舞钢市',410400000000,3),
(410482000000,'汝州市',410400000000,3),
(410500000000,'安阳市',410000000000,2),
(410501000000,'市辖区',410500000000,3),
(410502000000,'文峰区',410500000000,3),
(410503000000,'北关区',410500000000,3),
(410505000000,'殷都区',410500000000,3),
(410506000000,'龙安区',410500000000,3),
(410522000000,'安阳县',410500000000,3),
(410523000000,'汤阴县',410500000000,3),
(410526000000,'滑县',410500000000,3),
(410527000000,'内黄县',410500000000,3),
(410571000000,'安阳高新技术产业开发区',410500000000,3),
(410581000000,'林州市',410500000000,3),
(410600000000,'鹤壁市',410000000000,2),
(410601000000,'市辖区',410600000000,3),
(410602000000,'鹤山区',410600000000,3),
(410603000000,'山城区',410600000000,3),
(410611000000,'淇滨区',410600000000,3),
(410621000000,'浚县',410600000000,3),
(410622000000,'淇县',410600000000,3),
(410671000000,'鹤壁经济技术开发区',410600000000,3),
(410700000000,'新乡市',410000000000,2),
(410701000000,'市辖区',410700000000,3),
(410702000000,'红旗区',410700000000,3),
(410703000000,'卫滨区',410700000000,3),
(410704000000,'凤泉区',410700000000,3),
(410711000000,'牧野区',410700000000,3),
(410721000000,'新乡县',410700000000,3),
(410724000000,'获嘉县',410700000000,3),
(410725000000,'原阳县',410700000000,3),
(410726000000,'延津县',410700000000,3),
(410727000000,'封丘县',410700000000,3),
(410728000000,'长垣县',410700000000,3),
(410771000000,'新乡高新技术产业开发区',410700000000,3),
(410772000000,'新乡经济技术开发区',410700000000,3),
(410773000000,'新乡市平原城乡一体化示范区',410700000000,3),
(410781000000,'卫辉市',410700000000,3),
(410782000000,'辉县市',410700000000,3),
(410800000000,'焦作市',410000000000,2),
(410801000000,'市辖区',410800000000,3),
(410802000000,'解放区',410800000000,3),
(410803000000,'中站区',410800000000,3),
(410804000000,'马村区',410800000000,3),
(410811000000,'山阳区',410800000000,3),
(410821000000,'修武县',410800000000,3),
(410822000000,'博爱县',410800000000,3),
(410823000000,'武陟县',410800000000,3),
(410825000000,'温县',410800000000,3),
(410871000000,'焦作城乡一体化示范区',410800000000,3),
(410882000000,'沁阳市',410800000000,3),
(410883000000,'孟州市',410800000000,3),
(410900000000,'濮阳市',410000000000,2),
(410901000000,'市辖区',410900000000,3),
(410902000000,'华龙区',410900000000,3),
(410922000000,'清丰县',410900000000,3),
(410923000000,'南乐县',410900000000,3),
(410926000000,'范县',410900000000,3),
(410927000000,'台前县',410900000000,3),
(410928000000,'濮阳县',410900000000,3),
(410971000000,'河南濮阳工业园区',410900000000,3),
(410972000000,'濮阳经济技术开发区',410900000000,3),
(411000000000,'许昌市',410000000000,2),
(411001000000,'市辖区',411000000000,3),
(411002000000,'魏都区',411000000000,3),
(411003000000,'建安区',411000000000,3),
(411024000000,'鄢陵县',411000000000,3),
(411025000000,'襄城县',411000000000,3),
(411071000000,'许昌经济技术开发区',411000000000,3),
(411081000000,'禹州市',411000000000,3),
(411082000000,'长葛市',411000000000,3),
(411100000000,'漯河市',410000000000,2),
(411101000000,'市辖区',411100000000,3),
(411102000000,'源汇区',411100000000,3),
(411103000000,'郾城区',411100000000,3),
(411104000000,'召陵区',411100000000,3),
(411121000000,'舞阳县',411100000000,3),
(411122000000,'临颍县',411100000000,3),
(411171000000,'漯河经济技术开发区',411100000000,3),
(411200000000,'三门峡市',410000000000,2),
(411201000000,'市辖区',411200000000,3),
(411202000000,'湖滨区',411200000000,3),
(411203000000,'陕州区',411200000000,3),
(411221000000,'渑池县',411200000000,3),
(411224000000,'卢氏县',411200000000,3),
(411271000000,'河南三门峡经济开发区',411200000000,3),
(411281000000,'义马市',411200000000,3),
(411282000000,'灵宝市',411200000000,3),
(411300000000,'南阳市',410000000000,2),
(411301000000,'市辖区',411300000000,3),
(411302000000,'宛城区',411300000000,3),
(411303000000,'卧龙区',411300000000,3),
(411321000000,'南召县',411300000000,3),
(411322000000,'方城县',411300000000,3),
(411323000000,'西峡县',411300000000,3),
(411324000000,'镇平县',411300000000,3),
(411325000000,'内乡县',411300000000,3),
(411326000000,'淅川县',411300000000,3),
(411327000000,'社旗县',411300000000,3),
(411328000000,'唐河县',411300000000,3),
(411329000000,'新野县',411300000000,3),
(411330000000,'桐柏县',411300000000,3),
(411371000000,'南阳高新技术产业开发区',411300000000,3),
(411372000000,'南阳市城乡一体化示范区',411300000000,3),
(411381000000,'邓州市',411300000000,3),
(411400000000,'商丘市',410000000000,2),
(411401000000,'市辖区',411400000000,3),
(411402000000,'梁园区',411400000000,3),
(411403000000,'睢阳区',411400000000,3),
(411421000000,'民权县',411400000000,3),
(411422000000,'睢县',411400000000,3),
(411423000000,'宁陵县',411400000000,3),
(411424000000,'柘城县',411400000000,3),
(411425000000,'虞城县',411400000000,3),
(411426000000,'夏邑县',411400000000,3),
(411471000000,'豫东综合物流产业聚集区',411400000000,3),
(411472000000,'河南商丘经济开发区',411400000000,3),
(411481000000,'永城市',411400000000,3),
(411500000000,'信阳市',410000000000,2),
(411501000000,'市辖区',411500000000,3),
(411502000000,'浉河区',411500000000,3),
(411503000000,'平桥区',411500000000,3),
(411521000000,'罗山县',411500000000,3),
(411522000000,'光山县',411500000000,3),
(411523000000,'新县',411500000000,3),
(411524000000,'商城县',411500000000,3),
(411525000000,'固始县',411500000000,3),
(411526000000,'潢川县',411500000000,3),
(411527000000,'淮滨县',411500000000,3),
(411528000000,'息县',411500000000,3),
(411571000000,'信阳高新技术产业开发区',411500000000,3),
(411600000000,'周口市',410000000000,2),
(411601000000,'市辖区',411600000000,3),
(411602000000,'川汇区',411600000000,3),
(411621000000,'扶沟县',411600000000,3),
(411622000000,'西华县',411600000000,3),
(411623000000,'商水县',411600000000,3),
(411624000000,'沈丘县',411600000000,3),
(411625000000,'郸城县',411600000000,3),
(411626000000,'淮阳县',411600000000,3),
(411627000000,'太康县',411600000000,3),
(411628000000,'鹿邑县',411600000000,3),
(411671000000,'河南周口经济开发区',411600000000,3),
(411681000000,'项城市',411600000000,3),
(411700000000,'驻马店市',410000000000,2),
(411701000000,'市辖区',411700000000,3),
(411702000000,'驿城区',411700000000,3),
(411721000000,'西平县',411700000000,3),
(411722000000,'上蔡县',411700000000,3),
(411723000000,'平舆县',411700000000,3),
(411724000000,'正阳县',411700000000,3),
(411725000000,'确山县',411700000000,3),
(411726000000,'泌阳县',411700000000,3),
(411727000000,'汝南县',411700000000,3),
(411728000000,'遂平县',411700000000,3),
(411729000000,'新蔡县',411700000000,3),
(411771000000,'河南驻马店经济开发区',411700000000,3),
(419000000000,'省直辖县级行政区划',410000000000,2),
(419001000000,'济源市',419000000000,3),
(420000000000,'湖北省',0,1),
(420100000000,'武汉市',420000000000,2),
(420101000000,'市辖区',420100000000,3),
(420102000000,'江岸区',420100000000,3),
(420103000000,'江汉区',420100000000,3),
(420104000000,'硚口区',420100000000,3),
(420105000000,'汉阳区',420100000000,3),
(420106000000,'武昌区',420100000000,3),
(420107000000,'青山区',420100000000,3),
(420111000000,'洪山区',420100000000,3),
(420112000000,'东西湖区',420100000000,3),
(420113000000,'汉南区',420100000000,3),
(420114000000,'蔡甸区',420100000000,3),
(420115000000,'江夏区',420100000000,3),
(420116000000,'黄陂区',420100000000,3),
(420117000000,'新洲区',420100000000,3),
(420200000000,'黄石市',420000000000,2),
(420201000000,'市辖区',420200000000,3),
(420202000000,'黄石港区',420200000000,3),
(420203000000,'西塞山区',420200000000,3),
(420204000000,'下陆区',420200000000,3),
(420205000000,'铁山区',420200000000,3),
(420222000000,'阳新县',420200000000,3),
(420281000000,'大冶市',420200000000,3),
(420300000000,'十堰市',420000000000,2),
(420301000000,'市辖区',420300000000,3),
(420302000000,'茅箭区',420300000000,3),
(420303000000,'张湾区',420300000000,3),
(420304000000,'郧阳区',420300000000,3),
(420322000000,'郧西县',420300000000,3),
(420323000000,'竹山县',420300000000,3),
(420324000000,'竹溪县',420300000000,3),
(420325000000,'房县',420300000000,3),
(420381000000,'丹江口市',420300000000,3),
(420500000000,'宜昌市',420000000000,2),
(420501000000,'市辖区',420500000000,3),
(420502000000,'西陵区',420500000000,3),
(420503000000,'伍家岗区',420500000000,3),
(420504000000,'点军区',420500000000,3),
(420505000000,'猇亭区',420500000000,3),
(420506000000,'夷陵区',420500000000,3),
(420525000000,'远安县',420500000000,3),
(420526000000,'兴山县',420500000000,3),
(420527000000,'秭归县',420500000000,3),
(420528000000,'长阳土家族自治县',420500000000,3),
(420529000000,'五峰土家族自治县',420500000000,3),
(420581000000,'宜都市',420500000000,3),
(420582000000,'当阳市',420500000000,3),
(420583000000,'枝江市',420500000000,3),
(420600000000,'襄阳市',420000000000,2),
(420601000000,'市辖区',420600000000,3),
(420602000000,'襄城区',420600000000,3),
(420606000000,'樊城区',420600000000,3),
(420607000000,'襄州区',420600000000,3),
(420624000000,'南漳县',420600000000,3),
(420625000000,'谷城县',420600000000,3),
(420626000000,'保康县',420600000000,3),
(420682000000,'老河口市',420600000000,3),
(420683000000,'枣阳市',420600000000,3),
(420684000000,'宜城市',420600000000,3),
(420700000000,'鄂州市',420000000000,2),
(420701000000,'市辖区',420700000000,3),
(420702000000,'梁子湖区',420700000000,3),
(420703000000,'华容区',420700000000,3),
(420704000000,'鄂城区',420700000000,3),
(420800000000,'荆门市',420000000000,2),
(420801000000,'市辖区',420800000000,3),
(420802000000,'东宝区',420800000000,3),
(420804000000,'掇刀区',420800000000,3),
(420822000000,'沙洋县',420800000000,3),
(420881000000,'钟祥市',420800000000,3),
(420882000000,'京山市',420800000000,3),
(420900000000,'孝感市',420000000000,2),
(420901000000,'市辖区',420900000000,3),
(420902000000,'孝南区',420900000000,3),
(420921000000,'孝昌县',420900000000,3),
(420922000000,'大悟县',420900000000,3),
(420923000000,'云梦县',420900000000,3),
(420981000000,'应城市',420900000000,3),
(420982000000,'安陆市',420900000000,3),
(420984000000,'汉川市',420900000000,3),
(421000000000,'荆州市',420000000000,2),
(421001000000,'市辖区',421000000000,3),
(421002000000,'沙市区',421000000000,3),
(421003000000,'荆州区',421000000000,3),
(421022000000,'公安县',421000000000,3),
(421023000000,'监利县',421000000000,3),
(421024000000,'江陵县',421000000000,3),
(421071000000,'荆州经济技术开发区',421000000000,3),
(421081000000,'石首市',421000000000,3),
(421083000000,'洪湖市',421000000000,3),
(421087000000,'松滋市',421000000000,3),
(421100000000,'黄冈市',420000000000,2),
(421101000000,'市辖区',421100000000,3),
(421102000000,'黄州区',421100000000,3),
(421121000000,'团风县',421100000000,3),
(421122000000,'红安县',421100000000,3),
(421123000000,'罗田县',421100000000,3),
(421124000000,'英山县',421100000000,3),
(421125000000,'浠水县',421100000000,3),
(421126000000,'蕲春县',421100000000,3),
(421127000000,'黄梅县',421100000000,3),
(421171000000,'龙感湖管理区',421100000000,3),
(421181000000,'麻城市',421100000000,3),
(421182000000,'武穴市',421100000000,3),
(421200000000,'咸宁市',420000000000,2),
(421201000000,'市辖区',421200000000,3),
(421202000000,'咸安区',421200000000,3),
(421221000000,'嘉鱼县',421200000000,3),
(421222000000,'通城县',421200000000,3),
(421223000000,'崇阳县',421200000000,3),
(421224000000,'通山县',421200000000,3),
(421281000000,'赤壁市',421200000000,3),
(421300000000,'随州市',420000000000,2),
(421301000000,'市辖区',421300000000,3),
(421303000000,'曾都区',421300000000,3),
(421321000000,'随县',421300000000,3),
(421381000000,'广水市',421300000000,3),
(422800000000,'恩施土家族苗族自治州',420000000000,2),
(422801000000,'恩施市',422800000000,3),
(422802000000,'利川市',422800000000,3),
(422822000000,'建始县',422800000000,3),
(422823000000,'巴东县',422800000000,3),
(422825000000,'宣恩县',422800000000,3),
(422826000000,'咸丰县',422800000000,3),
(422827000000,'来凤县',422800000000,3),
(422828000000,'鹤峰县',422800000000,3),
(429000000000,'省直辖县级行政区划',420000000000,2),
(429004000000,'仙桃市',429000000000,3),
(429005000000,'潜江市',429000000000,3),
(429006000000,'天门市',429000000000,3),
(429021000000,'神农架林区',429000000000,3),
(430000000000,'湖南省',0,1),
(430100000000,'长沙市',430000000000,2),
(430101000000,'市辖区',430100000000,3),
(430102000000,'芙蓉区',430100000000,3),
(430103000000,'天心区',430100000000,3),
(430104000000,'岳麓区',430100000000,3),
(430105000000,'开福区',430100000000,3),
(430111000000,'雨花区',430100000000,3),
(430112000000,'望城区',430100000000,3),
(430121000000,'长沙县',430100000000,3),
(430181000000,'浏阳市',430100000000,3),
(430182000000,'宁乡市',430100000000,3),
(430200000000,'株洲市',430000000000,2),
(430201000000,'市辖区',430200000000,3),
(430202000000,'荷塘区',430200000000,3),
(430203000000,'芦淞区',430200000000,3),
(430204000000,'石峰区',430200000000,3),
(430211000000,'天元区',430200000000,3),
(430212000000,'渌口区',430200000000,3),
(430223000000,'攸县',430200000000,3),
(430224000000,'茶陵县',430200000000,3),
(430225000000,'炎陵县',430200000000,3),
(430271000000,'云龙示范区',430200000000,3),
(430281000000,'醴陵市',430200000000,3),
(430300000000,'湘潭市',430000000000,2),
(430301000000,'市辖区',430300000000,3),
(430302000000,'雨湖区',430300000000,3),
(430304000000,'岳塘区',430300000000,3),
(430321000000,'湘潭县',430300000000,3),
(430371000000,'湖南湘潭高新技术产业园区',430300000000,3),
(430372000000,'湘潭昭山示范区',430300000000,3),
(430373000000,'湘潭九华示范区',430300000000,3),
(430381000000,'湘乡市',430300000000,3),
(430382000000,'韶山市',430300000000,3),
(430400000000,'衡阳市',430000000000,2),
(430401000000,'市辖区',430400000000,3),
(430405000000,'珠晖区',430400000000,3),
(430406000000,'雁峰区',430400000000,3),
(430407000000,'石鼓区',430400000000,3),
(430408000000,'蒸湘区',430400000000,3),
(430412000000,'南岳区',430400000000,3),
(430421000000,'衡阳县',430400000000,3),
(430422000000,'衡南县',430400000000,3),
(430423000000,'衡山县',430400000000,3),
(430424000000,'衡东县',430400000000,3),
(430426000000,'祁东县',430400000000,3),
(430471000000,'衡阳综合保税区',430400000000,3),
(430472000000,'湖南衡阳高新技术产业园区',430400000000,3),
(430473000000,'湖南衡阳松木经济开发区',430400000000,3),
(430481000000,'耒阳市',430400000000,3),
(430482000000,'常宁市',430400000000,3),
(430500000000,'邵阳市',430000000000,2),
(430501000000,'市辖区',430500000000,3),
(430502000000,'双清区',430500000000,3),
(430503000000,'大祥区',430500000000,3),
(430511000000,'北塔区',430500000000,3),
(430521000000,'邵东县',430500000000,3),
(430522000000,'新邵县',430500000000,3),
(430523000000,'邵阳县',430500000000,3),
(430524000000,'隆回县',430500000000,3),
(430525000000,'洞口县',430500000000,3),
(430527000000,'绥宁县',430500000000,3),
(430528000000,'新宁县',430500000000,3),
(430529000000,'城步苗族自治县',430500000000,3),
(430581000000,'武冈市',430500000000,3),
(430600000000,'岳阳市',430000000000,2),
(430601000000,'市辖区',430600000000,3),
(430602000000,'岳阳楼区',430600000000,3),
(430603000000,'云溪区',430600000000,3),
(430611000000,'君山区',430600000000,3),
(430621000000,'岳阳县',430600000000,3),
(430623000000,'华容县',430600000000,3),
(430624000000,'湘阴县',430600000000,3),
(430626000000,'平江县',430600000000,3),
(430671000000,'岳阳市屈原管理区',430600000000,3),
(430681000000,'汨罗市',430600000000,3),
(430682000000,'临湘市',430600000000,3),
(430700000000,'常德市',430000000000,2),
(430701000000,'市辖区',430700000000,3),
(430702000000,'武陵区',430700000000,3),
(430703000000,'鼎城区',430700000000,3),
(430721000000,'安乡县',430700000000,3),
(430722000000,'汉寿县',430700000000,3),
(430723000000,'澧县',430700000000,3),
(430724000000,'临澧县',430700000000,3),
(430725000000,'桃源县',430700000000,3),
(430726000000,'石门县',430700000000,3),
(430771000000,'常德市西洞庭管理区',430700000000,3),
(430781000000,'津市市',430700000000,3),
(430800000000,'张家界市',430000000000,2),
(430801000000,'市辖区',430800000000,3),
(430802000000,'永定区',430800000000,3),
(430811000000,'武陵源区',430800000000,3),
(430821000000,'慈利县',430800000000,3),
(430822000000,'桑植县',430800000000,3),
(430900000000,'益阳市',430000000000,2),
(430901000000,'市辖区',430900000000,3),
(430902000000,'资阳区',430900000000,3),
(430903000000,'赫山区',430900000000,3),
(430921000000,'南县',430900000000,3),
(430922000000,'桃江县',430900000000,3),
(430923000000,'安化县',430900000000,3),
(430971000000,'益阳市大通湖管理区',430900000000,3),
(430972000000,'湖南益阳高新技术产业园区',430900000000,3),
(430981000000,'沅江市',430900000000,3),
(431000000000,'郴州市',430000000000,2),
(431001000000,'市辖区',431000000000,3),
(431002000000,'北湖区',431000000000,3),
(431003000000,'苏仙区',431000000000,3),
(431021000000,'桂阳县',431000000000,3),
(431022000000,'宜章县',431000000000,3),
(431023000000,'永兴县',431000000000,3),
(431024000000,'嘉禾县',431000000000,3),
(431025000000,'临武县',431000000000,3),
(431026000000,'汝城县',431000000000,3),
(431027000000,'桂东县',431000000000,3),
(431028000000,'安仁县',431000000000,3),
(431081000000,'资兴市',431000000000,3),
(431100000000,'永州市',430000000000,2),
(431101000000,'市辖区',431100000000,3),
(431102000000,'零陵区',431100000000,3),
(431103000000,'冷水滩区',431100000000,3),
(431121000000,'祁阳县',431100000000,3),
(431122000000,'东安县',431100000000,3),
(431123000000,'双牌县',431100000000,3),
(431124000000,'道县',431100000000,3),
(431125000000,'江永县',431100000000,3),
(431126000000,'宁远县',431100000000,3),
(431127000000,'蓝山县',431100000000,3),
(431128000000,'新田县',431100000000,3),
(431129000000,'江华瑶族自治县',431100000000,3),
(431171000000,'永州经济技术开发区',431100000000,3),
(431172000000,'永州市金洞管理区',431100000000,3),
(431173000000,'永州市回龙圩管理区',431100000000,3),
(431200000000,'怀化市',430000000000,2),
(431201000000,'市辖区',431200000000,3),
(431202000000,'鹤城区',431200000000,3),
(431221000000,'中方县',431200000000,3),
(431222000000,'沅陵县',431200000000,3),
(431223000000,'辰溪县',431200000000,3),
(431224000000,'溆浦县',431200000000,3),
(431225000000,'会同县',431200000000,3),
(431226000000,'麻阳苗族自治县',431200000000,3),
(431227000000,'新晃侗族自治县',431200000000,3),
(431228000000,'芷江侗族自治县',431200000000,3),
(431229000000,'靖州苗族侗族自治县',431200000000,3),
(431230000000,'通道侗族自治县',431200000000,3),
(431271000000,'怀化市洪江管理区',431200000000,3),
(431281000000,'洪江市',431200000000,3),
(431300000000,'娄底市',430000000000,2),
(431301000000,'市辖区',431300000000,3),
(431302000000,'娄星区',431300000000,3),
(431321000000,'双峰县',431300000000,3),
(431322000000,'新化县',431300000000,3),
(431381000000,'冷水江市',431300000000,3),
(431382000000,'涟源市',431300000000,3),
(433100000000,'湘西土家族苗族自治州',430000000000,2),
(433101000000,'吉首市',433100000000,3),
(433122000000,'泸溪县',433100000000,3),
(433123000000,'凤凰县',433100000000,3),
(433124000000,'花垣县',433100000000,3),
(433125000000,'保靖县',433100000000,3),
(433126000000,'古丈县',433100000000,3),
(433127000000,'永顺县',433100000000,3),
(433130000000,'龙山县',433100000000,3),
(433172000000,'湖南吉首经济开发区',433100000000,3),
(433173000000,'湖南永顺经济开发区',433100000000,3),
(440000000000,'广东省',0,1),
(440100000000,'广州市',440000000000,2),
(440101000000,'市辖区',440100000000,3),
(440103000000,'荔湾区',440100000000,3),
(440104000000,'越秀区',440100000000,3),
(440105000000,'海珠区',440100000000,3),
(440106000000,'天河区',440100000000,3),
(440111000000,'白云区',440100000000,3),
(440112000000,'黄埔区',440100000000,3),
(440113000000,'番禺区',440100000000,3),
(440114000000,'花都区',440100000000,3),
(440115000000,'南沙区',440100000000,3),
(440117000000,'从化区',440100000000,3),
(440118000000,'增城区',440100000000,3),
(440200000000,'韶关市',440000000000,2),
(440201000000,'市辖区',440200000000,3),
(440203000000,'武江区',440200000000,3),
(440204000000,'浈江区',440200000000,3),
(440205000000,'曲江区',440200000000,3),
(440222000000,'始兴县',440200000000,3),
(440224000000,'仁化县',440200000000,3),
(440229000000,'翁源县',440200000000,3),
(440232000000,'乳源瑶族自治县',440200000000,3),
(440233000000,'新丰县',440200000000,3),
(440281000000,'乐昌市',440200000000,3),
(440282000000,'南雄市',440200000000,3),
(440300000000,'深圳市',440000000000,2),
(440301000000,'市辖区',440300000000,3),
(440303000000,'罗湖区',440300000000,3),
(440304000000,'福田区',440300000000,3),
(440305000000,'南山区',440300000000,3),
(440306000000,'宝安区',440300000000,3),
(440307000000,'龙岗区',440300000000,3),
(440308000000,'盐田区',440300000000,3),
(440309000000,'龙华区',440300000000,3),
(440310000000,'坪山区',440300000000,3),
(440311000000,'光明区',440300000000,3),
(440400000000,'珠海市',440000000000,2),
(440401000000,'市辖区',440400000000,3),
(440402000000,'香洲区',440400000000,3),
(440403000000,'斗门区',440400000000,3),
(440404000000,'金湾区',440400000000,3),
(440500000000,'汕头市',440000000000,2),
(440501000000,'市辖区',440500000000,3),
(440507000000,'龙湖区',440500000000,3),
(440511000000,'金平区',440500000000,3),
(440512000000,'濠江区',440500000000,3),
(440513000000,'潮阳区',440500000000,3),
(440514000000,'潮南区',440500000000,3),
(440515000000,'澄海区',440500000000,3),
(440523000000,'南澳县',440500000000,3),
(440600000000,'佛山市',440000000000,2),
(440601000000,'市辖区',440600000000,3),
(440604000000,'禅城区',440600000000,3),
(440605000000,'南海区',440600000000,3),
(440606000000,'顺德区',440600000000,3),
(440607000000,'三水区',440600000000,3),
(440608000000,'高明区',440600000000,3),
(440700000000,'江门市',440000000000,2),
(440701000000,'市辖区',440700000000,3),
(440703000000,'蓬江区',440700000000,3),
(440704000000,'江海区',440700000000,3),
(440705000000,'新会区',440700000000,3),
(440781000000,'台山市',440700000000,3),
(440783000000,'开平市',440700000000,3),
(440784000000,'鹤山市',440700000000,3),
(440785000000,'恩平市',440700000000,3),
(440800000000,'湛江市',440000000000,2),
(440801000000,'市辖区',440800000000,3),
(440802000000,'赤坎区',440800000000,3),
(440803000000,'霞山区',440800000000,3),
(440804000000,'坡头区',440800000000,3),
(440811000000,'麻章区',440800000000,3),
(440823000000,'遂溪县',440800000000,3),
(440825000000,'徐闻县',440800000000,3),
(440881000000,'廉江市',440800000000,3),
(440882000000,'雷州市',440800000000,3),
(440883000000,'吴川市',440800000000,3),
(440900000000,'茂名市',440000000000,2),
(440901000000,'市辖区',440900000000,3),
(440902000000,'茂南区',440900000000,3),
(440904000000,'电白区',440900000000,3),
(440981000000,'高州市',440900000000,3),
(440982000000,'化州市',440900000000,3),
(440983000000,'信宜市',440900000000,3),
(441200000000,'肇庆市',440000000000,2),
(441201000000,'市辖区',441200000000,3),
(441202000000,'端州区',441200000000,3),
(441203000000,'鼎湖区',441200000000,3),
(441204000000,'高要区',441200000000,3),
(441223000000,'广宁县',441200000000,3),
(441224000000,'怀集县',441200000000,3),
(441225000000,'封开县',441200000000,3),
(441226000000,'德庆县',441200000000,3),
(441284000000,'四会市',441200000000,3),
(441300000000,'惠州市',440000000000,2),
(441301000000,'市辖区',441300000000,3),
(441302000000,'惠城区',441300000000,3),
(441303000000,'惠阳区',441300000000,3),
(441322000000,'博罗县',441300000000,3),
(441323000000,'惠东县',441300000000,3),
(441324000000,'龙门县',441300000000,3),
(441400000000,'梅州市',440000000000,2),
(441401000000,'市辖区',441400000000,3),
(441402000000,'梅江区',441400000000,3),
(441403000000,'梅县区',441400000000,3),
(441422000000,'大埔县',441400000000,3),
(441423000000,'丰顺县',441400000000,3),
(441424000000,'五华县',441400000000,3),
(441426000000,'平远县',441400000000,3),
(441427000000,'蕉岭县',441400000000,3),
(441481000000,'兴宁市',441400000000,3),
(441500000000,'汕尾市',440000000000,2),
(441501000000,'市辖区',441500000000,3),
(441502000000,'城区',441500000000,3),
(441521000000,'海丰县',441500000000,3),
(441523000000,'陆河县',441500000000,3),
(441581000000,'陆丰市',441500000000,3),
(441600000000,'河源市',440000000000,2),
(441601000000,'市辖区',441600000000,3),
(441602000000,'源城区',441600000000,3),
(441621000000,'紫金县',441600000000,3),
(441622000000,'龙川县',441600000000,3),
(441623000000,'连平县',441600000000,3),
(441624000000,'和平县',441600000000,3),
(441625000000,'东源县',441600000000,3),
(441700000000,'阳江市',440000000000,2),
(441701000000,'市辖区',441700000000,3),
(441702000000,'江城区',441700000000,3),
(441704000000,'阳东区',441700000000,3),
(441721000000,'阳西县',441700000000,3),
(441781000000,'阳春市',441700000000,3),
(441800000000,'清远市',440000000000,2),
(441801000000,'市辖区',441800000000,3),
(441802000000,'清城区',441800000000,3),
(441803000000,'清新区',441800000000,3),
(441821000000,'佛冈县',441800000000,3),
(441823000000,'阳山县',441800000000,3),
(441825000000,'连山壮族瑶族自治县',441800000000,3),
(441826000000,'连南瑶族自治县',441800000000,3),
(441881000000,'英德市',441800000000,3),
(441882000000,'连州市',441800000000,3),
(441900000000,'东莞市',440000000000,2),
(441900003000,'东城街道办事处',441900000000,3),
(441900004000,'南城街道办事处',441900000000,3),
(441900005000,'万江街道办事处',441900000000,3),
(441900006000,'莞城街道办事处',441900000000,3),
(441900101000,'石碣镇',441900000000,3),
(441900102000,'石龙镇',441900000000,3),
(441900103000,'茶山镇',441900000000,3),
(441900104000,'石排镇',441900000000,3),
(441900105000,'企石镇',441900000000,3),
(441900106000,'横沥镇',441900000000,3),
(441900107000,'桥头镇',441900000000,3),
(441900108000,'谢岗镇',441900000000,3),
(441900109000,'东坑镇',441900000000,3),
(441900110000,'常平镇',441900000000,3),
(441900111000,'寮步镇',441900000000,3),
(441900112000,'樟木头镇',441900000000,3),
(441900113000,'大朗镇',441900000000,3),
(441900114000,'黄江镇',441900000000,3),
(441900115000,'清溪镇',441900000000,3),
(441900116000,'塘厦镇',441900000000,3),
(441900117000,'凤岗镇',441900000000,3),
(441900118000,'大岭山镇',441900000000,3),
(441900119000,'长安镇',441900000000,3),
(441900121000,'虎门镇',441900000000,3),
(441900122000,'厚街镇',441900000000,3),
(441900123000,'沙田镇',441900000000,3),
(441900124000,'道滘镇',441900000000,3),
(441900125000,'洪梅镇',441900000000,3),
(441900126000,'麻涌镇',441900000000,3),
(441900127000,'望牛墩镇',441900000000,3),
(441900128000,'中堂镇',441900000000,3),
(441900129000,'高埗镇',441900000000,3),
(441900401000,'松山湖管委会',441900000000,3),
(441900402000,'东莞港',441900000000,3),
(441900403000,'东莞生态园',441900000000,3),
(442000000000,'中山市',440000000000,2),
(442000001000,'石岐区街道办事处',442000000000,3),
(442000002000,'东区街道办事处',442000000000,3),
(442000003000,'火炬开发区街道办事处',442000000000,3),
(442000004000,'西区街道办事处',442000000000,3),
(442000005000,'南区街道办事处',442000000000,3),
(442000006000,'五桂山街道办事处',442000000000,3),
(442000100000,'小榄镇',442000000000,3),
(442000101000,'黄圃镇',442000000000,3),
(442000102000,'民众镇',442000000000,3),
(442000103000,'东凤镇',442000000000,3),
(442000104000,'东升镇',442000000000,3),
(442000105000,'古镇镇',442000000000,3),
(442000106000,'沙溪镇',442000000000,3),
(442000107000,'坦洲镇',442000000000,3),
(442000108000,'港口镇',442000000000,3),
(442000109000,'三角镇',442000000000,3),
(442000110000,'横栏镇',442000000000,3),
(442000111000,'南头镇',442000000000,3),
(442000112000,'阜沙镇',442000000000,3),
(442000113000,'南朗镇',442000000000,3),
(442000114000,'三乡镇',442000000000,3),
(442000115000,'板芙镇',442000000000,3),
(442000116000,'大涌镇',442000000000,3),
(442000117000,'神湾镇',442000000000,3),
(445100000000,'潮州市',440000000000,2),
(445101000000,'市辖区',445100000000,3),
(445102000000,'湘桥区',445100000000,3),
(445103000000,'潮安区',445100000000,3),
(445122000000,'饶平县',445100000000,3),
(445200000000,'揭阳市',440000000000,2),
(445201000000,'市辖区',445200000000,3),
(445202000000,'榕城区',445200000000,3),
(445203000000,'揭东区',445200000000,3),
(445222000000,'揭西县',445200000000,3),
(445224000000,'惠来县',445200000000,3),
(445281000000,'普宁市',445200000000,3),
(445300000000,'云浮市',440000000000,2),
(445301000000,'市辖区',445300000000,3),
(445302000000,'云城区',445300000000,3),
(445303000000,'云安区',445300000000,3),
(445321000000,'新兴县',445300000000,3),
(445322000000,'郁南县',445300000000,3),
(445381000000,'罗定市',445300000000,3),
(450000000000,'广西壮族自治区',0,1),
(450100000000,'南宁市',450000000000,2),
(450101000000,'市辖区',450100000000,3),
(450102000000,'兴宁区',450100000000,3),
(450103000000,'青秀区',450100000000,3),
(450105000000,'江南区',450100000000,3),
(450107000000,'西乡塘区',450000000000,3),
(450108000000,'良庆区',450100000000,3),
(450109000000,'邕宁区',450100000000,3),
(450110000000,'武鸣区',450100000000,3),
(450123000000,'隆安县',450100000000,3),
(450124000000,'马山县',450100000000,3),
(450125000000,'上林县',450100000000,3),
(450126000000,'宾阳县',450100000000,3),
(450127000000,'横县',450100000000,3),
(450200000000,'柳州市',450000000000,2),
(450201000000,'市辖区',450200000000,3),
(450202000000,'城中区',450200000000,3),
(450203000000,'鱼峰区',450200000000,3),
(450204000000,'柳南区',450200000000,3),
(450205000000,'柳北区',450200000000,3),
(450206000000,'柳江区',450200000000,3),
(450222000000,'柳城县',450200000000,3),
(450223000000,'鹿寨县',450200000000,3),
(450224000000,'融安县',450200000000,3),
(450225000000,'融水苗族自治县',450200000000,3),
(450226000000,'三江侗族自治县',450200000000,3),
(450300000000,'桂林市',450000000000,2),
(450301000000,'市辖区',450300000000,3),
(450302000000,'秀峰区',450300000000,3),
(450303000000,'叠彩区',450300000000,3),
(450304000000,'象山区',450300000000,3),
(450305000000,'七星区',450300000000,3),
(450311000000,'雁山区',450300000000,3),
(450312000000,'临桂区',450300000000,3),
(450321000000,'阳朔县',450300000000,3),
(450323000000,'灵川县',450300000000,3),
(450324000000,'全州县',450300000000,3),
(450325000000,'兴安县',450300000000,3),
(450326000000,'永福县',450300000000,3),
(450327000000,'灌阳县',450300000000,3),
(450328000000,'龙胜各族自治县',450300000000,3),
(450329000000,'资源县',450300000000,3),
(450330000000,'平乐县',450300000000,3),
(450332000000,'恭城瑶族自治县',450300000000,3),
(450381000000,'荔浦市',450300000000,3),
(450400000000,'梧州市',450000000000,2),
(450401000000,'市辖区',450400000000,3),
(450403000000,'万秀区',450400000000,3),
(450405000000,'长洲区',450400000000,3),
(450406000000,'龙圩区',450400000000,3),
(450421000000,'苍梧县',450400000000,3),
(450422000000,'藤县',450400000000,3),
(450423000000,'蒙山县',450400000000,3),
(450481000000,'岑溪市',450400000000,3),
(450500000000,'北海市',450000000000,2),
(450501000000,'市辖区',450500000000,3),
(450502000000,'海城区',450500000000,3),
(450503000000,'银海区',450500000000,3),
(450512000000,'铁山港区',450500000000,3),
(450521000000,'合浦县',450500000000,3),
(450600000000,'防城港市',450000000000,2),
(450601000000,'市辖区',450600000000,3),
(450602000000,'港口区',450600000000,3),
(450603000000,'防城区',450600000000,3),
(450621000000,'上思县',450600000000,3),
(450681000000,'东兴市',450600000000,3),
(450700000000,'钦州市',450000000000,2),
(450701000000,'市辖区',450700000000,3),
(450702000000,'钦南区',450700000000,3),
(450703000000,'钦北区',450700000000,3),
(450721000000,'灵山县',450700000000,3),
(450722000000,'浦北县',450700000000,3),
(450800000000,'贵港市',450000000000,2),
(450801000000,'市辖区',450800000000,3),
(450802000000,'港北区',450800000000,3),
(450803000000,'港南区',450800000000,3),
(450804000000,'覃塘区',450800000000,3),
(450821000000,'平南县',450800000000,3),
(450881000000,'桂平市',450800000000,3),
(450900000000,'玉林市',450000000000,2),
(450901000000,'市辖区',450900000000,3),
(450902000000,'玉州区',450900000000,3),
(450903000000,'福绵区',450900000000,3),
(450921000000,'容县',450900000000,3),
(450922000000,'陆川县',450900000000,3),
(450923000000,'博白县',450900000000,3),
(450924000000,'兴业县',450900000000,3),
(450981000000,'北流市',450900000000,3),
(451000000000,'百色市',450000000000,2),
(451001000000,'市辖区',451000000000,3),
(451002000000,'右江区',451000000000,3),
(451021000000,'田阳县',451000000000,3),
(451022000000,'田东县',451000000000,3),
(451023000000,'平果县',451000000000,3),
(451024000000,'德保县',451000000000,3),
(451026000000,'那坡县',451000000000,3),
(451027000000,'凌云县',451000000000,3),
(451028000000,'乐业县',451000000000,3),
(451029000000,'田林县',451000000000,3),
(451030000000,'西林县',451000000000,3),
(451031000000,'隆林各族自治县',451000000000,3),
(451081000000,'靖西市',451000000000,3),
(451100000000,'贺州市',450000000000,2),
(451101000000,'市辖区',451100000000,3),
(451102000000,'八步区',451100000000,3),
(451103000000,'平桂区',451100000000,3),
(451121000000,'昭平县',451100000000,3),
(451122000000,'钟山县',451100000000,3),
(451123000000,'富川瑶族自治县',451100000000,3),
(451200000000,'河池市',450000000000,2),
(451201000000,'市辖区',451200000000,3),
(451202000000,'金城江区',451200000000,3),
(451203000000,'宜州区',451200000000,3),
(451221000000,'南丹县',451200000000,3),
(451222000000,'天峨县',451200000000,3),
(451223000000,'凤山县',451200000000,3),
(451224000000,'东兰县',451200000000,3),
(451225000000,'罗城仫佬族自治县',451200000000,3),
(451226000000,'环江毛南族自治县',451200000000,3),
(451227000000,'巴马瑶族自治县',451200000000,3),
(451228000000,'都安瑶族自治县',451200000000,3),
(451229000000,'大化瑶族自治县',451200000000,3),
(451300000000,'来宾市',450000000000,2),
(451301000000,'市辖区',451300000000,3),
(451302000000,'兴宾区',451300000000,3),
(451321000000,'忻城县',451300000000,3),
(451322000000,'象州县',451300000000,3),
(451323000000,'武宣县',451300000000,3),
(451324000000,'金秀瑶族自治县',451300000000,3),
(451381000000,'合山市',451300000000,3),
(451400000000,'崇左市',450000000000,2),
(451401000000,'市辖区',451400000000,3),
(451402000000,'江州区',451400000000,3),
(451421000000,'扶绥县',451400000000,3),
(451422000000,'宁明县',451400000000,3),
(451423000000,'龙州县',451400000000,3),
(451424000000,'大新县',451400000000,3),
(451425000000,'天等县',451400000000,3),
(451481000000,'凭祥市',451400000000,3),
(460000000000,'海南省',0,1),
(460100000000,'海口市',460000000000,2),
(460101000000,'市辖区',460100000000,3),
(460105000000,'秀英区',460100000000,3),
(460106000000,'龙华区',460100000000,3),
(460107000000,'琼山区',460100000000,3),
(460108000000,'美兰区',460100000000,3),
(460200000000,'三亚市',460000000000,2),
(460201000000,'市辖区',460200000000,3),
(460202000000,'海棠区',460200000000,3),
(460203000000,'吉阳区',460200000000,3),
(460204000000,'天涯区',460200000000,3),
(460205000000,'崖州区',460200000000,3),
(460300000000,'三沙市',460000000000,2),
(460321000000,'西沙群岛',460300000000,3),
(460322000000,'南沙群岛',460300000000,3),
(460323000000,'中沙群岛的岛礁及其海域',460300000000,3),
(460400000000,'儋州市',460000000000,2),
(469000000000,'省直辖县级行政区划',460000000000,2),
(469001000000,'五指山市',469000000000,3),
(469002000000,'琼海市',469000000000,3),
(469005000000,'文昌市',469000000000,3),
(469006000000,'万宁市',469000000000,3),
(469007000000,'东方市',469000000000,3),
(469021000000,'定安县',469000000000,3),
(469022000000,'屯昌县',469000000000,3),
(469023000000,'澄迈县',469000000000,3),
(469024000000,'临高县',469000000000,3),
(469025000000,'白沙黎族自治县',469000000000,3),
(469026000000,'昌江黎族自治县',469000000000,3),
(469027000000,'乐东黎族自治县',469000000000,3),
(469028000000,'陵水黎族自治县',469000000000,3),
(469029000000,'保亭黎族苗族自治县',469000000000,3),
(469030000000,'琼中黎族苗族自治县',469000000000,3),
(500000000000,'重庆市',0,1),
(500100000000,'市辖区',500000000000,2),
(500101000000,'万州区',500100000000,3),
(500102000000,'涪陵区',500100000000,3),
(500103000000,'渝中区',500100000000,3),
(500104000000,'大渡口区',500100000000,3),
(500105000000,'江北区',500100000000,3),
(500106000000,'沙坪坝区',500100000000,3),
(500107000000,'九龙坡区',500100000000,3),
(500108000000,'南岸区',500100000000,3),
(500109000000,'北碚区',500100000000,3),
(500110000000,'綦江区',500100000000,3),
(500111000000,'大足区',500100000000,3),
(500112000000,'渝北区',500100000000,3),
(500113000000,'巴南区',500100000000,3),
(500114000000,'黔江区',500100000000,3),
(500115000000,'长寿区',500100000000,3),
(500116000000,'江津区',500100000000,3),
(500117000000,'合川区',500100000000,3),
(500118000000,'永川区',500100000000,3),
(500119000000,'南川区',500100000000,3),
(500120000000,'璧山区',500100000000,3),
(500151000000,'铜梁区',500100000000,3),
(500152000000,'潼南区',500100000000,3),
(500153000000,'荣昌区',500100000000,3),
(500154000000,'开州区',500100000000,3),
(500155000000,'梁平区',500100000000,3),
(500156000000,'武隆区',500100000000,3),
(500200000000,'县',500000000000,2),
(500229000000,'城口县',500200000000,3),
(500230000000,'丰都县',500200000000,3),
(500231000000,'垫江县',500200000000,3),
(500233000000,'忠县',500200000000,3),
(500235000000,'云阳县',500200000000,3),
(500236000000,'奉节县',500200000000,3),
(500237000000,'巫山县',500200000000,3),
(500238000000,'巫溪县',500200000000,3),
(500240000000,'石柱土家族自治县',500200000000,3),
(500241000000,'秀山土家族苗族自治县',500200000000,3),
(500242000000,'酉阳土家族苗族自治县',500200000000,3),
(500243000000,'彭水苗族土家族自治县',500200000000,3),
(510000000000,'四川省',0,1),
(510100000000,'成都市',510000000000,2),
(510101000000,'市辖区',510100000000,3),
(510104000000,'锦江区',510100000000,3),
(510105000000,'青羊区',510100000000,3),
(510106000000,'金牛区',510100000000,3),
(510107000000,'武侯区',510100000000,3),
(510108000000,'成华区',510100000000,3),
(510112000000,'龙泉驿区',510100000000,3),
(510113000000,'青白江区',510100000000,3),
(510114000000,'新都区',510100000000,3),
(510115000000,'温江区',510100000000,3),
(510116000000,'双流区',510100000000,3),
(510117000000,'郫都区',510100000000,3),
(510121000000,'金堂县',510100000000,3),
(510129000000,'大邑县',510100000000,3),
(510131000000,'蒲江县',510100000000,3),
(510132000000,'新津县',510100000000,3),
(510181000000,'都江堰市',510100000000,3),
(510182000000,'彭州市',510100000000,3),
(510183000000,'邛崃市',510100000000,3),
(510184000000,'崇州市',510100000000,3),
(510185000000,'简阳市',510100000000,3),
(510300000000,'自贡市',510000000000,2),
(510301000000,'市辖区',510300000000,3),
(510302000000,'自流井区',510300000000,3),
(510303000000,'贡井区',510300000000,3),
(510304000000,'大安区',510300000000,3),
(510311000000,'沿滩区',510300000000,3),
(510321000000,'荣县',510300000000,3),
(510322000000,'富顺县',510300000000,3),
(510400000000,'攀枝花市',510000000000,2),
(510401000000,'市辖区',510400000000,3),
(510402000000,'东区',510400000000,3),
(510403000000,'西区',510400000000,3),
(510411000000,'仁和区',510400000000,3),
(510421000000,'米易县',510400000000,3),
(510422000000,'盐边县',510400000000,3),
(510500000000,'泸州市',510000000000,2),
(510501000000,'市辖区',510500000000,3),
(510502000000,'江阳区',510500000000,3),
(510503000000,'纳溪区',510500000000,3),
(510504000000,'龙马潭区',510500000000,3),
(510521000000,'泸县',510500000000,3),
(510522000000,'合江县',510500000000,3),
(510524000000,'叙永县',510500000000,3),
(510525000000,'古蔺县',510500000000,3),
(510600000000,'德阳市',510000000000,2),
(510601000000,'市辖区',510600000000,3),
(510603000000,'旌阳区',510600000000,3),
(510604000000,'罗江区',510600000000,3),
(510623000000,'中江县',510600000000,3),
(510681000000,'广汉市',510600000000,3),
(510682000000,'什邡市',510600000000,3),
(510683000000,'绵竹市',510600000000,3),
(510700000000,'绵阳市',510000000000,2),
(510701000000,'市辖区',510700000000,3),
(510703000000,'涪城区',510700000000,3),
(510704000000,'游仙区',510700000000,3),
(510705000000,'安州区',510700000000,3),
(510722000000,'三台县',510700000000,3),
(510723000000,'盐亭县',510700000000,3),
(510725000000,'梓潼县',510700000000,3),
(510726000000,'北川羌族自治县',510700000000,3),
(510727000000,'平武县',510700000000,3),
(510781000000,'江油市',510700000000,3),
(510800000000,'广元市',510000000000,2),
(510801000000,'市辖区',510800000000,3),
(510802000000,'利州区',510800000000,3),
(510811000000,'昭化区',510800000000,3),
(510812000000,'朝天区',510800000000,3),
(510821000000,'旺苍县',510800000000,3),
(510822000000,'青川县',510800000000,3),
(510823000000,'剑阁县',510800000000,3),
(510824000000,'苍溪县',510800000000,3),
(510900000000,'遂宁市',510000000000,2),
(510901000000,'市辖区',510900000000,3),
(510903000000,'船山区',510900000000,3),
(510904000000,'安居区',510900000000,3),
(510921000000,'蓬溪县',510900000000,3),
(510922000000,'射洪县',510900000000,3),
(510923000000,'大英县',510900000000,3),
(511000000000,'内江市',510000000000,2),
(511001000000,'市辖区',511000000000,3),
(511002000000,'市中区',511000000000,3),
(511011000000,'东兴区',511000000000,3),
(511024000000,'威远县',511000000000,3),
(511025000000,'资中县',511000000000,3),
(511071000000,'内江经济开发区',511000000000,3),
(511083000000,'隆昌市',511000000000,3),
(511100000000,'乐山市',510000000000,2),
(511101000000,'市辖区',511100000000,3),
(511102000000,'市中区',511100000000,3),
(511111000000,'沙湾区',511100000000,3),
(511112000000,'五通桥区',511100000000,3),
(511113000000,'金口河区',511100000000,3),
(511123000000,'犍为县',511100000000,3),
(511124000000,'井研县',511100000000,3),
(511126000000,'夹江县',511100000000,3),
(511129000000,'沐川县',511100000000,3),
(511132000000,'峨边彝族自治县',511100000000,3),
(511133000000,'马边彝族自治县',511100000000,3),
(511181000000,'峨眉山市',511100000000,3),
(511300000000,'南充市',510000000000,2),
(511301000000,'市辖区',511300000000,3),
(511302000000,'顺庆区',511300000000,3),
(511303000000,'高坪区',511300000000,3),
(511304000000,'嘉陵区',511300000000,3),
(511321000000,'南部县',511300000000,3),
(511322000000,'营山县',511300000000,3),
(511323000000,'蓬安县',511300000000,3),
(511324000000,'仪陇县',511300000000,3),
(511325000000,'西充县',511300000000,3),
(511381000000,'阆中市',511300000000,3),
(511400000000,'眉山市',510000000000,2),
(511401000000,'市辖区',511400000000,3),
(511402000000,'东坡区',511400000000,3),
(511403000000,'彭山区',511400000000,3),
(511421000000,'仁寿县',511400000000,3),
(511423000000,'洪雅县',511400000000,3),
(511424000000,'丹棱县',511400000000,3),
(511425000000,'青神县',511400000000,3),
(511500000000,'宜宾市',510000000000,2),
(511501000000,'市辖区',511500000000,3),
(511502000000,'翠屏区',511500000000,3),
(511503000000,'南溪区',511500000000,3),
(511504000000,'叙州区',511500000000,3),
(511523000000,'江安县',511500000000,3),
(511524000000,'长宁县',511500000000,3),
(511525000000,'高县',511500000000,3),
(511526000000,'珙县',511500000000,3),
(511527000000,'筠连县',511500000000,3),
(511528000000,'兴文县',511500000000,3),
(511529000000,'屏山县',511500000000,3),
(511600000000,'广安市',510000000000,2),
(511601000000,'市辖区',511600000000,3),
(511602000000,'广安区',511600000000,3),
(511603000000,'前锋区',511600000000,3),
(511621000000,'岳池县',511600000000,3),
(511622000000,'武胜县',511600000000,3),
(511623000000,'邻水县',511600000000,3),
(511681000000,'华蓥市',511600000000,3),
(511700000000,'达州市',510000000000,2),
(511701000000,'市辖区',511700000000,3),
(511702000000,'通川区',511700000000,3),
(511703000000,'达川区',511700000000,3),
(511722000000,'宣汉县',511700000000,3),
(511723000000,'开江县',511700000000,3),
(511724000000,'大竹县',511700000000,3),
(511725000000,'渠县',511700000000,3),
(511771000000,'达州经济开发区',511700000000,3),
(511781000000,'万源市',511700000000,3),
(511800000000,'雅安市',510000000000,2),
(511801000000,'市辖区',511800000000,3),
(511802000000,'雨城区',511800000000,3),
(511803000000,'名山区',511800000000,3),
(511822000000,'荥经县',511800000000,3),
(511823000000,'汉源县',511800000000,3),
(511824000000,'石棉县',511800000000,3),
(511825000000,'天全县',511800000000,3),
(511826000000,'芦山县',511800000000,3),
(511827000000,'宝兴县',511800000000,3),
(511900000000,'巴中市',510000000000,2),
(511901000000,'市辖区',511900000000,3),
(511902000000,'巴州区',511900000000,3),
(511903000000,'恩阳区',511900000000,3),
(511921000000,'通江县',511900000000,3),
(511922000000,'南江县',511900000000,3),
(511923000000,'平昌县',511900000000,3),
(511971000000,'巴中经济开发区',511900000000,3),
(512000000000,'资阳市',510000000000,2),
(512001000000,'市辖区',512000000000,3),
(512002000000,'雁江区',512000000000,3),
(512021000000,'安岳县',512000000000,3),
(512022000000,'乐至县',512000000000,3),
(513200000000,'阿坝藏族羌族自治州',510000000000,2),
(513201000000,'马尔康市',513200000000,3),
(513221000000,'汶川县',513200000000,3),
(513222000000,'理县',513200000000,3),
(513223000000,'茂县',513200000000,3),
(513224000000,'松潘县',513200000000,3),
(513225000000,'九寨沟县',513200000000,3),
(513226000000,'金川县',513200000000,3),
(513227000000,'小金县',513200000000,3),
(513228000000,'黑水县',513200000000,3),
(513230000000,'壤塘县',513200000000,3),
(513231000000,'阿坝县',513200000000,3),
(513232000000,'若尔盖县',513200000000,3),
(513233000000,'红原县',513200000000,3),
(513300000000,'甘孜藏族自治州',510000000000,2),
(513301000000,'康定市',513300000000,3),
(513322000000,'泸定县',513300000000,3),
(513323000000,'丹巴县',513300000000,3),
(513324000000,'九龙县',513300000000,3),
(513325000000,'雅江县',513300000000,3),
(513326000000,'道孚县',513300000000,3),
(513327000000,'炉霍县',513300000000,3),
(513328000000,'甘孜县',513300000000,3),
(513329000000,'新龙县',513300000000,3),
(513330000000,'德格县',513300000000,3),
(513331000000,'白玉县',513300000000,3),
(513332000000,'石渠县',513300000000,3),
(513333000000,'色达县',513300000000,3),
(513334000000,'理塘县',513300000000,3),
(513335000000,'巴塘县',513300000000,3),
(513336000000,'乡城县',513300000000,3),
(513337000000,'稻城县',513300000000,3),
(513338000000,'得荣县',513300000000,3),
(513400000000,'凉山彝族自治州',510000000000,2),
(513401000000,'西昌市',513400000000,3),
(513422000000,'木里藏族自治县',513400000000,3),
(513423000000,'盐源县',513400000000,3),
(513424000000,'德昌县',513400000000,3),
(513425000000,'会理县',513400000000,3),
(513426000000,'会东县',513400000000,3),
(513427000000,'宁南县',513400000000,3),
(513428000000,'普格县',513400000000,3),
(513429000000,'布拖县',513400000000,3),
(513430000000,'金阳县',513400000000,3),
(513431000000,'昭觉县',513400000000,3),
(513432000000,'喜德县',513400000000,3),
(513433000000,'冕宁县',513400000000,3),
(513434000000,'越西县',513400000000,3),
(513435000000,'甘洛县',513400000000,3),
(513436000000,'美姑县',513400000000,3),
(513437000000,'雷波县',513400000000,3),
(520000000000,'贵州省',0,1),
(520100000000,'贵阳市',520000000000,2),
(520101000000,'市辖区',520100000000,3),
(520102000000,'南明区',520100000000,3),
(520103000000,'云岩区',520100000000,3),
(520111000000,'花溪区',520100000000,3),
(520112000000,'乌当区',520100000000,3),
(520113000000,'白云区',520100000000,3),
(520115000000,'观山湖区',520100000000,3),
(520121000000,'开阳县',520100000000,3),
(520122000000,'息烽县',520100000000,3),
(520123000000,'修文县',520100000000,3),
(520181000000,'清镇市',520100000000,3),
(520200000000,'六盘水市',520000000000,2),
(520201000000,'钟山区',520200000000,3),
(520203000000,'六枝特区',520200000000,3),
(520221000000,'水城县',520200000000,3),
(520281000000,'盘州市',520200000000,3),
(520300000000,'遵义市',520000000000,2),
(520301000000,'市辖区',520300000000,3),
(520302000000,'红花岗区',520300000000,3),
(520303000000,'汇川区',520300000000,3),
(520304000000,'播州区',520300000000,3),
(520322000000,'桐梓县',520300000000,3),
(520323000000,'绥阳县',520300000000,3),
(520324000000,'正安县',520300000000,3),
(520325000000,'道真仡佬族苗族自治县',520300000000,3),
(520326000000,'务川仡佬族苗族自治县',520300000000,3),
(520327000000,'凤冈县',520300000000,3),
(520328000000,'湄潭县',520300000000,3),
(520329000000,'余庆县',520300000000,3),
(520330000000,'习水县',520300000000,3),
(520381000000,'赤水市',520300000000,3),
(520382000000,'仁怀市',520300000000,3),
(520400000000,'安顺市',520000000000,2),
(520401000000,'市辖区',520400000000,3),
(520402000000,'西秀区',520400000000,3),
(520403000000,'平坝区',520400000000,3),
(520422000000,'普定县',520400000000,3),
(520423000000,'镇宁布依族苗族自治县',520400000000,3),
(520424000000,'关岭布依族苗族自治县',520400000000,3),
(520425000000,'紫云苗族布依族自治县',520400000000,3),
(520500000000,'毕节市',520000000000,2),
(520501000000,'市辖区',520500000000,3),
(520502000000,'七星关区',520500000000,3),
(520521000000,'大方县',520500000000,3),
(520522000000,'黔西县',520500000000,3),
(520523000000,'金沙县',520500000000,3),
(520524000000,'织金县',520500000000,3),
(520525000000,'纳雍县',520500000000,3),
(520526000000,'威宁彝族回族苗族自治县',520500000000,3),
(520527000000,'赫章县',520500000000,3),
(520600000000,'铜仁市',520000000000,2),
(520601000000,'市辖区',520600000000,3),
(520602000000,'碧江区',520600000000,3),
(520603000000,'万山区',520600000000,3),
(520621000000,'江口县',520600000000,3),
(520622000000,'玉屏侗族自治县',520600000000,3),
(520623000000,'石阡县',520600000000,3),
(520624000000,'思南县',520600000000,3),
(520625000000,'印江土家族苗族自治县',520600000000,3),
(520626000000,'德江县',520600000000,3),
(520627000000,'沿河土家族自治县',520600000000,3),
(520628000000,'松桃苗族自治县',520600000000,3),
(522300000000,'黔西南布依族苗族自治州',520000000000,2),
(522301000000,'兴义市',522300000000,3),
(522302000000,'兴仁市',522300000000,3),
(522323000000,'普安县',522300000000,3),
(522324000000,'晴隆县',522300000000,3),
(522325000000,'贞丰县',522300000000,3),
(522326000000,'望谟县',522300000000,3),
(522327000000,'册亨县',522300000000,3),
(522328000000,'安龙县',522300000000,3),
(522600000000,'黔东南苗族侗族自治州',520000000000,2),
(522601000000,'凯里市',522600000000,3),
(522622000000,'黄平县',522600000000,3),
(522623000000,'施秉县',522600000000,3),
(522624000000,'三穗县',522600000000,3),
(522625000000,'镇远县',522600000000,3),
(522626000000,'岑巩县',522600000000,3),
(522627000000,'天柱县',522600000000,3),
(522628000000,'锦屏县',522600000000,3),
(522629000000,'剑河县',522600000000,3),
(522630000000,'台江县',522600000000,3),
(522631000000,'黎平县',522600000000,3),
(522632000000,'榕江县',522600000000,3),
(522633000000,'从江县',522600000000,3),
(522634000000,'雷山县',522600000000,3),
(522635000000,'麻江县',522600000000,3),
(522636000000,'丹寨县',522600000000,3),
(522700000000,'黔南布依族苗族自治州',520000000000,2),
(522701000000,'都匀市',522700000000,3),
(522702000000,'福泉市',522700000000,3),
(522722000000,'荔波县',522700000000,3),
(522723000000,'贵定县',522700000000,3),
(522725000000,'瓮安县',522700000000,3),
(522726000000,'独山县',522700000000,3),
(522727000000,'平塘县',522700000000,3),
(522728000000,'罗甸县',522700000000,3),
(522729000000,'长顺县',522700000000,3),
(522730000000,'龙里县',522700000000,3),
(522731000000,'惠水县',522700000000,3),
(522732000000,'三都水族自治县',522700000000,3),
(530000000000,'云南省',0,1),
(530100000000,'昆明市',530000000000,2),
(530101000000,'市辖区',530100000000,3),
(530102000000,'五华区',530100000000,3),
(530103000000,'盘龙区',530100000000,3),
(530111000000,'官渡区',530100000000,3),
(530112000000,'西山区',530100000000,3),
(530113000000,'东川区',530100000000,3),
(530114000000,'呈贡区',530100000000,3),
(530115000000,'晋宁区',530100000000,3),
(530124000000,'富民县',530100000000,3),
(530125000000,'宜良县',530100000000,3),
(530126000000,'石林彝族自治县',530100000000,3),
(530127000000,'嵩明县',530100000000,3),
(530128000000,'禄劝彝族苗族自治县',530100000000,3),
(530129000000,'寻甸回族彝族自治县',530100000000,3),
(530181000000,'安宁市',530100000000,3),
(530300000000,'曲靖市',530000000000,2),
(530301000000,'市辖区',530300000000,3),
(530302000000,'麒麟区',530300000000,3),
(530303000000,'沾益区',530300000000,3),
(530304000000,'马龙区',530300000000,3),
(530322000000,'陆良县',530300000000,3),
(530323000000,'师宗县',530300000000,3),
(530324000000,'罗平县',530300000000,3),
(530325000000,'富源县',530300000000,3),
(530326000000,'会泽县',530300000000,3),
(530381000000,'宣威市',530300000000,3),
(530400000000,'玉溪市',530000000000,2),
(530401000000,'市辖区',530400000000,3),
(530402000000,'红塔区',530400000000,3),
(530403000000,'江川区',530400000000,3),
(530422000000,'澄江县',530400000000,3),
(530423000000,'通海县',530400000000,3),
(530424000000,'华宁县',530400000000,3),
(530425000000,'易门县',530400000000,3),
(530426000000,'峨山彝族自治县',530400000000,3),
(530427000000,'新平彝族傣族自治县',530400000000,3),
(530428000000,'元江哈尼族彝族傣族自治县',530400000000,3),
(530500000000,'保山市',530000000000,2),
(530501000000,'市辖区',530500000000,3),
(530502000000,'隆阳区',530500000000,3),
(530521000000,'施甸县',530500000000,3),
(530523000000,'龙陵县',530500000000,3),
(530524000000,'昌宁县',530500000000,3),
(530581000000,'腾冲市',530500000000,3),
(530600000000,'昭通市',530000000000,2),
(530601000000,'市辖区',530600000000,3),
(530602000000,'昭阳区',530600000000,3),
(530621000000,'鲁甸县',530600000000,3),
(530622000000,'巧家县',530600000000,3),
(530623000000,'盐津县',530600000000,3),
(530624000000,'大关县',530600000000,3),
(530625000000,'永善县',530600000000,3),
(530626000000,'绥江县',530600000000,3),
(530627000000,'镇雄县',530600000000,3),
(530628000000,'彝良县',530600000000,3),
(530629000000,'威信县',530600000000,3),
(530681000000,'水富市',530600000000,3),
(530700000000,'丽江市',530000000000,2),
(530701000000,'市辖区',530700000000,3),
(530702000000,'古城区',530700000000,3),
(530721000000,'玉龙纳西族自治县',530700000000,3),
(530722000000,'永胜县',530700000000,3),
(530723000000,'华坪县',530700000000,3),
(530724000000,'宁蒗彝族自治县',530700000000,3),
(530800000000,'普洱市',530000000000,2),
(530801000000,'市辖区',530800000000,3),
(530802000000,'思茅区',530800000000,3),
(530821000000,'宁洱哈尼族彝族自治县',530800000000,3),
(530822000000,'墨江哈尼族自治县',530800000000,3),
(530823000000,'景东彝族自治县',530800000000,3),
(530824000000,'景谷傣族彝族自治县',530800000000,3),
(530825000000,'镇沅彝族哈尼族拉祜族自治县',530800000000,3),
(530826000000,'江城哈尼族彝族自治县',530800000000,3),
(530827000000,'孟连傣族拉祜族佤族自治县',530800000000,3),
(530828000000,'澜沧拉祜族自治县',530800000000,3),
(530829000000,'西盟佤族自治县',530800000000,3),
(530900000000,'临沧市',530000000000,2),
(530901000000,'市辖区',530900000000,3),
(530902000000,'临翔区',530900000000,3),
(530921000000,'凤庆县',530900000000,3),
(530922000000,'云县',530900000000,3),
(530923000000,'永德县',530900000000,3),
(530924000000,'镇康县',530900000000,3),
(530925000000,'双江拉祜族佤族布朗族傣族自治县',530900000000,3),
(530926000000,'耿马傣族佤族自治县',530900000000,3),
(530927000000,'沧源佤族自治县',530900000000,3),
(532300000000,'楚雄彝族自治州',530000000000,2),
(532301000000,'楚雄市',532300000000,3),
(532322000000,'双柏县',532300000000,3),
(532323000000,'牟定县',532300000000,3),
(532324000000,'南华县',532300000000,3),
(532325000000,'姚安县',532300000000,3),
(532326000000,'大姚县',532300000000,3),
(532327000000,'永仁县',532300000000,3),
(532328000000,'元谋县',532300000000,3),
(532329000000,'武定县',532300000000,3),
(532331000000,'禄丰县',532300000000,3),
(532500000000,'红河哈尼族彝族自治州',530000000000,2),
(532501000000,'个旧市',532500000000,3),
(532502000000,'开远市',532500000000,3),
(532503000000,'蒙自市',532500000000,3),
(532504000000,'弥勒市',532500000000,3),
(532523000000,'屏边苗族自治县',532500000000,3),
(532524000000,'建水县',532500000000,3),
(532525000000,'石屏县',532500000000,3),
(532527000000,'泸西县',532500000000,3),
(532528000000,'元阳县',532500000000,3),
(532529000000,'红河县',532500000000,3),
(532530000000,'金平苗族瑶族傣族自治县',532500000000,3),
(532531000000,'绿春县',532500000000,3),
(532532000000,'河口瑶族自治县',532500000000,3),
(532600000000,'文山壮族苗族自治州',530000000000,2),
(532601000000,'文山市',532600000000,3),
(532622000000,'砚山县',532600000000,3),
(532623000000,'西畴县',532600000000,3),
(532624000000,'麻栗坡县',532600000000,3),
(532625000000,'马关县',532600000000,3),
(532626000000,'丘北县',532600000000,3),
(532627000000,'广南县',532600000000,3),
(532628000000,'富宁县',532600000000,3),
(532800000000,'西双版纳傣族自治州',530000000000,2),
(532801000000,'景洪市',532800000000,3),
(532822000000,'勐海县',532800000000,3),
(532823000000,'勐腊县',532800000000,3),
(532900000000,'大理白族自治州',530000000000,2),
(532901000000,'大理市',532900000000,3),
(532922000000,'漾濞彝族自治县',532900000000,3),
(532923000000,'祥云县',532900000000,3),
(532924000000,'宾川县',532900000000,3),
(532925000000,'弥渡县',532900000000,3),
(532926000000,'南涧彝族自治县',532900000000,3),
(532927000000,'巍山彝族回族自治县',532900000000,3),
(532928000000,'永平县',532900000000,3),
(532929000000,'云龙县',532900000000,3),
(532930000000,'洱源县',532900000000,3),
(532931000000,'剑川县',532900000000,3),
(532932000000,'鹤庆县',532900000000,3),
(533100000000,'德宏傣族景颇族自治州',530000000000,2),
(533102000000,'瑞丽市',533100000000,3),
(533103000000,'芒市',533100000000,3),
(533122000000,'梁河县',533100000000,3),
(533123000000,'盈江县',533100000000,3),
(533124000000,'陇川县',533100000000,3),
(533300000000,'怒江傈僳族自治州',530000000000,2),
(533301000000,'泸水市',533300000000,3),
(533323000000,'福贡县',533300000000,3),
(533324000000,'贡山独龙族怒族自治县',533300000000,3),
(533325000000,'兰坪白族普米族自治县',533300000000,3),
(533400000000,'迪庆藏族自治州',530000000000,2),
(533401000000,'香格里拉市',533400000000,3),
(533422000000,'德钦县',533400000000,3),
(533423000000,'维西傈僳族自治县',533400000000,3),
(540000000000,'西藏自治区',0,1),
(540100000000,'拉萨市',540000000000,2),
(540101000000,'市辖区',540100000000,3),
(540102000000,'城关区',540100000000,3),
(540103000000,'堆龙德庆区',540100000000,3),
(540104000000,'达孜区',540100000000,3),
(540121000000,'林周县',540100000000,3),
(540122000000,'当雄县',540100000000,3),
(540123000000,'尼木县',540100000000,3),
(540124000000,'曲水县',540100000000,3),
(540127000000,'墨竹工卡县',540100000000,3),
(540171000000,'格尔木藏青工业园区',540100000000,3),
(540172000000,'拉萨经济技术开发区',540100000000,3),
(540173000000,'西藏文化旅游创意园区',540100000000,3),
(540174000000,'达孜工业园区',540100000000,3),
(540200000000,'日喀则市',540000000000,2),
(540202000000,'桑珠孜区',540200000000,3),
(540221000000,'南木林县',540200000000,3),
(540222000000,'江孜县',540200000000,3),
(540223000000,'定日县',540200000000,3),
(540224000000,'萨迦县',540200000000,3),
(540225000000,'拉孜县',540200000000,3),
(540226000000,'昂仁县',540200000000,3),
(540227000000,'谢通门县',540200000000,3),
(540228000000,'白朗县',540200000000,3),
(540229000000,'仁布县',540200000000,3),
(540230000000,'康马县',540200000000,3),
(540231000000,'定结县',540200000000,3),
(540232000000,'仲巴县',540200000000,3),
(540233000000,'亚东县',540200000000,3),
(540234000000,'吉隆县',540200000000,3),
(540235000000,'聂拉木县',540200000000,3),
(540236000000,'萨嘎县',540200000000,3),
(540237000000,'岗巴县',540200000000,3),
(540300000000,'昌都市',540000000000,2),
(540302000000,'卡若区',540300000000,3),
(540321000000,'江达县',540300000000,3),
(540322000000,'贡觉县',540300000000,3),
(540323000000,'类乌齐县',540300000000,3),
(540324000000,'丁青县',540300000000,3),
(540325000000,'察雅县',540300000000,3),
(540326000000,'八宿县',540300000000,3),
(540327000000,'左贡县',540300000000,3),
(540328000000,'芒康县',540300000000,3),
(540329000000,'洛隆县',540300000000,3),
(540330000000,'边坝县',540300000000,3),
(540400000000,'林芝市',540000000000,2),
(540402000000,'巴宜区',540400000000,3),
(540421000000,'工布江达县',540400000000,3),
(540422000000,'米林县',540400000000,3),
(540423000000,'墨脱县',540400000000,3),
(540424000000,'波密县',540400000000,3),
(540425000000,'察隅县',540400000000,3),
(540426000000,'朗县',540400000000,3),
(540500000000,'山南市',540000000000,2),
(540501000000,'市辖区',540500000000,3),
(540502000000,'乃东区',540500000000,3),
(540521000000,'扎囊县',540500000000,3),
(540522000000,'贡嘎县',540500000000,3),
(540523000000,'桑日县',540500000000,3),
(540524000000,'琼结县',540500000000,3),
(540525000000,'曲松县',540500000000,3),
(540526000000,'措美县',540500000000,3),
(540527000000,'洛扎县',540500000000,3),
(540528000000,'加查县',540500000000,3),
(540529000000,'隆子县',540500000000,3),
(540530000000,'错那县',540500000000,3),
(540531000000,'浪卡子县',540500000000,3),
(540600000000,'那曲市',540000000000,2),
(540602000000,'色尼区',540600000000,3),
(540621000000,'嘉黎县',540600000000,3),
(540622000000,'比如县',540600000000,3),
(540623000000,'聂荣县',540600000000,3),
(540624000000,'安多县',540600000000,3),
(540625000000,'申扎县',540600000000,3),
(540626000000,'索县',540600000000,3),
(540627000000,'班戈县',540600000000,3),
(540628000000,'巴青县',540600000000,3),
(540629000000,'尼玛县',540600000000,3),
(540630000000,'双湖县',540600000000,3),
(542500000000,'阿里地区',540000000000,2),
(542521000000,'普兰县',542500000000,3),
(542522000000,'札达县',542500000000,3),
(542523000000,'噶尔县',542500000000,3),
(542524000000,'日土县',542500000000,3),
(542525000000,'革吉县',542500000000,3),
(542526000000,'改则县',542500000000,3),
(542527000000,'措勤县',542500000000,3),
(610000000000,'陕西省',0,1),
(610100000000,'西安市',610000000000,2),
(610101000000,'市辖区',610100000000,3),
(610102000000,'新城区',610100000000,3),
(610103000000,'碑林区',610100000000,3),
(610104000000,'莲湖区',610100000000,3),
(610111000000,'灞桥区',610100000000,3),
(610112000000,'未央区',610100000000,3),
(610113000000,'雁塔区',610100000000,3),
(610114000000,'阎良区',610100000000,3),
(610115000000,'临潼区',610100000000,3),
(610116000000,'长安区',610100000000,3),
(610117000000,'高陵区',610100000000,3),
(610118000000,'鄠邑区',610100000000,3),
(610122000000,'蓝田县',610100000000,3),
(610124000000,'周至县',610100000000,3),
(610200000000,'铜川市',610000000000,2),
(610201000000,'市辖区',610200000000,3),
(610202000000,'王益区',610200000000,3),
(610203000000,'印台区',610200000000,3),
(610204000000,'耀州区',610200000000,3),
(610222000000,'宜君县',610200000000,3),
(610300000000,'宝鸡市',610000000000,2),
(610301000000,'市辖区',610300000000,3),
(610302000000,'渭滨区',610300000000,3),
(610303000000,'金台区',610300000000,3),
(610304000000,'陈仓区',610300000000,3),
(610322000000,'凤翔县',610300000000,3),
(610323000000,'岐山县',610300000000,3),
(610324000000,'扶风县',610300000000,3),
(610326000000,'眉县',610300000000,3),
(610327000000,'陇县',610300000000,3),
(610328000000,'千阳县',610300000000,3),
(610329000000,'麟游县',610300000000,3),
(610330000000,'凤县',610300000000,3),
(610331000000,'太白县',610300000000,3),
(610400000000,'咸阳市',610000000000,2),
(610401000000,'市辖区',610400000000,3),
(610402000000,'秦都区',610400000000,3),
(610403000000,'杨陵区',610400000000,3),
(610404000000,'渭城区',610400000000,3),
(610422000000,'三原县',610400000000,3),
(610423000000,'泾阳县',610400000000,3),
(610424000000,'乾县',610400000000,3),
(610425000000,'礼泉县',610400000000,3),
(610426000000,'永寿县',610400000000,3),
(610428000000,'长武县',610400000000,3),
(610429000000,'旬邑县',610400000000,3),
(610430000000,'淳化县',610400000000,3),
(610431000000,'武功县',610400000000,3),
(610481000000,'兴平市',610400000000,3),
(610482000000,'彬州市',610400000000,3),
(610500000000,'渭南市',610000000000,2),
(610501000000,'市辖区',610500000000,3),
(610502000000,'临渭区',610500000000,3),
(610503000000,'华州区',610500000000,3),
(610522000000,'潼关县',610500000000,3),
(610523000000,'大荔县',610500000000,3),
(610524000000,'合阳县',610500000000,3),
(610525000000,'澄城县',610500000000,3),
(610526000000,'蒲城县',610500000000,3),
(610527000000,'白水县',610500000000,3),
(610528000000,'富平县',610500000000,3),
(610581000000,'韩城市',610500000000,3),
(610582000000,'华阴市',610500000000,3),
(610600000000,'延安市',610000000000,2),
(610601000000,'市辖区',610600000000,3),
(610602000000,'宝塔区',610600000000,3),
(610603000000,'安塞区',610600000000,3),
(610621000000,'延长县',610600000000,3),
(610622000000,'延川县',610600000000,3),
(610623000000,'子长县',610600000000,3),
(610625000000,'志丹县',610600000000,3),
(610626000000,'吴起县',610600000000,3),
(610627000000,'甘泉县',610600000000,3),
(610628000000,'富县',610600000000,3),
(610629000000,'洛川县',610600000000,3),
(610630000000,'宜川县',610600000000,3),
(610631000000,'黄龙县',610600000000,3),
(610632000000,'黄陵县',610600000000,3),
(610700000000,'汉中市',610000000000,2),
(610701000000,'市辖区',610700000000,3),
(610702000000,'汉台区',610700000000,3),
(610703000000,'南郑区',610700000000,3),
(610722000000,'城固县',610700000000,3),
(610723000000,'洋县',610700000000,3),
(610724000000,'西乡县',610700000000,3),
(610725000000,'勉县',610700000000,3),
(610726000000,'宁强县',610700000000,3),
(610727000000,'略阳县',610700000000,3),
(610728000000,'镇巴县',610700000000,3),
(610729000000,'留坝县',610700000000,3),
(610730000000,'佛坪县',610700000000,3),
(610800000000,'榆林市',610000000000,2),
(610801000000,'市辖区',610800000000,3),
(610802000000,'榆阳区',610800000000,3),
(610803000000,'横山区',610800000000,3),
(610822000000,'府谷县',610800000000,3),
(610824000000,'靖边县',610800000000,3),
(610825000000,'定边县',610800000000,3),
(610826000000,'绥德县',610800000000,3),
(610827000000,'米脂县',610800000000,3),
(610828000000,'佳县',610800000000,3),
(610829000000,'吴堡县',610800000000,3),
(610830000000,'清涧县',610800000000,3),
(610831000000,'子洲县',610800000000,3),
(610881000000,'神木市',610800000000,3),
(610900000000,'安康市',610000000000,2),
(610901000000,'市辖区',610900000000,3),
(610902000000,'汉滨区',610900000000,3),
(610921000000,'汉阴县',610900000000,3),
(610922000000,'石泉县',610900000000,3),
(610923000000,'宁陕县',610900000000,3),
(610924000000,'紫阳县',610900000000,3),
(610925000000,'岚皋县',610900000000,3),
(610926000000,'平利县',610900000000,3),
(610927000000,'镇坪县',610900000000,3),
(610928000000,'旬阳县',610900000000,3),
(610929000000,'白河县',610900000000,3),
(611000000000,'商洛市',610000000000,2),
(611001000000,'市辖区',611000000000,3),
(611002000000,'商州区',611000000000,3),
(611021000000,'洛南县',611000000000,3),
(611022000000,'丹凤县',611000000000,3),
(611023000000,'商南县',611000000000,3),
(611024000000,'山阳县',611000000000,3),
(611025000000,'镇安县',611000000000,3),
(611026000000,'柞水县',611000000000,3),
(620000000000,'甘肃省',0,1),
(620100000000,'兰州市',620000000000,2),
(620101000000,'市辖区',620100000000,3),
(620102000000,'城关区',620100000000,3),
(620103000000,'七里河区',620100000000,3),
(620104000000,'西固区',620100000000,3),
(620105000000,'安宁区',620100000000,3),
(620111000000,'红古区',620100000000,3),
(620121000000,'永登县',620100000000,3),
(620122000000,'皋兰县',620100000000,3),
(620123000000,'榆中县',620100000000,3),
(620171000000,'兰州新区',620100000000,3),
(620200000000,'嘉峪关市',620000000000,2),
(620201000000,'市辖区',620200000000,3),
(620300000000,'金昌市',620000000000,2),
(620301000000,'市辖区',620300000000,3),
(620302000000,'金川区',620300000000,3),
(620321000000,'永昌县',620300000000,3),
(620400000000,'白银市',620000000000,2),
(620401000000,'市辖区',620400000000,3),
(620402000000,'白银区',620400000000,3),
(620403000000,'平川区',620400000000,3),
(620421000000,'靖远县',620400000000,3),
(620422000000,'会宁县',620400000000,3),
(620423000000,'景泰县',620400000000,3),
(620500000000,'天水市',620000000000,2),
(620501000000,'市辖区',620500000000,3),
(620502000000,'秦州区',620500000000,3),
(620503000000,'麦积区',620500000000,3),
(620521000000,'清水县',620500000000,3),
(620522000000,'秦安县',620500000000,3),
(620523000000,'甘谷县',620500000000,3),
(620524000000,'武山县',620500000000,3),
(620525000000,'张家川回族自治县',620500000000,3),
(620600000000,'武威市',620000000000,2),
(620601000000,'市辖区',620600000000,3),
(620602000000,'凉州区',620600000000,3),
(620621000000,'民勤县',620600000000,3),
(620622000000,'古浪县',620600000000,3),
(620623000000,'天祝藏族自治县',620600000000,3),
(620700000000,'张掖市',620000000000,2),
(620701000000,'市辖区',620700000000,3),
(620702000000,'甘州区',620700000000,3),
(620721000000,'肃南裕固族自治县',620700000000,3),
(620722000000,'民乐县',620700000000,3),
(620723000000,'临泽县',620700000000,3),
(620724000000,'高台县',620700000000,3),
(620725000000,'山丹县',620700000000,3),
(620800000000,'平凉市',620000000000,2),
(620801000000,'市辖区',620800000000,3),
(620802000000,'崆峒区',620800000000,3),
(620821000000,'泾川县',620800000000,3),
(620822000000,'灵台县',620800000000,3),
(620823000000,'崇信县',620800000000,3),
(620825000000,'庄浪县',620800000000,3),
(620826000000,'静宁县',620800000000,3),
(620881000000,'华亭市',620800000000,3),
(620900000000,'酒泉市',620000000000,2),
(620901000000,'市辖区',620900000000,3),
(620902000000,'肃州区',620900000000,3),
(620921000000,'金塔县',620900000000,3),
(620922000000,'瓜州县',620900000000,3),
(620923000000,'肃北蒙古族自治县',620900000000,3),
(620924000000,'阿克塞哈萨克族自治县',620900000000,3),
(620981000000,'玉门市',620900000000,3),
(620982000000,'敦煌市',620900000000,3),
(621000000000,'庆阳市',620000000000,2),
(621001000000,'市辖区',621000000000,3),
(621002000000,'西峰区',621000000000,3),
(621021000000,'庆城县',621000000000,3),
(621022000000,'环县',621000000000,3),
(621023000000,'华池县',621000000000,3),
(621024000000,'合水县',621000000000,3),
(621025000000,'正宁县',621000000000,3),
(621026000000,'宁县',621000000000,3),
(621027000000,'镇原县',621000000000,3),
(621100000000,'定西市',620000000000,2),
(621101000000,'市辖区',621100000000,3),
(621102000000,'安定区',621100000000,3),
(621121000000,'通渭县',621100000000,3),
(621122000000,'陇西县',621100000000,3),
(621123000000,'渭源县',621100000000,3),
(621124000000,'临洮县',621100000000,3),
(621125000000,'漳县',621100000000,3),
(621126000000,'岷县',621100000000,3),
(621200000000,'陇南市',620000000000,2),
(621201000000,'市辖区',621200000000,3),
(621202000000,'武都区',621200000000,3),
(621221000000,'成县',621200000000,3),
(621222000000,'文县',621200000000,3),
(621223000000,'宕昌县',621200000000,3),
(621224000000,'康县',621200000000,3),
(621225000000,'西和县',621200000000,3),
(621226000000,'礼县',621200000000,3),
(621227000000,'徽县',621200000000,3),
(621228000000,'两当县',621200000000,3),
(622900000000,'临夏回族自治州',620000000000,2),
(622901000000,'临夏市',622900000000,3),
(622921000000,'临夏县',622900000000,3),
(622922000000,'康乐县',622900000000,3),
(622923000000,'永靖县',622900000000,3),
(622924000000,'广河县',622900000000,3),
(622925000000,'和政县',622900000000,3),
(622926000000,'东乡族自治县',622900000000,3),
(622927000000,'积石山保安族东乡族撒拉族自治县',622900000000,3),
(623000000000,'甘南藏族自治州',620000000000,2),
(623001000000,'合作市',623000000000,3),
(623021000000,'临潭县',623000000000,3),
(623022000000,'卓尼县',623000000000,3),
(623023000000,'舟曲县',623000000000,3),
(623024000000,'迭部县',623000000000,3),
(623025000000,'玛曲县',623000000000,3),
(623026000000,'碌曲县',623000000000,3),
(623027000000,'夏河县',623000000000,3),
(630000000000,'青海省',0,1),
(630100000000,'西宁市',630000000000,2),
(630101000000,'市辖区',630100000000,3),
(630102000000,'城东区',630100000000,3),
(630103000000,'城中区',630100000000,3),
(630104000000,'城西区',630100000000,3),
(630105000000,'城北区',630100000000,3),
(630121000000,'大通回族土族自治县',630100000000,3),
(630122000000,'湟中县',630100000000,3),
(630123000000,'湟源县',630100000000,3),
(630200000000,'海东市',630000000000,2),
(630202000000,'乐都区',630200000000,3),
(630203000000,'平安区',630200000000,3),
(630222000000,'民和回族土族自治县',630200000000,3),
(630223000000,'互助土族自治县',630200000000,3),
(630224000000,'化隆回族自治县',630200000000,3),
(630225000000,'循化撒拉族自治县',630200000000,3),
(632200000000,'海北藏族自治州',630000000000,2),
(632221000000,'门源回族自治县',632200000000,3),
(632222000000,'祁连县',632200000000,3),
(632223000000,'海晏县',632200000000,3),
(632224000000,'刚察县',632200000000,3),
(632300000000,'黄南藏族自治州',630000000000,2),
(632321000000,'同仁县',632300000000,3),
(632322000000,'尖扎县',632300000000,3),
(632323000000,'泽库县',632300000000,3),
(632324000000,'河南蒙古族自治县',632300000000,3),
(632500000000,'海南藏族自治州',630000000000,2),
(632521000000,'共和县',632500000000,3),
(632522000000,'同德县',632500000000,3),
(632523000000,'贵德县',632500000000,3),
(632524000000,'兴海县',632500000000,3),
(632525000000,'贵南县',632500000000,3),
(632600000000,'果洛藏族自治州',630000000000,2),
(632621000000,'玛沁县',632600000000,3),
(632622000000,'班玛县',632600000000,3),
(632623000000,'甘德县',632600000000,3),
(632624000000,'达日县',632600000000,3),
(632625000000,'久治县',632600000000,3),
(632626000000,'玛多县',632600000000,3),
(632700000000,'玉树藏族自治州',630000000000,2),
(632701000000,'玉树市',632700000000,3),
(632722000000,'杂多县',632700000000,3),
(632723000000,'称多县',632700000000,3),
(632724000000,'治多县',632700000000,3),
(632725000000,'囊谦县',632700000000,3),
(632726000000,'曲麻莱县',632700000000,3),
(632800000000,'海西蒙古族藏族自治州',630000000000,2),
(632801000000,'格尔木市',632800000000,3),
(632802000000,'德令哈市',632800000000,3),
(632803000000,'茫崖市',632800000000,3),
(632821000000,'乌兰县',632800000000,3),
(632822000000,'都兰县',632800000000,3),
(632823000000,'天峻县',632800000000,3),
(632857000000,'大柴旦行政委员会',632800000000,3),
(640000000000,'宁夏回族自治区',0,1),
(640100000000,'银川市',640000000000,2),
(640101000000,'市辖区',640100000000,3),
(640104000000,'兴庆区',640100000000,3),
(640105000000,'西夏区',640100000000,3),
(640106000000,'金凤区',640100000000,3),
(640121000000,'永宁县',640100000000,3),
(640122000000,'贺兰县',640100000000,3),
(640181000000,'灵武市',640100000000,3),
(640200000000,'石嘴山市',640000000000,2),
(640201000000,'市辖区',640200000000,3),
(640202000000,'大武口区',640200000000,3),
(640205000000,'惠农区',640200000000,3),
(640221000000,'平罗县',640200000000,3),
(640300000000,'吴忠市',640000000000,2),
(640301000000,'市辖区',640300000000,3),
(640302000000,'利通区',640300000000,3),
(640303000000,'红寺堡区',640300000000,3),
(640323000000,'盐池县',640300000000,3),
(640324000000,'同心县',640300000000,3),
(640381000000,'青铜峡市',640300000000,3),
(640400000000,'固原市',640000000000,2),
(640401000000,'市辖区',640400000000,3),
(640402000000,'原州区',640400000000,3),
(640422000000,'西吉县',640400000000,3),
(640423000000,'隆德县',640400000000,3),
(640424000000,'泾源县',640400000000,3),
(640425000000,'彭阳县',640400000000,3),
(640500000000,'中卫市',640000000000,2),
(640501000000,'市辖区',640500000000,3),
(640502000000,'沙坡头区',640500000000,3),
(640521000000,'中宁县',640500000000,3),
(640522000000,'海原县',640500000000,3),
(650000000000,'新疆维吾尔自治区',0,1),
(650100000000,'乌鲁木齐市',650000000000,2),
(650101000000,'市辖区',650100000000,3),
(650102000000,'天山区',650100000000,3),
(650103000000,'沙依巴克区',650100000000,3),
(650104000000,'新市区',650100000000,3),
(650105000000,'水磨沟区',650100000000,3),
(650106000000,'头屯河区',650100000000,3),
(650107000000,'达坂城区',650100000000,3),
(650109000000,'米东区',650100000000,3),
(650121000000,'乌鲁木齐县',650100000000,3),
(650171000000,'乌鲁木齐经济技术开发区',650100000000,3),
(650172000000,'乌鲁木齐高新技术产业开发区',650100000000,3),
(650200000000,'克拉玛依市',650000000000,2),
(650201000000,'市辖区',650200000000,3),
(650202000000,'独山子区',650200000000,3),
(650203000000,'克拉玛依区',650200000000,3),
(650204000000,'白碱滩区',650200000000,3),
(650205000000,'乌尔禾区',650200000000,3),
(650400000000,'吐鲁番市',650000000000,2),
(650402000000,'高昌区',650400000000,3),
(650421000000,'鄯善县',650400000000,3),
(650422000000,'托克逊县',650400000000,3),
(650500000000,'哈密市',650000000000,2),
(650502000000,'伊州区',650500000000,3),
(650521000000,'巴里坤哈萨克自治县',650500000000,3),
(650522000000,'伊吾县',650500000000,3),
(652300000000,'昌吉回族自治州',650000000000,2),
(652301000000,'昌吉市',652300000000,3),
(652302000000,'阜康市',652300000000,3),
(652323000000,'呼图壁县',652300000000,3),
(652324000000,'玛纳斯县',652300000000,3),
(652325000000,'奇台县',652300000000,3),
(652327000000,'吉木萨尔县',652300000000,3),
(652328000000,'木垒哈萨克自治县',652300000000,3),
(652700000000,'博尔塔拉蒙古自治州',650000000000,2),
(652701000000,'博乐市',652700000000,3),
(652702000000,'阿拉山口市',652700000000,3),
(652722000000,'精河县',652700000000,3),
(652723000000,'温泉县',652700000000,3),
(652800000000,'巴音郭楞蒙古自治州',650000000000,2),
(652801000000,'库尔勒市',652800000000,3),
(652822000000,'轮台县',652800000000,3),
(652823000000,'尉犁县',652800000000,3),
(652824000000,'若羌县',652800000000,3),
(652825000000,'且末县',652800000000,3),
(652826000000,'焉耆回族自治县',652800000000,3),
(652827000000,'和静县',652800000000,3),
(652828000000,'和硕县',652800000000,3),
(652829000000,'博湖县',652800000000,3),
(652871000000,'库尔勒经济技术开发区',652800000000,3),
(652900000000,'阿克苏地区',650000000000,2),
(652901000000,'阿克苏市',652900000000,3),
(652922000000,'温宿县',652900000000,3),
(652923000000,'库车县',652900000000,3),
(652924000000,'沙雅县',652900000000,3),
(652925000000,'新和县',652900000000,3),
(652926000000,'拜城县',652900000000,3),
(652927000000,'乌什县',652900000000,3),
(652928000000,'阿瓦提县',652900000000,3),
(652929000000,'柯坪县',652900000000,3),
(653000000000,'克孜勒苏柯尔克孜自治州',650000000000,2),
(653001000000,'阿图什市',653000000000,3),
(653022000000,'阿克陶县',653000000000,3),
(653023000000,'阿合奇县',653000000000,3),
(653024000000,'乌恰县',653000000000,3),
(653100000000,'喀什地区',650000000000,2),
(653101000000,'喀什市',653100000000,3),
(653121000000,'疏附县',653100000000,3),
(653122000000,'疏勒县',653100000000,3),
(653123000000,'英吉沙县',653100000000,3),
(653124000000,'泽普县',653100000000,3),
(653125000000,'莎车县',653100000000,3),
(653126000000,'叶城县',653100000000,3),
(653127000000,'麦盖提县',653100000000,3),
(653128000000,'岳普湖县',653100000000,3),
(653129000000,'伽师县',653100000000,3),
(653130000000,'巴楚县',653100000000,3),
(653131000000,'塔什库尔干塔吉克自治县',653100000000,3),
(653200000000,'和田地区',650000000000,2),
(653201000000,'和田市',653200000000,3),
(653221000000,'和田县',653200000000,3),
(653222000000,'墨玉县',653200000000,3),
(653223000000,'皮山县',653200000000,3),
(653224000000,'洛浦县',653200000000,3),
(653225000000,'策勒县',653200000000,3),
(653226000000,'于田县',653200000000,3),
(653227000000,'民丰县',653200000000,3),
(654000000000,'伊犁哈萨克自治州',650000000000,2),
(654002000000,'伊宁市',654000000000,3),
(654003000000,'奎屯市',654000000000,3),
(654004000000,'霍尔果斯市',654000000000,3),
(654021000000,'伊宁县',654000000000,3),
(654022000000,'察布查尔锡伯自治县',654000000000,3),
(654023000000,'霍城县',654000000000,3),
(654024000000,'巩留县',654000000000,3),
(654025000000,'新源县',654000000000,3),
(654026000000,'昭苏县',654000000000,3),
(654027000000,'特克斯县',654000000000,3),
(654028000000,'尼勒克县',654000000000,3),
(654200000000,'塔城地区',650000000000,2),
(654201000000,'塔城市',654200000000,3),
(654202000000,'乌苏市',654200000000,3),
(654221000000,'额敏县',654200000000,3),
(654223000000,'沙湾县',654200000000,3),
(654224000000,'托里县',654200000000,3),
(654225000000,'裕民县',654200000000,3),
(654226000000,'和布克赛尔蒙古自治县',654200000000,3),
(654300000000,'阿勒泰地区',650000000000,2),
(654301000000,'阿勒泰市',654300000000,3),
(654321000000,'布尔津县',654300000000,3),
(654322000000,'富蕴县',654300000000,3),
(654323000000,'福海县',654300000000,3),
(654324000000,'哈巴河县',654300000000,3),
(654325000000,'青河县',654300000000,3),
(654326000000,'吉木乃县',654300000000,3),
(659000000000,'自治区直辖县级行政区划',650000000000,2),
(659001000000,'石河子市',659000000000,3),
(659002000000,'阿拉尔市',659000000000,3),
(659003000000,'图木舒克市',659000000000,3),
(659004000000,'五家渠市',659000000000,3),
(659006000000,'铁门关市',659000000000,3);

/*Table structure for table `tz_attach_file` */

DROP TABLE IF EXISTS `tz_attach_file`;

CREATE TABLE `tz_attach_file` (
  `file_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `file_type` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `file_size` int(10) DEFAULT NULL COMMENT '文件大小',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `file_join_id` bigint(20) DEFAULT NULL COMMENT '文件关联的表主键id',
  `file_join_type` tinyint(2) DEFAULT NULL COMMENT '文件关联表类型：1 商品表  FileJoinType',
  PRIMARY KEY (`file_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;

/*Data for the table `tz_attach_file` */

insert  into `tz_attach_file`(`file_id`,`file_path`,`file_type`,`file_size`,`upload_time`,`file_join_id`,`file_join_type`) values
(1,'2019/07/6aeea212368e4796ad83e17381d62c38.png','png',26409,'2019-07-01 17:26:44',NULL,NULL),
(2,'2019/07/44097d528be24d48bf54eb0bd596ebc0.jpg','jpg',22379,'2019-07-02 11:30:16',NULL,NULL),
(3,'2019/07/ec73013a82d44306ac477c7bfbaad209.jpg','jpg',41935,'2019-07-02 11:30:26',NULL,NULL),
(4,'2019/07/94a2a3b53b9b4643ad80fec10ef96e28.jpg','jpg',19692,'2019-07-02 11:30:47',NULL,NULL),
(5,'2019/07/ac0b289260174f0b9757afce4da9c115.jpg','jpg',25583,'2019-07-02 11:31:14',NULL,NULL),
(6,'2019/07/5f3cd673cee046218c5916e004346d4c.jpg','jpg',25583,'2019-07-02 11:32:30',NULL,NULL),
(7,'2019/07/44a492a2ec1942a9b7fefe813ba27b78.jpg','jpg',40107,'2019-07-02 17:14:38',NULL,NULL),
(8,'2019/07/bb485486c4334af283738f9192ae806b.gif','gif',10320,'2019-07-23 19:09:20',NULL,NULL);

/*Table structure for table `tz_basket` */

DROP TABLE IF EXISTS `tz_basket`;

CREATE TABLE `tz_basket` (
  `basket_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `prod_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '产品ID',
  `sku_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'SkuID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `basket_count` int(11) NOT NULL DEFAULT '0' COMMENT '购物车产品个数',
  `basket_date` datetime NOT NULL COMMENT '购物时间',
  `discount_id` bigint(20) DEFAULT NULL COMMENT '满减活动ID',
  `distribution_card_no` varchar(36) DEFAULT NULL COMMENT '分销推广人卡号',
  PRIMARY KEY (`basket_id`),
  UNIQUE KEY `uk_user_shop_sku` (`sku_id`,`user_id`,`shop_id`),
  KEY `shop_id` (`shop_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='购物车';

/*Data for the table `tz_basket` */

/*Table structure for table `tz_brand` */

DROP TABLE IF EXISTS `tz_brand`;

CREATE TABLE `tz_brand` (
  `brand_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `brand_name` varchar(30) NOT NULL DEFAULT '' COMMENT '品牌名称',
  `brand_pic` varchar(255) DEFAULT NULL COMMENT '图片路径',
  `user_id` varchar(36) NOT NULL DEFAULT '' COMMENT '用户ID',
  `memo` varchar(50) DEFAULT NULL COMMENT '备注',
  `seq` int(11) DEFAULT '1' COMMENT '顺序',
  `status` int(1) NOT NULL DEFAULT '1' COMMENT '默认是1，表示正常状态,0为下线状态',
  `brief` varchar(100) DEFAULT NULL COMMENT '简要描述',
  `content` text COMMENT '内容',
  `rec_time` datetime DEFAULT NULL COMMENT '记录时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `first_char` varchar(1) DEFAULT NULL COMMENT '品牌首字母',
  PRIMARY KEY (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='品牌表';

/*Data for the table `tz_brand` */

/*Table structure for table `tz_category` */

DROP TABLE IF EXISTS `tz_category`;

CREATE TABLE `tz_category` (
  `category_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '类目ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `parent_id` bigint(20) unsigned NOT NULL COMMENT '父节点',
  `category_name` varchar(50) NOT NULL DEFAULT '' COMMENT '产品类目名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '类目图标',
  `pic` varchar(300) DEFAULT NULL COMMENT '类目的显示图片',
  `seq` int(5) NOT NULL COMMENT '排序',
  `status` int(1) NOT NULL DEFAULT '1' COMMENT '默认是1，表示正常状态,0为下线状态',
  `rec_time` datetime NOT NULL COMMENT '记录时间',
  `grade` int(2) NOT NULL COMMENT '分类层级',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `parent_id` (`parent_id`),
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=utf8 COMMENT='产品类目';

/*Data for the table `tz_category` */

insert  into `tz_category`(`category_id`,`shop_id`,`parent_id`,`category_name`,`icon`,`pic`,`seq`,`status`,`rec_time`,`grade`,`update_time`) values
(85,1,0,'手机数码',NULL,'2019/04/4f148d81d60941b695cb77370a073653.jpg',1,1,'2019-04-21 17:28:32',0,'2019-04-27 11:22:09'),
(87,1,0,'美妆护肤',NULL,'2019/04/84f1a591a9ed46f68d46eee55c130b34.jpg',1,1,'2019-04-21 17:30:45',0,'2019-04-27 10:46:12'),
(88,1,0,'运动服饰',NULL,'2019/04/68f081f04c2644319d882a8828f4741c.jpg',1,1,'2019-04-21 17:31:12',0,'2019-04-27 10:38:31'),
(93,1,85,'手机通讯',NULL,'2019/04/647401fe37b04c19a74adc2353085a24.jpg',1,1,'2019-04-21 18:04:03',0,NULL),
(94,1,85,'智能设备',NULL,'2019/04/b5f5f75e0e4f4669a2f079be1f2c4418.jpg',1,1,'2019-04-21 18:04:32',0,NULL),
(95,1,0,'美味零食',NULL,'2019/04/3483eafc86674a74bcf9da6100769a9a.jpg',1,1,'2019-04-21 21:46:01',0,'2019-04-27 11:23:27'),
(96,1,85,'珠宝钟表',NULL,'2019/04/f2f502eae04c4e9ab5982d57cc4e6333.jpg',1,1,'2019-04-26 17:20:49',0,NULL);

/*Table structure for table `tz_category_brand` */

DROP TABLE IF EXISTS `tz_category_brand`;

CREATE TABLE `tz_category_brand` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类id',
  `brand_id` bigint(20) DEFAULT NULL COMMENT '品牌id',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `brand_id` (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `tz_category_brand` */

/*Table structure for table `tz_category_prop` */

DROP TABLE IF EXISTS `tz_category_prop`;

CREATE TABLE `tz_category_prop` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类id',
  `prop_id` bigint(20) DEFAULT NULL COMMENT '商品属性id即表tz_prod_prop中的prop_id',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `prop_id` (`prop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `tz_category_prop` */

/*Table structure for table `tz_delivery` */

DROP TABLE IF EXISTS `tz_delivery`;

CREATE TABLE `tz_delivery` (
  `dvy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dvy_name` varchar(50) NOT NULL DEFAULT '' COMMENT '物流公司名称',
  `company_home_url` varchar(255) DEFAULT NULL COMMENT '公司主页',
  `rec_time` datetime NOT NULL COMMENT '建立时间',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `query_url` varchar(520) NOT NULL COMMENT '物流查询接口',
  PRIMARY KEY (`dvy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8 COMMENT='物流公司';

/*Data for the table `tz_delivery` */

insert  into `tz_delivery`(`dvy_id`,`dvy_name`,`company_home_url`,`rec_time`,`modify_time`,`query_url`) values
(14,'顺丰快递公司','http://www.sf-express.com','2015-08-20 11:58:03','2017-03-22 17:12:27','http://www.kuaidi100.com/query?type=shunfeng&postid={dvyFlowId}&id=11'),
(15,'申通快递公司','http://www.sto-express.com','2015-08-20 11:58:24','2015-08-20 12:04:23','http://www.kuaidi100.com/query?type=shentong&postid={dvyFlowId}&id=11'),
(16,'中通速递','http://www.zto.cn','2015-08-20 11:58:48','2015-08-20 12:04:31','http://www.kuaidi100.com/query?type=zhongtong&postid={dvyFlowId}&id=11'),
(17,'安信达','http://www.anxinda.com','2015-12-22 10:19:33','2015-12-22 10:37:26','http://www.kuaidi100.com/query?type=anxindakuaixi&postid={dvyFlowId}&id=11'),
(18,'EMS','http://www.ems.com.cn','2015-12-22 10:38:15','2015-12-22 10:38:15','http://www.kuaidi100.com/query?type=ems&postid={dvyFlowId}&id=11'),
(19,'凡客如风达','http://www.rufengda.com','2015-12-22 10:38:55','2015-12-22 10:38:55','http://www.kuaidi100.com/query?type=rufengda&postid={dvyFlowId}&id=11'),
(20,'汇通快递','http://www.htky365.com','2015-12-22 10:39:46','2015-12-22 10:39:46','http://www.kuaidi100.com/query?type=huitongkuaidi&postid={dvyFlowId}&id=11'),
(21,'天天快递','http://www.ttkdex.com','2015-12-22 10:40:44','2015-12-22 10:40:44','http://www.kuaidi100.com/query?type=tiantian&postid={dvyFlowId}&id=11'),
(22,'佳吉快运','http://www.jiaji.com','2015-12-22 10:42:55','2015-12-22 10:42:55','http://www.kuaidi100.com/query?type=jiajiwuliu&postid={dvyFlowId}&id=11'),
(23,'速尔快递','http://www.sure56.com','2015-12-22 10:43:35','2015-12-22 10:43:35','http://www.kuaidi100.com/query?type=suer&postid={dvyFlowId}&id=11'),
(24,'信丰物流','http://www.xf-express.com.cn','2015-12-22 10:44:17','2015-12-22 10:44:17','http://www.kuaidi100.com/query?type=xinfengwuliu&postid={dvyFlowId}&id=11'),
(25,'韵达快递','http://www.yundaex.com','2015-12-22 10:44:51','2015-12-22 10:44:51','http://www.kuaidi100.com/query?type=yunda&postid={dvyFlowId}&id=11'),
(26,'优速快递','http://www.uc56.com','2015-12-22 10:45:20','2015-12-22 10:45:20','http://www.kuaidi100.com/query?type=youshuwuliu&postid={dvyFlowId}&id=11'),
(27,'中邮物流','http://www.cnpl.com.cn','2015-12-22 10:45:58','2015-12-22 10:45:58','http://www.kuaidi100.com/query?type=zhongyouwuliu&postid={dvyFlowId}&id=11'),
(28,'圆通快递','http://www.yto.net.cn','2015-12-22 11:44:18','2015-12-22 11:44:18','http://www.kuaidi100.com/query?type=yuantong&postid={dvyFlowId}&id=11'),
(29,'宅急送','http://www.zjs.com.cn','2015-12-22 11:45:55','2015-12-22 11:45:55','http://www.kuaidi100.com/query?type=zhaijisong&postid={dvyFlowId}&id=11');

/*Table structure for table `tz_hot_search` */

DROP TABLE IF EXISTS `tz_hot_search`;

CREATE TABLE `tz_hot_search` (
  `hot_search_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺ID 0为全局热搜',
  `content` varchar(255) NOT NULL COMMENT '内容',
  `rec_date` datetime NOT NULL COMMENT '录入时间',
  `seq` int(11) DEFAULT NULL COMMENT '顺序',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态 0下线 1上线',
  `title` varchar(255) NOT NULL COMMENT '热搜标题',
  PRIMARY KEY (`hot_search_id`) USING BTREE,
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='热搜';

/*Data for the table `tz_hot_search` */

/*Table structure for table `tz_index_img` */

DROP TABLE IF EXISTS `tz_index_img`;

CREATE TABLE `tz_index_img` (
  `img_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺ID',
  `img_url` varchar(200) NOT NULL COMMENT '图片',
  `des` varchar(200) DEFAULT '' COMMENT '说明文字,描述',
  `title` varchar(200) DEFAULT NULL COMMENT '标题',
  `link` varchar(200) DEFAULT NULL COMMENT '链接',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `seq` int(11) DEFAULT '0' COMMENT '顺序',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `relation` bigint(20) DEFAULT NULL COMMENT '关联',
  `type` int(2) DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`img_id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='主页轮播图';

/*Data for the table `tz_index_img` */

insert  into `tz_index_img`(`img_id`,`shop_id`,`img_url`,`des`,`title`,`link`,`status`,`seq`,`upload_time`,`relation`,`type`) values
(2,1,'2019/04/f676a8a8dbcb4e5c9bc907ef059aedb9.jpg','',NULL,NULL,1,1,'2018-12-20 15:22:19',73,0),
(4,1,'2019/04/52b0082c60c04fc99dd03288548f2841.dpg','',NULL,NULL,1,120,'2019-04-17 17:12:37',76,0),
(5,1,'2019/04/c2d830afc56d469f96825e91e464d155.jpg','',NULL,NULL,1,10,'2019-04-22 09:57:34',76,0);

/*Table structure for table `tz_message` */

DROP TABLE IF EXISTS `tz_message`;

CREATE TABLE `tz_message` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `create_time` datetime DEFAULT NULL COMMENT '留言创建时间',
  `user_name` varchar(36) DEFAULT NULL COMMENT '姓名',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `contact` varchar(36) DEFAULT NULL COMMENT '联系方式',
  `content` text COMMENT '留言内容',
  `reply` text COMMENT '留言回复',
  `status` tinyint(2) DEFAULT NULL COMMENT '状态：0:未审核 1审核通过',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8;

/*Table structure for table `tz_notice` */

DROP TABLE IF EXISTS `tz_notice`;

CREATE TABLE `tz_notice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公告id',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `title` varchar(36) DEFAULT NULL COMMENT '公告标题',
  `content` text COMMENT '公告内容',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态(1:公布 0:撤回)',
  `is_top` tinyint(2) DEFAULT NULL COMMENT '是否置顶',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

/*Data for the table `tz_notice` */

insert  into `tz_notice`(`id`,`shop_id`,`title`,`content`,`status`,`is_top`,`publish_time`,`update_time`) values
(1,1,'两种意见 | 早餐绝配，松软到怀疑人生的酵母面包','<p style=\"text-align: justify;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">这款酵母面包我吹爆，不愧是销量超900万的爆品，非常新鲜，松软到怀疑人生，感觉跟超市里卖的那种小面包不太一样。因为是用天然酵母混合面粉发酵，制作周期比人工合成的速发酵母要长很多，但很松软，是那种自然的甜味，也不容易长胖，跟咖啡搭配简直绝了，已经成了我每天早餐的绝配。</span></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><img style=\"display: block; margin-left: auto; margin-right: auto;\" src=\"http://img-test.gz-yami.com/2019/04/3b6bf397f22c42249e463661a522c88c.jpg\" alt=\"\" width=\"750\" height=\"450\" /></p>',1,1,'2019-04-22 14:33:41','2019-04-27 15:24:27'),
(2,1,'超大容量折叠包，再也不担心旅游购物买爆','<p><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;\"><img style=\"display: block; margin-left: auto; margin-right: auto;\" src=\"http://img-test.gz-yami.com/2019/04/febc23ad9cab40a3b7a9557df244b948.jpg\" alt=\"\" width=\"750\" height=\"450\" /></span></p>\n<p>&nbsp;</p>\n<p style=\"text-align: justify;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">出国旅游，购物时很容易不小心就放飞自我，买一大堆东西，发现装不下了。这时，我就会从包里拿出严选折叠手提袋，优雅地解决&ldquo;爆买&rdquo;后无处安放的尴尬。</span></p>\n<p style=\"text-align: justify;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">这个手提袋容量巨大，双层防水面料，但非常轻，收纳起来只有手掌厚度和大小，放在包里一点不占空间。真的超能装，打开后最多能装25L，抵半个行李箱了。袋子一侧能直接套入拉杆，稳稳地放在箱子上，拖起来毫不费力。我每次长途旅行必备它，平时拿去买菜、健身房也很好用。</span></p>',1,1,'2019-04-22 15:54:55','2019-04-27 15:24:39'),
(3,1,'同是纯棉，为什么它睡起来如此舒服','<p style=\"text-align: justify;\"><span style=\"font-size: 20px;\"><img style=\"display: block; margin-left: auto; margin-right: auto;\" src=\"http://img-test.gz-yami.com/2019/04/b837e346051a4c51a5d0d22e137187fe.jpg\" alt=\"\" width=\"670\" height=\"480\" /></span></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><span style=\"font-size: 20px;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">说到床品，我一直很想要那种五星级酒店的面料，但每次都搞不懂那些纺织名词，稀里糊涂就买了，结果往往是不好睡。</span><br /></span></p>\n<p style=\"text-align: justify;\"><span style=\"font-size: 16px;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">后来查了一下，只有贡缎才具有这种光滑细腻的质感，但织法工艺难度高，一套动辄六七百。同事推荐了严选这款四件套，是用60支的优质长绒棉，每平方英寸400根线织成的，非常细密柔软。</span></span></p>\n<p style=\"text-align: justify;\"><span style=\"font-size: 16px;\"><span style=\"color: #222222; font-family: Consolas, \'Lucida Console\', \'Courier New\', monospace; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;\">第一天睡觉的时候我惊呆了，同样是纯棉床品，为什么它睡起来如此舒服，竟然有着丝绸一样的顺滑触感。而且厚薄适中，透气性也不错，一年四季都能用，非常推荐。</span></span></p>\n<p>&nbsp;</p>',1,1,'2019-04-22 16:28:18','2019-04-27 15:25:00');


/*Table structure for table `tz_order` */

DROP TABLE IF EXISTS `tz_order`;

CREATE TABLE `tz_order` (
  `order_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `prod_name` varchar(1000) NOT NULL DEFAULT '' COMMENT '产品名称,多个产品将会以逗号隔开',
  `user_id` varchar(36) NOT NULL COMMENT '订购用户ID',
  `order_number` varchar(50) NOT NULL COMMENT '订购流水号',
  `total` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总值',
  `actual_total` decimal(15,2) DEFAULT NULL COMMENT '实际总值',
  `pay_type` int(1) DEFAULT NULL COMMENT '支付方式 0 手动代付 1 微信支付 2 支付宝',
  `remarks` varchar(1024) DEFAULT NULL COMMENT '订单备注',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '订单状态 1:待付款 2:待发货 3:待收货 4:待评价 5:成功 6:失败',
  `dvy_type` varchar(10) DEFAULT NULL COMMENT '配送类型',
  `dvy_id` bigint(20) DEFAULT NULL COMMENT '配送方式ID',
  `dvy_flow_id` varchar(100) DEFAULT '' COMMENT '物流单号',
  `freight_amount` decimal(15,2) DEFAULT '0.00' COMMENT '订单运费',
  `addr_order_id` bigint(20) DEFAULT NULL COMMENT '用户订单地址Id',
  `product_nums` int(10) DEFAULT NULL COMMENT '订单商品总数',
  `create_time` datetime NOT NULL COMMENT '订购时间',
  `update_time` datetime DEFAULT NULL COMMENT '订单更新时间',
  `pay_time` datetime DEFAULT NULL COMMENT '付款时间',
  `dvy_time` datetime DEFAULT NULL COMMENT '发货时间',
  `finally_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `is_payed` tinyint(1) DEFAULT NULL COMMENT '是否已经支付，1：已经支付过，0：，没有支付过',
  `delete_status` int(1) DEFAULT '0' COMMENT '用户订单删除状态，0：没有删除， 1：回收站， 2：永久删除',
  `refund_sts` int(1) DEFAULT '0' COMMENT '0:默认,1:在处理,2:处理完成',
  `reduce_amount` decimal(15,2) DEFAULT NULL COMMENT '优惠总额',
  `order_type` tinyint(2) DEFAULT NULL COMMENT '订单类型',
  `close_type` tinyint(2) DEFAULT NULL COMMENT '订单关闭原因 1-超时未支付 2-退款关闭 4-买家取消 15-已通过货到付款交易',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_number_unique_ind` (`order_number`),
  UNIQUE KEY `order_number_userid_unique_ind` (`user_id`,`order_number`),
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='订单表';

/*Data for the table `tz_order` */

insert  into `tz_order`(`order_id`,`shop_id`,`prod_name`,`user_id`,`order_number`,`total`,`actual_total`,`pay_type`,`remarks`,`status`,`dvy_type`,`dvy_id`,`dvy_flow_id`,`freight_amount`,`addr_order_id`,`product_nums`,`create_time`,`update_time`,`pay_time`,`dvy_time`,`finally_time`,`cancel_time`,`is_payed`,`delete_status`,`refund_sts`,`reduce_amount`,`order_type`,`close_type`) values
(1,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','51540df5255e4d22903b0f83921095ff','1145634946149388288',1.01,1.01,1,'',5,NULL,14,'123656347535245',0.00,1,1,'2019-07-01 18:07:11','2019-07-01 18:07:22','2019-07-01 18:07:22','2019-07-03 11:06:57','2019-08-07 17:05:00',NULL,1,0,0,0.00,NULL,NULL),
(2,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','51540df5255e4d22903b0f83921095ff','1146346112622399488',1.01,1.01,NULL,'',6,NULL,NULL,'',0.00,2,1,'2019-07-03 17:13:06','2019-07-03 17:13:06',NULL,NULL,NULL,'2019-07-03 17:17:46',0,2,0,0.00,NULL,NULL),
(3,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 64GB ','51540df5255e4d22903b0f83921095ff','1146346762332672000',7103.00,7103.00,NULL,'',6,NULL,NULL,'',0.00,3,1,'2019-07-03 17:15:41','2019-07-03 17:15:41',NULL,NULL,NULL,'2019-07-03 17:17:21',0,2,0,0.00,NULL,NULL),
(4,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','51540df5255e4d22903b0f83921095ff','1146346992126005248',1.01,1.01,NULL,'',6,NULL,NULL,'',0.00,5,1,'2019-07-03 17:16:36','2019-07-03 17:16:36',NULL,NULL,NULL,'2019-07-03 17:17:17',0,2,0,0.00,NULL,NULL),
(5,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','51540df5255e4d22903b0f83921095ff','1146347329394184192',1.01,1.01,NULL,'',6,NULL,NULL,'',0.00,6,1,'2019-07-03 17:17:56','2019-07-03 17:17:56',NULL,NULL,NULL,'2019-08-07 17:02:00',0,0,0,0.00,NULL,NULL);

/*Table structure for table `tz_order_item` */

DROP TABLE IF EXISTS `tz_order_item`;

CREATE TABLE `tz_order_item` (
  `order_item_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `order_number` varchar(50) NOT NULL COMMENT '订单order_number',
  `prod_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `sku_id` bigint(20) unsigned NOT NULL COMMENT '产品SkuID',
  `prod_count` int(11) NOT NULL DEFAULT '0' COMMENT '购物车产品个数',
  `prod_name` varchar(120) NOT NULL DEFAULT '' COMMENT '产品名称',
  `sku_name` varchar(120) DEFAULT NULL COMMENT 'sku名称',
  `pic` varchar(255) NOT NULL DEFAULT '' COMMENT '产品主图片路径',
  `price` decimal(15,2) NOT NULL COMMENT '产品价格',
  `user_id` varchar(36) NOT NULL DEFAULT '' COMMENT '用户Id',
  `product_total_amount` decimal(15,2) NOT NULL COMMENT '商品总金额',
  `rec_time` datetime NOT NULL COMMENT '购物时间',
  `comm_sts` int(1) NOT NULL DEFAULT '0' COMMENT '评论状态： 0 未评价  1 已评价',
  `distribution_card_no` varchar(36) DEFAULT NULL COMMENT '推广员使用的推销卡号',
  `basket_date` datetime DEFAULT NULL COMMENT '加入购物车时间',
  PRIMARY KEY (`order_item_id`),
  KEY `order_number` (`order_number`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='订单项';

/*Data for the table `tz_order_item` */

insert  into `tz_order_item`(`order_item_id`,`shop_id`,`order_number`,`prod_id`,`sku_id`,`prod_count`,`prod_name`,`sku_name`,`pic`,`price`,`user_id`,`product_total_amount`,`rec_time`,`comm_sts`,`distribution_card_no`,`basket_date`) values
(1,1,'1145634946149388288',18,314,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','换修无忧版 深空灰色 64GB ','2019/04/2cd9ec641d92458983c00d87ff33ad57.jpg',1.01,'51540df5255e4d22903b0f83921095ff',1.01,'2019-07-01 18:07:11',0,NULL,'2019-07-01 18:05:54'),
(2,1,'1146346112622399488',18,314,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','换修无忧版 深空灰色 64GB ','2019/04/2cd9ec641d92458983c00d87ff33ad57.jpg',1.01,'51540df5255e4d22903b0f83921095ff',1.01,'2019-07-03 17:13:06',0,NULL,'2019-07-03 17:13:05'),
(3,1,'1146346762332672000',18,317,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 64GB ','换修无忧版 银色 64GB ','2019/04/eaa8c9bd3e7b41eaa310adbde10b6401.jpg',7103.00,'51540df5255e4d22903b0f83921095ff',7103.00,'2019-07-03 17:15:41',0,NULL,'2019-07-03 17:15:40'),
(4,1,'1146346992126005248',18,314,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','换修无忧版 深空灰色 64GB ','2019/04/2cd9ec641d92458983c00d87ff33ad57.jpg',1.01,'51540df5255e4d22903b0f83921095ff',1.01,'2019-07-03 17:16:36',0,NULL,'2019-07-03 17:16:34'),
(5,1,'1146347329394184192',18,314,1,'Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ','换修无忧版 深空灰色 64GB ','2019/04/2cd9ec641d92458983c00d87ff33ad57.jpg',1.01,'51540df5255e4d22903b0f83921095ff',1.01,'2019-07-03 17:17:56',0,NULL,'2019-07-03 17:17:55');

/*Table structure for table `tz_order_refund` */

DROP TABLE IF EXISTS `tz_order_refund`;

CREATE TABLE `tz_order_refund` (
  `refund_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单流水号',
  `order_amount` double(12,2) NOT NULL COMMENT '订单总金额',
  `order_item_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单项ID 全部退款是0',
  `refund_sn` varchar(50) NOT NULL COMMENT '退款编号',
  `flow_trade_no` varchar(100) NOT NULL COMMENT '订单支付流水号',
  `out_refund_no` varchar(200) DEFAULT NULL COMMENT '第三方退款单号(微信退款单号)',
  `pay_type` int(1) DEFAULT NULL COMMENT '订单支付方式 1 微信支付 2 支付宝',
  `pay_type_name` varchar(50) DEFAULT NULL COMMENT '订单支付名称',
  `user_id` varchar(50) NOT NULL COMMENT '买家ID',
  `goods_num` int(11) DEFAULT NULL COMMENT '退货数量',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `apply_type` int(1) NOT NULL DEFAULT '0' COMMENT '申请类型:1,仅退款,2退款退货',
  `refund_sts` int(1) NOT NULL DEFAULT '0' COMMENT '处理状态:1为待审核,2为同意,3为不同意',
  `return_money_sts` int(1) NOT NULL DEFAULT '0' COMMENT '处理退款状态: 0:退款处理中 1:退款成功 -1:退款失败',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `handel_time` datetime DEFAULT NULL COMMENT '卖家处理时间',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `photo_files` varchar(150) DEFAULT NULL COMMENT '文件凭证json',
  `buyer_msg` varchar(300) DEFAULT NULL COMMENT '申请原因',
  `seller_msg` varchar(300) DEFAULT NULL COMMENT '卖家备注',
  `express_name` varchar(50) DEFAULT NULL COMMENT '物流公司名称',
  `express_no` varchar(50) DEFAULT NULL COMMENT '物流单号',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `receive_message` varchar(300) DEFAULT NULL COMMENT '收货备注',
  PRIMARY KEY (`refund_id`),
  UNIQUE KEY `refund_sn_unique` (`refund_sn`),
  KEY `order_number` (`order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Data for the table `tz_order_refund` */

/*Table structure for table `tz_order_settlement` */

DROP TABLE IF EXISTS `tz_order_settlement`;

CREATE TABLE `tz_order_settlement` (
  `settlement_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '支付结算单据ID',
  `pay_no` varchar(36) DEFAULT NULL COMMENT '支付单号',
  `biz_pay_no` varchar(255) DEFAULT NULL COMMENT '外部订单流水号',
  `order_number` varchar(36) DEFAULT NULL COMMENT 'order表中的订单号',
  `pay_type` int(1) DEFAULT NULL COMMENT '支付方式 1 微信支付 2 支付宝',
  `pay_type_name` varchar(50) DEFAULT NULL COMMENT '支付方式名称',
  `pay_amount` decimal(15,2) DEFAULT NULL COMMENT '支付金额',
  `is_clearing` int(1) DEFAULT NULL COMMENT '是否清算 0:否 1:是',
  `user_id` varchar(36) DEFAULT NULL COMMENT '用户ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `clearing_time` datetime DEFAULT NULL COMMENT '清算时间',
  `version` int(11) DEFAULT NULL COMMENT '版本号',
  `pay_status` int(1) DEFAULT NULL COMMENT '支付状态',
  PRIMARY KEY (`settlement_id`),
  UNIQUE KEY `primary_order_no` (`order_number`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

/*Data for the table `tz_order_settlement` */

insert  into `tz_order_settlement`(`settlement_id`,`pay_no`,`biz_pay_no`,`order_number`,`pay_type`,`pay_type_name`,`pay_amount`,`is_clearing`,`user_id`,`create_time`,`clearing_time`,`version`,`pay_status`) values
(1,'1145634947151826944',NULL,'1145634946149388288',1,NULL,1.01,0,'51540df5255e4d22903b0f83921095ff','2019-07-01 18:07:11',NULL,1,1),
(2,'1146346113691947008',NULL,'1146346112622399488',1,NULL,1.01,0,'51540df5255e4d22903b0f83921095ff','2019-07-03 17:13:06',NULL,0,0),
(3,'1146346763158949888',NULL,'1146346762332672000',1,NULL,7103.00,0,'51540df5255e4d22903b0f83921095ff','2019-07-03 17:15:41',NULL,0,0),
(4,'1146346992977448960',NULL,'1146346992126005248',1,NULL,1.01,0,'51540df5255e4d22903b0f83921095ff','2019-07-03 17:16:36',NULL,0,0),
(5,'1146347417931747328',NULL,'1146347329394184192',1,NULL,1.01,0,'51540df5255e4d22903b0f83921095ff','2019-07-03 17:17:56',NULL,0,0);

/*Table structure for table `tz_pick_addr` */

DROP TABLE IF EXISTS `tz_pick_addr`;

CREATE TABLE `tz_pick_addr` (
  `addr_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `addr_name` varchar(36) DEFAULT NULL COMMENT '自提点名称',
  `addr` varchar(1000) DEFAULT NULL COMMENT '地址',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `province` varchar(32) DEFAULT NULL COMMENT '省份',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `city` varchar(32) DEFAULT NULL COMMENT '城市',
  `area_id` bigint(20) DEFAULT NULL COMMENT '区/县ID',
  `area` varchar(32) DEFAULT NULL COMMENT '区/县',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  PRIMARY KEY (`addr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='用户配送地址';

/*Table structure for table `tz_prod` */

DROP TABLE IF EXISTS `tz_prod`;

CREATE TABLE `tz_prod` (
  `prod_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `prod_name` varchar(300) NOT NULL DEFAULT '' COMMENT '商品名称',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `ori_price` decimal(15,2) DEFAULT '0.00' COMMENT '原价',
  `price` decimal(15,2) DEFAULT NULL COMMENT '现价',
  `brief` varchar(500) DEFAULT '' COMMENT '简要描述,卖点等',
  `content` text COMMENT '详细描述',
  `pic` varchar(255) DEFAULT NULL COMMENT '商品主图',
  `imgs` varchar(1000) DEFAULT NULL COMMENT '商品图片，以,分割',
  `status` int(1) DEFAULT '0' COMMENT '默认是1，表示正常状态, -1表示删除, 0下架',
  `category_id` bigint(20) unsigned DEFAULT NULL COMMENT '商品分类',
  `sold_num` int(11) DEFAULT NULL COMMENT '销量',
  `total_stocks` int(11) DEFAULT '0' COMMENT '总库存',
  `delivery_mode` json DEFAULT NULL COMMENT '配送方式json见TransportModeVO',
  `delivery_template_id` bigint(20) DEFAULT NULL COMMENT '运费模板id',
  `create_time` datetime DEFAULT NULL COMMENT '录入时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `putaway_time` datetime DEFAULT NULL COMMENT '上架时间',
  `version` int(11) DEFAULT NULL COMMENT '版本 乐观锁',
  PRIMARY KEY (`prod_id`),
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8 COMMENT='商品';

/*Data for the table `tz_prod` */

insert  into `tz_prod`(`prod_id`,`prod_name`,`shop_id`,`ori_price`,`price`,`brief`,`content`,`pic`,`imgs`,`status`,`category_id`,`sold_num`,`total_stocks`,`delivery_mode`,`delivery_template_id`,`create_time`,`update_time`,`putaway_time`,`version`) values
(18,'Apple iPhone XS Max 移动联通电信4G手机 ',1,0.00,1.01,'6.5英寸大屏，支持双卡。','<div style=\"margin: 0px; padding: 0px; color: #666666; font-family: tahoma, arial, \'Microsoft YaHei\', \'Hiragino Sans GB\', u5b8bu4f53, sans-serif; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration-style: initial; text-decoration-color: initial;\" align=\"center\">\n<table id=\"__01\" style=\"text-align: center;\" border=\"0\" width=\"750\" cellspacing=\"0\" cellpadding=\"0\">\n<tbody>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img30.360buyimg.com/cms/jfs/t1/4626/32/3475/220504/5b997365E80a1373f/279c244f12161cb3.jpg\" alt=\"\" width=\"750\" height=\"1991\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img12.360buyimg.com/cms/jfs/t1/3397/21/3533/236322/5b99759aE73795787/f782e04a140c8f16.jpg\" alt=\"\" width=\"750\" height=\"2052\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img11.360buyimg.com/cms/jfs/t1/5274/3/3465/245167/5b997365E16b81bc9/93e07e40f3af5e62.jpg\" alt=\"\" width=\"750\" height=\"2250\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img30.360buyimg.com/cms/jfs/t1/2322/11/3524/269574/5b997365E26f81a7a/e01fc9486da9eda1.jpg\" alt=\"\" width=\"750\" height=\"2327\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img13.360buyimg.com/cms/jfs/t1/5074/21/3432/296470/5b997364Ee966f7a0/7f424d41479db45d.jpg\" alt=\"\" width=\"750\" height=\"2561\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img13.360buyimg.com/cms/jfs/t1/5770/18/3580/288371/5b997365Ea2c58cb4/176b9a40ccd4e56b.jpg\" alt=\"\" width=\"750\" height=\"2668\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img11.360buyimg.com/cms/jfs/t1/227/21/3811/268132/5b997364E3d6c51b2/92d2a3a559e3baa8.jpg\" alt=\"\" width=\"750\" height=\"2850\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img20.360buyimg.com/cms/jfs/t1/3787/5/3493/125020/5b997363E3c9f5910/ddbd08a556744630.jpg\" alt=\"\" width=\"750\" height=\"1486\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img30.360buyimg.com/cms/jfs/t1/1687/5/3327/266718/5b997366E9cc80e69/9e40ceae1fef4466.jpg\" alt=\"\" width=\"750\" height=\"3376\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img30.360buyimg.com/cms/jfs/t1/457/6/3849/283318/5b997363E0c5ab7a9/6f636f0a286bc87c.jpg\" alt=\"\" width=\"750\" height=\"2455\" /></td>\n</tr>\n<tr>\n<td><img class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; vertical-align: middle;\" src=\"https://img10.360buyimg.com/cms/jfs/t1/397/25/3796/217624/5b9975a8E5ee578af/4d8f05a606fa5c4a.jpg\" alt=\"\" width=\"750\" height=\"2703\" /></td>\n</tr>\n</tbody>\n</table>\n</div>','2019/04/eaa8c9bd3e7b41eaa310adbde10b6401.jpg','2019/04/eaa8c9bd3e7b41eaa310adbde10b6401.jpg',1,93,NULL,96,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-03-29 14:40:00','2019-06-22 18:28:32','2019-06-22 18:28:32',85),
(59,'兰蔻粉水清滢柔肤水400ml 爽肤水女保湿舒缓滋润嫩肤',1,0.00,420.00,'好看耐穿','<p><img src=\"http://img-test.gz-yami.com/2019/04/71f54ee20ef34872b1e0aa53cb75b7b6.jpg\" alt=\"\" width=\"790\" height=\"1110\" /></p>','2019/04/ce5a32005a7a4f9483a17051bda6bd6c.jpg','2019/04/ce5a32005a7a4f9483a17051bda6bd6c.jpg,2019/04/8a8712b81a2d4f4ca3eb4a725ad229c6.jpg',1,87,NULL,0,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',48,'2019-04-21 19:15:34','2019-04-29 14:30:44','2019-04-29 14:30:44',NULL),
(68,'【Dole都乐】菲律宾都乐非转基因木瓜1只 单只约410g',1,0.00,26.00,'包邮 肉厚籽少 独具风味','<p style=\"text-align: justify;\"><img src=\"http://img-test.gz-yami.com/2019/04/e7536a53a83d450e8635ce1e9819faf6.jpg\" alt=\"\" width=\"790\" height=\"350\" /></p>','2019/04/0bfd73f43d764d20b2f0b92813abdc56.jpg','2019/04/0bfd73f43d764d20b2f0b92813abdc56.jpg,2019/04/355020f27acd4b13a652cb830f03bedc.jpg',1,95,NULL,0,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-21 21:56:38','2019-05-22 10:30:37','2019-05-22 10:30:37',NULL),
(69,'阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956',1,10.00,1199.00,' 运动鞋/休闲鞋','<p><img src=\"http://img-test.gz-yami.com/2019/04/6d0bea4a0be54423999136bcd1158897.jpg\" alt=\"\" width=\"790\" height=\"2232\" /></p>','2019/04/b3558ee506fb4589bfaa94a543226477.jpg','2019/04/b3558ee506fb4589bfaa94a543226477.jpg,2019/04/df052ea31d3149fdb5c54f7c4fc6349b.jpg',0,88,NULL,0,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-21 22:10:04','2019-05-23 20:17:03','2019-05-22 13:37:26',NULL),
(70,'【Dole都乐】比利时Truval啤梨12只 进口水果新鲜梨 单果120g左右',1,0.00,38.00,'好吃','<p><img src=\"http://img-test.gz-yami.com/2019/04/67ce2251e9b14ea08b87752ef7b30207.jpg\" alt=\"\" width=\"760\" height=\"488\" /></p>','2019/04/de0edd2aaf2d4d3c8b6c3fdfde738805.jpg','2019/04/de0edd2aaf2d4d3c8b6c3fdfde738805.jpg,2019/04/ee4530e364ca48869347e1e79915406c.jpg',1,95,NULL,15,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-22 16:43:33','2019-06-22 09:40:24','2019-06-22 09:40:24',NULL),
(71,'旗舰店官网 自拍神器 梵高定制',1,10.00,6998.00,'梵高定制礼盒 全球限量发售 分期免息','<p><img src=\"http://img-test.gz-yami.com/2019/04/fa35b300102e45f3a57d7c5c775ebf6d.jpg\" alt=\"\" width=\"790\" height=\"853\" /></p>\n<p><img src=\"http://img-test.gz-yami.com/2019/04/f8fd168ddb8a437dbb5b742691bd1d02.jpg\" alt=\"\" width=\"800\" height=\"800\" /><img src=\"http://img-test.gz-yami.com/2019/04/db46108466264b48841b18437940e0b3.jpg\" alt=\"\" width=\"800\" height=\"800\" /></p>','2019/04/667e4ce211b44c6da10891f42927fc11.jpg','2019/04/667e4ce211b44c6da10891f42927fc11.jpg,2019/04/31c416e7020b4fe69d491bc09baaae90.jpg',1,85,NULL,99,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-23 15:43:26','2019-05-21 11:01:59','2019-05-21 11:01:59',NULL),
(72,'餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭',1,0.00,169.00,'创意餐厅装饰画 好好吃饭','<p><img src=\"http://img-test.gz-yami.com/2019/04/dca352cc3b0a438e95509f2cf54860de.jpg\" alt=\"\" width=\"790\" height=\"1282\" /></p>','2019/04/299df42b0b404c338060aef5b1b11dd7.jpg','2019/04/299df42b0b404c338060aef5b1b11dd7.jpg,2019/04/bca5ff8e257f4db6aa0be42ed4393568.jpg',1,91,NULL,0,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-25 10:22:21','2019-04-29 14:29:25','2019-04-29 14:29:25',NULL),
(73,'阴阳师寮办秘制 月见和菓子 184克',1,0.00,22.00,'三层夹心，Q弹有料','<p><img src=\"http://img-test.gz-yami.com/2019/04/77f70d0c32c44294a2c80273262fa5fc.jpg\" alt=\"\" width=\"750\" height=\"750\" /></p>','2019/04/60c6300596504c10bd80d6365647b078.jpg','2019/04/60c6300596504c10bd80d6365647b078.jpg,2019/04/11175723eae8431cabda164e0e197cb1.jpg',1,95,NULL,77,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-04-27 11:37:13','2019-05-21 13:56:10','2019-05-21 13:56:10',NULL),
(74,'旺仔牛奶',2,0.00,100.00,'测试','<p>测试商品</p>','2019/05/b1a4bf7612554dbc8395f22e22d531c8.jpg','2019/05/b1a4bf7612554dbc8395f22e22d531c8.jpg',1,95,NULL,0,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',48,'2019-05-22 10:39:49','2019-05-22 10:39:49','2019-05-22 10:39:49',NULL),
(75,'测试商品A',1,30.00,30.00,'','<p>测试</p>','2019/05/65525bbccee04ec5a094f38ce61d6b86.jpg','2019/05/65525bbccee04ec5a094f38ce61d6b86.jpg',1,95,NULL,30,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-05-23 17:23:11','2019-06-22 09:40:13','2019-06-22 09:40:13',NULL),
(76,'测试商品B',1,40.00,40.00,'BBBB','<p>测试</p>','2019/05/150d6c4a46914dbfa82690098b2ec4e7.jpg','2019/05/150d6c4a46914dbfa82690098b2ec4e7.jpg',1,95,NULL,40,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-05-23 17:24:06','2019-06-22 09:40:06','2019-06-22 09:40:06',NULL),
(77,'测试商品C',1,60.00,50.00,'CCC','<p>CCCC</p>','2019/05/1bdd097dbffd477799a7aca949377b20.jpg','2019/05/1bdd097dbffd477799a7aca949377b20.jpg',1,95,NULL,50,'{\"hasUserPickUp\": false, \"hasShopDelivery\": true}',47,'2019-05-23 17:24:48','2019-06-22 09:39:59','2019-06-22 09:39:59',NULL);

/*Table structure for table `tz_prod_comm` */

DROP TABLE IF EXISTS `tz_prod_comm`;

CREATE TABLE `tz_prod_comm` (
  `prod_comm_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `prod_id` bigint(20) unsigned NOT NULL COMMENT '商品ID',
  `order_item_id` bigint(20) unsigned DEFAULT NULL COMMENT '订单项ID',
  `user_id` varchar(36) DEFAULT NULL COMMENT '评论用户ID',
  `content` varchar(500) DEFAULT '' COMMENT '评论内容',
  `reply_content` varchar(500) DEFAULT '' COMMENT '掌柜回复',
  `rec_time` datetime DEFAULT NULL COMMENT '记录时间',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `reply_sts` int(1) DEFAULT '0' COMMENT '是否回复 0:未回复  1:已回复',
  `postip` varchar(16) DEFAULT NULL COMMENT 'IP来源',
  `score` tinyint(2) DEFAULT '0' COMMENT '得分，0-5分',
  `useful_counts` int(11) DEFAULT '0' COMMENT '有用的计数',
  `pics` varchar(1000) DEFAULT NULL COMMENT '晒图的json字符串',
  `is_anonymous` int(1) DEFAULT '0' COMMENT '是否匿名(1:是  0:否)',
  `status` int(1) DEFAULT NULL COMMENT '是否显示，1:为显示，0:待审核， -1：不通过审核，不显示。 如果需要审核评论，则是0,，否则1',
  `evaluate` tinyint(2) DEFAULT NULL COMMENT '评价(0好评 1中评 2差评)',
  PRIMARY KEY (`prod_comm_id`),
  KEY `prod_id` (`prod_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品评论';

/*Data for the table `tz_prod_comm` */

/*Table structure for table `tz_prod_favorite` */

DROP TABLE IF EXISTS `tz_prod_favorite`;

CREATE TABLE `tz_prod_favorite` (
  `favorite_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `prod_id` bigint(20) unsigned NOT NULL COMMENT '商品ID',
  `rec_time` datetime NOT NULL COMMENT '收藏时间',
  `user_id` varchar(36) NOT NULL DEFAULT '' COMMENT '用户ID',
  PRIMARY KEY (`favorite_id`),
  KEY `prod_id` (`prod_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品收藏表';

/*Data for the table `tz_prod_favorite` */

/*Table structure for table `tz_prod_prop` */

DROP TABLE IF EXISTS `tz_prod_prop`;

CREATE TABLE `tz_prod_prop` (
  `prop_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '属性id',
  `prop_name` varchar(20) DEFAULT NULL COMMENT '属性名称',
  `rule` tinyint(2) DEFAULT NULL COMMENT 'ProdPropRule 1:销售属性(规格); 2:参数属性;',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  PRIMARY KEY (`prop_id`),
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4;

/*Data for the table `tz_prod_prop` */

insert  into `tz_prod_prop`(`prop_id`,`prop_name`,`rule`,`shop_id`) values
(80,'内存',1,1),
(81,'颜色',1,1);

/*Table structure for table `tz_prod_prop_value` */

DROP TABLE IF EXISTS `tz_prod_prop_value`;

CREATE TABLE `tz_prod_prop_value` (
  `value_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '属性值ID',
  `prop_value` varchar(20) DEFAULT NULL COMMENT '属性值名称',
  `prop_id` bigint(20) DEFAULT NULL COMMENT '属性ID',
  PRIMARY KEY (`value_id`),
  KEY `prop_id` (`prop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=387 DEFAULT CHARSET=utf8mb4;

/*Data for the table `tz_prod_prop_value` */

insert  into `tz_prod_prop_value`(`value_id`,`prop_value`,`prop_id`) values
(381,'32G',80),
(382,'64G',80),
(383,'128G',80),
(384,'红',81),
(385,'黄',81),
(386,'蓝',81);

/*Table structure for table `tz_prod_tag` */

DROP TABLE IF EXISTS `tz_prod_tag`;

CREATE TABLE `tz_prod_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组标签id',
  `title` varchar(36) DEFAULT NULL COMMENT '分组标题',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺Id',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态(1为正常,0为删除)',
  `is_default` tinyint(1) DEFAULT NULL COMMENT '默认类型(0:商家自定义,1:系统默认)',
  `prod_count` bigint(20) DEFAULT NULL COMMENT '商品数量',
  `style` int(10) DEFAULT NULL COMMENT '列表样式(0:一列一个,1:一列两个,2:一列三个)',
  `seq` int(10) DEFAULT NULL COMMENT '排序',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `delete_time` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='商品分组表';

/*Data for the table `tz_prod_tag` */

insert  into `tz_prod_tag`(`id`,`title`,`shop_id`,`status`,`is_default`,`prod_count`,`style`,`seq`,`create_time`,`update_time`,`delete_time`) values
(1,'每日上新',1,1,0,0,2,3,'2019-04-18 14:27:02','2019-04-18 14:27:06',NULL),
(2,'商城热卖',1,1,0,0,1,2,'2019-04-18 14:27:27','2019-04-18 14:27:30',NULL),
(3,'更多宝贝',1,1,1,0,0,1,'2019-04-18 10:07:17','2019-04-18 10:07:17',NULL);

/*Table structure for table `tz_prod_tag_reference` */

DROP TABLE IF EXISTS `tz_prod_tag_reference`;

CREATE TABLE `tz_prod_tag_reference` (
  `reference_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组引用id',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  `prod_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态(1:正常,0:删除)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`reference_id`)
) ENGINE=InnoDB AUTO_INCREMENT=342 DEFAULT CHARSET=utf8;

/*Data for the table `tz_prod_tag_reference` */

insert  into `tz_prod_tag_reference`(`reference_id`,`shop_id`,`tag_id`,`prod_id`,`status`,`create_time`) values
(25,1,1,60,1,'2019-04-21 21:29:19'),
(26,1,3,60,1,'2019-04-21 21:29:19'),
(29,1,3,62,1,'2019-04-21 21:35:13'),
(30,1,1,62,1,'2019-04-21 21:35:13'),
(31,1,2,63,1,'2019-04-21 21:37:22'),
(32,1,1,63,1,'2019-04-21 21:37:22'),
(33,1,3,63,1,'2019-04-21 21:37:22'),
(34,1,1,64,1,'2019-04-21 21:48:11'),
(35,1,2,64,1,'2019-04-21 21:48:11'),
(36,1,2,65,1,'2019-04-21 21:50:23'),
(37,1,3,65,1,'2019-04-21 21:50:23'),
(38,1,1,66,1,'2019-04-21 21:52:10'),
(39,1,3,66,1,'2019-04-21 21:52:10'),
(40,1,3,67,1,'2019-04-21 21:54:20'),
(41,1,1,67,1,'2019-04-21 21:54:20'),
(203,NULL,1,61,1,'2019-04-27 09:48:16'),
(204,NULL,2,61,1,'2019-04-27 09:48:16'),
(225,NULL,1,72,1,'2019-04-29 14:29:26'),
(226,NULL,2,72,1,'2019-04-29 14:29:26'),
(241,NULL,1,59,1,'2019-04-29 14:30:44'),
(242,NULL,2,59,1,'2019-04-29 14:30:44'),
(243,NULL,3,59,1,'2019-04-29 14:30:44'),
(255,NULL,1,71,1,'2019-05-21 11:02:01'),
(256,NULL,2,71,1,'2019-05-21 11:02:01'),
(257,NULL,1,73,1,'2019-05-21 13:56:12'),
(258,NULL,3,73,1,'2019-05-21 13:56:12'),
(271,NULL,2,68,1,'2019-05-22 10:30:39'),
(272,NULL,3,68,1,'2019-05-22 10:30:39'),
(273,NULL,1,68,1,'2019-05-22 10:30:39'),
(286,1,1,74,1,'2019-05-22 10:39:51'),
(287,1,2,74,1,'2019-05-22 10:39:51'),
(288,1,3,74,1,'2019-05-22 10:39:51'),
(313,NULL,1,69,1,'2019-05-23 20:17:04'),
(314,NULL,2,69,1,'2019-05-23 20:17:04'),
(315,NULL,3,69,1,'2019-05-23 20:17:04'),
(330,NULL,1,77,1,'2019-06-22 09:39:57'),
(331,NULL,2,77,1,'2019-06-22 09:39:57'),
(332,NULL,1,76,1,'2019-06-22 09:40:04'),
(333,NULL,2,76,1,'2019-06-22 09:40:04'),
(334,NULL,1,75,1,'2019-06-22 09:40:10'),
(335,NULL,2,75,1,'2019-06-22 09:40:10'),
(336,NULL,1,70,1,'2019-06-22 09:40:22'),
(337,NULL,2,70,1,'2019-06-22 09:40:22'),
(338,NULL,3,70,1,'2019-06-22 09:40:22'),
(339,NULL,1,18,1,'2019-06-22 18:28:31'),
(340,NULL,2,18,1,'2019-06-22 18:28:31'),
(341,NULL,3,18,1,'2019-06-22 18:28:31');

/*Table structure for table `tz_shop_detail` */

DROP TABLE IF EXISTS `tz_shop_detail`;

CREATE TABLE `tz_shop_detail` (
  `shop_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '店铺id',
  `shop_name` varchar(50) DEFAULT NULL COMMENT '店铺名称(数字、中文，英文(可混合，不可有特殊字符)，可修改)、不唯一',
  `user_id` varchar(36) DEFAULT NULL COMMENT '店长用户id',
  `shop_type` tinyint(2) DEFAULT NULL COMMENT '店铺类型',
  `intro` varchar(200) DEFAULT NULL COMMENT '店铺简介(可修改)',
  `shop_notice` varchar(50) DEFAULT NULL COMMENT '店铺公告(可修改)',
  `shop_industry` tinyint(2) DEFAULT NULL COMMENT '店铺行业(餐饮、生鲜果蔬、鲜花等)',
  `shop_owner` varchar(20) DEFAULT NULL COMMENT '店长',
  `mobile` varchar(20) DEFAULT NULL COMMENT '店铺绑定的手机(登录账号：唯一)',
  `tel` varchar(20) DEFAULT NULL COMMENT '店铺联系电话',
  `shop_lat` varchar(20) DEFAULT NULL COMMENT '店铺所在纬度(可修改)',
  `shop_lng` varchar(20) DEFAULT NULL COMMENT '店铺所在经度(可修改)',
  `shop_address` varchar(100) DEFAULT NULL COMMENT '店铺详细地址',
  `province` varchar(10) DEFAULT NULL COMMENT '店铺所在省份（描述）',
  `city` varchar(10) DEFAULT NULL COMMENT '店铺所在城市（描述）',
  `area` varchar(10) DEFAULT NULL COMMENT '店铺所在区域（描述）',
  `pca_code` varchar(20) DEFAULT NULL COMMENT '店铺省市区代码，用于回显',
  `shop_logo` varchar(200) DEFAULT NULL COMMENT '店铺logo(可修改)',
  `shop_photos` varchar(1000) DEFAULT NULL COMMENT '店铺相册',
  `open_time` varchar(100) DEFAULT NULL COMMENT '每天营业时间段(可修改)',
  `shop_status` tinyint(2) DEFAULT NULL COMMENT '店铺状态(-1:未开通 0: 停业中 1:营业中)，可修改',
  `transport_type` tinyint(2) DEFAULT NULL COMMENT '0:商家承担运费; 1:买家承担运费',
  `fixed_freight` decimal(15,2) DEFAULT NULL COMMENT '固定运费',
  `full_free_shipping` decimal(15,2) DEFAULT NULL COMMENT '满X包邮',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_distribution` tinyint(2) DEFAULT NULL COMMENT '分销开关(0:开启 1:关闭)',
  PRIMARY KEY (`shop_id`),
  UNIQUE KEY `mobile` (`mobile`),
  UNIQUE KEY `shop_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

/*Data for the table `tz_shop_detail` */

insert  into `tz_shop_detail`(`shop_id`,`shop_name`,`user_id`,`shop_type`,`intro`,`shop_notice`,`shop_industry`,`shop_owner`,`mobile`,`tel`,`shop_lat`,`shop_lng`,`shop_address`,`province`,`city`,`area`,`pca_code`,`shop_logo`,`shop_photos`,`open_time`,`shop_status`,`transport_type`,`fixed_freight`,`full_free_shipping`,`create_time`,`update_time`,`is_distribution`) values
(1,'mall4j小店1',NULL,NULL,'mall4j小店 爱你哟',NULL,NULL,NULL,NULL,'020-123456',NULL,NULL,'大学城北','广东省','广州市','番禺区','44/4401/440113','2018/08/78a6a63cf02d4965912bc5047f49afa0.jpg','2018/08/c7a50f443a85462d8129d83cf0f7eb91.jpg,2018/08/79791fc749444ef1ab4d2ca56fe9363f.jpg','00:00:00 - 00:20:00',1,NULL,NULL,NULL,'2018-08-30 11:10:05',NULL,1);

/*Table structure for table `tz_sku` */

DROP TABLE IF EXISTS `tz_sku`;

CREATE TABLE `tz_sku` (
  `sku_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '单品ID',
  `prod_id` bigint(20) unsigned NOT NULL COMMENT '商品ID',
  `properties` varchar(2000) DEFAULT '' COMMENT '销售属性组合字符串 格式是p1:v1;p2:v2',
  `ori_price` decimal(15,2) DEFAULT NULL COMMENT '原价',
  `price` decimal(15,2) DEFAULT NULL COMMENT '价格',
  `stocks` int(11) NOT NULL COMMENT '商品在付款减库存的状态下，该sku上未付款的订单数量',
  `actual_stocks` int(11) DEFAULT NULL COMMENT '实际库存',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `rec_time` datetime NOT NULL COMMENT '记录时间',
  `party_code` varchar(100) DEFAULT NULL COMMENT '商家编码',
  `model_id` varchar(100) DEFAULT NULL COMMENT '商品条形码',
  `pic` varchar(500) DEFAULT NULL COMMENT 'sku图片',
  `sku_name` varchar(120) DEFAULT NULL COMMENT 'sku名称',
  `prod_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  `weight` double DEFAULT NULL COMMENT '商品重量',
  `volume` double DEFAULT NULL COMMENT '商品体积',
  `status` tinyint(2) DEFAULT '1' COMMENT '0 禁用 1 启用',
  `is_delete` tinyint(2) DEFAULT NULL COMMENT '0 正常 1 已被删除',
  PRIMARY KEY (`sku_id`),
  KEY `prod_id` (`prod_id`)
) ENGINE=InnoDB AUTO_INCREMENT=405 DEFAULT CHARSET=utf8 COMMENT='单品SKU表';

/*Data for the table `tz_sku` */

insert  into `tz_sku`(`sku_id`,`prod_id`,`properties`,`ori_price`,`price`,`stocks`,`actual_stocks`,`update_time`,`rec_time`,`party_code`,`model_id`,`pic`,`sku_name`,`prod_name`,`version`,`weight`,`volume`,`status`,`is_delete`) values
(10,4,NULL,NULL,0.10,0,100,'2019-05-14 10:00:41','2018-09-14 14:05:52',NULL,NULL,NULL,'',NULL,10,NULL,NULL,1,NULL),
(116,13,'面积:75m²',25000000.00,25.00,2,200,'2018-11-22 11:42:59','2018-11-22 11:42:59','100001',NULL,'2018/11/d9316669736f48f7bd047a928e3b2972.jpg','75m² ','房子 75m² ',0,0,0,1,1),
(117,15,'',0.00,0.00,0,0,'2018-11-22 15:20:53','2018-11-22 15:20:53','',NULL,NULL,'','',0,0,0,1,1),
(118,16,'',0.00,0.00,0,0,'2018-11-22 15:21:22','2018-11-22 15:21:22','',NULL,NULL,'','',0,0,0,1,1),
(119,17,'',0.00,0.00,0,0,'2018-11-22 15:21:34','2018-11-22 15:21:34','',NULL,NULL,'','',0,0,0,1,1),
(120,18,'版本:公开版;颜色:金色;内存:64GB',10999.00,10999.00,3,3,'2018-12-29 13:48:46','2018-11-22 16:02:41','',NULL,NULL,'公开版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 64GB ',3,0,0,1,1),
(121,18,'版本:换修无忧版;颜色:金色;内存:64GB',10999.00,10999.00,5,6,'2018-12-10 14:47:51','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 64GB ',1,0,0,1,1),
(122,18,'版本:原厂延保版;颜色:金色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 64GB ',0,0,0,1,1),
(123,18,'版本:公开版;颜色:深空灰色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 64GB ',0,0,0,1,1),
(124,18,'版本:换修无忧版;颜色:深空灰色;内存:64GB',10999.00,10999.00,100,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ',0,0,0,1,1),
(125,18,'版本:原厂延保版;颜色:深空灰色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 64GB ',0,0,0,1,1),
(126,18,'版本:公开版;颜色:银色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 64GB ',0,0,0,1,1),
(127,18,'版本:换修无忧版;颜色:银色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 64GB ',0,0,0,1,1),
(128,18,'版本:原厂延保版;颜色:银色;内存:64GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 64GB ',0,0,0,1,1),
(129,18,'版本:公开版;颜色:金色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 256GB ',0,0,0,1,1),
(130,18,'版本:换修无忧版;颜色:金色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 256GB ',0,0,0,1,1),
(131,18,'版本:原厂延保版;颜色:金色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 256GB ',0,0,0,1,1),
(132,18,'版本:公开版;颜色:深空灰色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 256GB ',0,0,0,1,1),
(133,18,'版本:换修无忧版;颜色:深空灰色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 256GB ',0,0,0,1,1),
(134,18,'版本:原厂延保版;颜色:深空灰色;内存:256GB',10999.00,10999.00,107,100,'2019-05-16 11:30:31','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 256GB ',27,0,0,1,1),
(135,18,'版本:公开版;颜色:银色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 256GB ',0,0,0,1,1),
(136,18,'版本:换修无忧版;颜色:银色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 256GB ',0,0,0,1,1),
(137,18,'版本:原厂延保版;颜色:银色;内存:256GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 256GB ',0,0,0,1,1),
(138,18,'版本:公开版;颜色:金色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 512GB ',0,0,0,1,1),
(139,18,'版本:换修无忧版;颜色:金色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 512GB ',0,0,0,1,1),
(140,18,'版本:原厂延保版;颜色:金色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 512GB ',0,0,0,1,1),
(141,18,'版本:公开版;颜色:深空灰色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 512GB ',0,0,0,1,1),
(142,18,'版本:换修无忧版;颜色:深空灰色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 512GB ',0,0,0,1,1),
(143,18,'版本:原厂延保版;颜色:深空灰色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 512GB ',0,0,0,1,1),
(144,18,'版本:公开版;颜色:银色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'公开版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 512GB ',0,0,0,1,1),
(145,18,'版本:换修无忧版;颜色:银色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'换修无忧版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 512GB ',0,0,0,1,1),
(146,18,'版本:原厂延保版;颜色:银色;内存:512GB',10999.00,10999.00,0,0,'2018-11-22 16:02:41','2018-11-22 16:02:41','',NULL,NULL,'原厂延保版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 512GB ',0,0,0,1,1),
(256,59,'',0.00,0.00,0,0,'2019-04-21 19:15:35','2019-04-21 19:15:35','',NULL,NULL,'','安踏运动鞋',0,0,0,1,1),
(257,59,'颜色:深空灰色',999.00,100.00,89,89,'2019-04-21 19:49:37','2019-04-21 19:49:37','123187',NULL,'2019/04/16c40e0a9a2c403e8fa64af68f46e330.jpg','深空灰色 ','安踏运动鞋',0,287,899,1,1),
(258,18,'版本:公开版',19999.00,12999.00,999,999,'2019-04-21 20:35:38','2019-04-21 20:35:38','123123412',NULL,'2019/04/5efab0e2610a484c925e3ec9a47719d1.jpg','公开版 ','Apple iPhone XS Max 移动联通电信4G手机  公开版 ',0,213,331,1,1),
(259,18,'版本:换修无忧版',13412.00,12312.00,232,211,'2019-04-21 20:35:38','2019-04-21 20:35:38','1142411241',NULL,'2019/04/4f4db61a05044458bf4d08fdce497f47.jpg','换修无忧版 ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 ',0,123,232,1,1),
(260,60,'颜色:金色',299.00,199.00,233,233,'2019-04-21 21:29:19','2019-04-21 21:29:19','1312151235',NULL,'2019/04/1be7d4e321f24c188bb694c50dc37a8e.jpg','金色 ','休闲鞋 底板',0,123,243,1,1),
(261,61,'颜色:深空灰色',100.00,100.00,23,23,'2019-04-21 21:33:33','2019-04-21 21:33:33','',NULL,'2019/04/3c547f3855f94894bd1058286eecb8d9.jpg','深空灰色 ','爆款女装T恤青春',0,0,0,1,1),
(262,62,'颜色:金色',400.00,358.00,122,122,'2019-04-21 21:35:13','2019-04-21 21:35:13','',NULL,'2019/04/0cabb44de08345e2b5db9c9ea98ef1f3.jpg','金色 ','工装裤',0,0,0,1,1),
(263,63,'版本:公开版',233.00,233.00,43,34,'2019-04-21 21:37:21','2019-04-21 21:37:21','',NULL,'2019/04/b98a528660e84cf680c5098992d77bf9.jpg','公开版 ','公文包',0,0,0,1,1),
(264,64,'',234.00,233.00,234,234,'2019-04-21 21:48:11','2019-04-21 21:48:11','',NULL,NULL,'','美味曲奇',0,0,0,1,1),
(265,65,'',13.00,10.00,345,345,'2019-04-21 21:50:23','2019-04-21 21:50:23','',NULL,NULL,'','大大口香糖',0,0,0,1,1),
(266,66,'',14.00,12.00,574,574,'2019-04-21 21:52:10','2019-04-21 21:52:10','',NULL,NULL,'','乐事',0,0,0,1,1),
(267,67,'',65.00,54.00,346,344,'2019-04-21 21:54:20','2019-04-21 21:54:20','',NULL,NULL,'','旺仔牛奶',0,0,0,1,1),
(268,68,'',12.00,26.00,65,65,'2019-04-21 21:56:39','2019-04-21 21:56:39','',NULL,NULL,'','【Dole都乐】菲律宾都乐非转基因木瓜1只 单只约410g',0,0,0,1,0),
(269,69,'版本:公开版',3799.00,3599.00,239,239,'2019-04-21 22:10:05','2019-04-21 22:10:05','',NULL,'2019/04/44cbf97dea4c45cda2a374d575568698.jpg','公开版 ','华为P30',0,0,0,1,1),
(270,18,'版本:公开版',0.00,0.00,0,0,'2019-04-22 10:33:51','2019-04-22 10:33:51','',NULL,NULL,'','',0,0,0,0,1),
(271,18,'版本:换修无忧版',0.00,0.00,0,0,'2019-04-22 10:33:51','2019-04-22 10:33:51','',NULL,NULL,'','',0,0,0,1,1),
(272,70,'',2.00,1.00,20,NULL,'2019-04-22 16:42:57','2019-04-22 16:42:57',NULL,NULL,NULL,'','面包面包',0,0.1,0.3,1,1),
(273,70,'',3.00,2.00,4,NULL,'2019-04-22 17:55:09','2019-04-22 17:55:09',NULL,NULL,NULL,'','面包面包',0,5,6,1,1),
(274,70,'',2.00,1.00,3,NULL,'2019-04-22 17:57:00','2019-04-22 17:57:00',NULL,NULL,NULL,'','面包面包',0,4,5,1,1),
(275,71,'颜色:金色',20.00,10.00,999,NULL,'2019-04-23 15:43:25','2019-04-23 15:43:25',NULL,NULL,NULL,'','红豆糕点',0,10,1,1,1),
(276,70,'',0.00,0.00,0,NULL,'2019-04-23 15:49:01','2019-04-23 15:49:01',NULL,NULL,NULL,'','面包面包',0,0,0,1,1),
(277,59,'颜色:深空灰色',0.00,0.00,0,NULL,'2019-04-24 08:44:28','2019-04-24 08:44:28',NULL,NULL,NULL,'深空灰色 ','安踏运动鞋',0,0,0,1,1),
(278,59,'颜色:深空灰色',0.00,0.00,0,NULL,'2019-04-24 08:44:58','2019-04-24 08:44:58',NULL,NULL,NULL,'深空灰色 ','安踏运动鞋',0,0,0,1,1),
(279,70,'',2.00,1.00,3,NULL,'2019-04-24 18:04:01','2019-04-24 18:04:01',NULL,NULL,NULL,'','面包面包',0,4,5,0,1),
(282,71,'颜色:金色',20.00,10.00,999,NULL,'2019-04-25 10:17:17','2019-04-25 10:17:17',NULL,NULL,'2019/04/de38ac60fa5c4798bf6ed3c1c6403e34.jpg','金色 ','红豆糕点',0,0.1,1,1,1),
(283,72,'颜色:金色',1.00,5.00,999,NULL,'2019-04-25 10:22:20','2019-04-25 10:22:20',NULL,NULL,'2019/04/5bbffdf1f3674a6d942319432ab4bc79.jpg','','快乐汽水',0,0.2,0.3,1,1),
(284,71,'颜色:金色',0.00,0.00,0,NULL,'2019-04-25 10:28:21','2019-04-25 10:28:21',NULL,NULL,NULL,'金色 ','红豆糕点',0,0,0,1,1),
(285,72,'颜色:金色',0.00,0.00,0,NULL,'2019-04-25 10:29:11','2019-04-25 10:29:11',NULL,NULL,NULL,'金色 ','快乐汽水',0,0,0,1,1),
(286,72,'颜色:金色',0.00,0.00,0,NULL,'2019-04-25 10:30:11','2019-04-25 10:30:11',NULL,NULL,NULL,'金色 ','快乐汽水',0,0,0,1,1),
(287,72,'颜色:金色',0.00,0.00,0,NULL,'2019-04-25 10:30:41','2019-04-25 10:30:41',NULL,NULL,NULL,'金色 ','快乐汽水',0,0,0,1,1),
(288,18,'版本:公开版',0.00,0.00,100,NULL,'2019-04-25 19:08:32','2019-04-25 19:08:32',NULL,NULL,NULL,'公开版 ','Apple iPhone XS Max 移动联通电信4G手机  公开版 ',0,0,0,1,1),
(289,18,'版本:换修无忧版',0.00,0.00,99,NULL,'2019-04-25 19:08:32','2019-04-25 19:08:32',NULL,NULL,NULL,'换修无忧版 ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 ',0,0,0,1,1),
(290,18,'版本:公开版;颜色:金色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(291,18,'版本:换修无忧版;颜色:金色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(292,18,'版本:公开版;颜色:深空灰色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(293,18,'版本:换修无忧版;颜色:深空灰色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(294,18,'版本:公开版;颜色:金色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(295,18,'版本:换修无忧版;颜色:金色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(296,18,'版本:公开版;颜色:深空灰色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(297,18,'版本:换修无忧版;颜色:深空灰色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:08:51','2019-04-26 11:08:51',NULL,NULL,NULL,'','',0,0,0,1,1),
(298,18,'版本:公开版;颜色:金色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'公开版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 64GB ',0,0,0,1,1),
(299,18,'版本:换修无忧版;颜色:金色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'换修无忧版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 64GB ',0,0,0,1,1),
(300,18,'版本:公开版;颜色:深空灰色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'公开版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 64GB ',0,0,0,1,1),
(301,18,'版本:换修无忧版;颜色:深空灰色;内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'换修无忧版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ',0,0,0,1,1),
(302,18,'版本:公开版;颜色:金色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'公开版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 256GB ',0,0,0,1,1),
(303,18,'版本:换修无忧版;颜色:金色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'换修无忧版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 256GB ',0,0,0,1,1),
(304,18,'版本:公开版;颜色:深空灰色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'公开版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 256GB ',0,0,0,1,1),
(305,18,'版本:换修无忧版;颜色:深空灰色;内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:19:48','2019-04-26 11:19:48',NULL,NULL,NULL,'换修无忧版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 256GB ',0,0,0,1,1),
(306,18,'内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:26:28','2019-04-26 11:26:28',NULL,NULL,NULL,'64GB ','Apple iPhone XS Max eeeeee移动联通电信4G手机  64GB ',0,0,0,1,1),
(307,18,'内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:26:28','2019-04-26 11:26:28',NULL,NULL,NULL,'256GB ','Apple iPhone XS Max 移动联通电信4G手机  256GB ',0,0,0,1,1),
(308,18,'内存:64GB',0.00,0.00,0,NULL,'2019-04-26 11:26:47','2019-04-26 11:26:47',NULL,NULL,NULL,'64GB ','Apple iPhone XS Max qqqqq 移动联通电信4G手机  64GB ',0,0,0,1,1),
(309,18,'内存:256GB',0.00,0.00,0,NULL,'2019-04-26 11:26:47','2019-04-26 11:26:47',NULL,NULL,NULL,'256GB ','Apple iPhone XS Max 移动联通电信4G手机  256GB ',0,0,0,1,1),
(310,18,'版本:公开版;颜色:金色;内存:64GB',0.00,10.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'公开版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 64GB ',0,0,0,0,0),
(311,18,'版本:换修无忧版;颜色:金色;内存:64GB',0.00,9.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'换修无忧版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 64GB ',0,0,0,0,0),
(312,18,'版本:原厂延保版;颜色:金色;内存:64GB',0.00,8.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'原厂延保版 金色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 64GB ',0,0,0,0,0),
(313,18,'版本:公开版;颜色:深空灰色;内存:64GB',0.00,7.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'公开版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 64GB ',0,0,0,0,0),
(314,18,'版本:换修无忧版;颜色:深空灰色;内存:64GB',0.00,1.01,81,NULL,'2019-07-03 17:17:56','2019-04-26 11:40:06',NULL,NULL,'2019/04/2cd9ec641d92458983c00d87ff33ad57.jpg','换修无忧版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 64GB ',55,0,0,1,0),
(315,18,'版本:原厂延保版;颜色:深空灰色;内存:64GB',0.00,1.02,0,NULL,'2019-04-29 19:41:13','2019-04-26 11:40:06',NULL,NULL,'2019/04/bf4c40f7e6e5454e806c244ca49d3964.jpg','原厂延保版 深空灰色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 64GB ',2,0,0,1,0),
(316,18,'版本:公开版;颜色:银色;内存:64GB',0.00,7102.00,1,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'公开版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 64GB ',0,0,0,1,0),
(317,18,'版本:换修无忧版;颜色:银色;内存:64GB',0.00,7103.00,1,NULL,'2019-07-03 17:15:41','2019-04-26 11:40:06',NULL,NULL,NULL,'换修无忧版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 64GB ',1,0,0,1,0),
(318,18,'版本:原厂延保版;颜色:银色;内存:64GB',0.00,7104.00,1,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'原厂延保版 银色 64GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 64GB ',0,0,0,1,0),
(319,18,'版本:公开版;颜色:金色;内存:256GB',0.00,7600.00,1,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/1cfc26bed5084caaa439ed4e49b41885.jpg','公开版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 256GB ',0,0,0,1,0),
(320,18,'版本:换修无忧版;颜色:金色;内存:256GB',0.00,7601.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/a25a877f99684e1882eef5a5bb9081c4.jpg','换修无忧版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 256GB ',0,0,0,1,0),
(321,18,'版本:原厂延保版;颜色:金色;内存:256GB',0.00,7602.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/b119c50832d44622a07c7ed0ff10f647.jpg','原厂延保版 金色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 256GB ',0,0,0,1,0),
(322,18,'版本:公开版;颜色:深空灰色;内存:256GB',0.00,7603.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/98b8c67212994d9bb62ac35aedd1cc08.jpg','公开版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 256GB ',0,0,0,1,0),
(323,18,'版本:换修无忧版;颜色:深空灰色;内存:256GB',0.00,7604.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/e5cdcd2c310d4cf2b702523db1993dbd.jpg','换修无忧版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 256GB ',0,0,0,1,0),
(324,18,'版本:原厂延保版;颜色:深空灰色;内存:256GB',0.00,7605.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/9edd1e5a57d345678dd52308496457bb.jpg','原厂延保版 深空灰色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 256GB ',0,0,0,1,0),
(325,18,'版本:公开版;颜色:银色;内存:256GB',0.00,7606.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'公开版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 256GB ',0,0,0,1,0),
(326,18,'版本:换修无忧版;颜色:银色;内存:256GB',0.00,7607.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'换修无忧版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 256GB ',0,0,0,1,0),
(327,18,'版本:原厂延保版;颜色:银色;内存:256GB',0.00,7608.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'原厂延保版 银色 256GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 256GB ',0,0,0,1,0),
(328,18,'版本:公开版;颜色:金色;内存:512GB',0.00,8000.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/20824781b8f24771b12cf273450aaaae.jpg','公开版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 金色 512GB ',0,0,0,1,0),
(329,18,'版本:换修无忧版;颜色:金色;内存:512GB',0.00,8001.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/2c60b3e379e84d6bacfd7f04edc1a653.jpg','换修无忧版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 金色 512GB ',0,0,0,1,0),
(330,18,'版本:原厂延保版;颜色:金色;内存:512GB',0.00,8002.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/2e5239d4fd6e49e29c9f8e75edad7318.jpg','原厂延保版 金色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 金色 512GB ',0,0,0,1,0),
(331,18,'版本:公开版;颜色:深空灰色;内存:512GB',0.00,8003.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/525b9161f2424645bab7ca1b73e918d2.jpg','公开版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 深空灰色 512GB ',0,0,0,1,0),
(332,18,'版本:换修无忧版;颜色:深空灰色;内存:512GB',0.00,8004.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/a5dd840eadaa41afadfd7d16ed05b139.jpg','换修无忧版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 深空灰色 512GB ',0,0,0,1,0),
(333,18,'版本:原厂延保版;颜色:深空灰色;内存:512GB',0.00,8006.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,'2019/04/ae2c5c3b77e0437c93114cae3bfb20e0.jpg','原厂延保版 深空灰色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 深空灰色 512GB ',0,0,0,1,0),
(334,18,'版本:公开版;颜色:银色;内存:512GB',0.00,8000.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'公开版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  公开版 银色 512GB ',0,0,0,0,0),
(335,18,'版本:换修无忧版;颜色:银色;内存:512GB',0.00,8005.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'换修无忧版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  换修无忧版 银色 512GB ',0,0,0,1,0),
(336,18,'版本:原厂延保版;颜色:银色;内存:512GB',0.00,8000.00,0,NULL,'2019-04-26 11:40:06','2019-04-26 11:40:06',NULL,NULL,NULL,'原厂延保版 银色 512GB ','Apple iPhone XS Max 移动联通电信4G手机  原厂延保版 银色 512GB ',0,0,0,0,0),
(337,70,'',0.00,0.00,0,NULL,'2019-04-26 11:40:24','2019-04-26 11:40:24',NULL,NULL,NULL,'','面包面包',0,0,0,1,1),
(338,70,'',2.00,1.00,3,NULL,'2019-04-26 13:40:59','2019-04-26 13:40:59',NULL,NULL,NULL,'','面包面包',0,4,5,1,1),
(339,70,'版本:公开版',0.00,0.00,0,NULL,'2019-04-26 14:21:12','2019-04-26 14:21:12',NULL,NULL,NULL,'公开版 ','面包面包11 公开版 ',0,0,0,1,1),
(340,70,'版本:换修无忧版',0.00,0.00,0,NULL,'2019-04-26 14:21:12','2019-04-26 14:21:12',NULL,NULL,NULL,'换修无忧版 ','面包面包11 换修无忧版 ',0,0,0,1,1),
(341,70,'版本:公开版',2.00,1.00,3,NULL,'2019-04-26 14:21:33','2019-04-26 14:21:33',NULL,NULL,'2019/04/c6b85c1bce184dfebaceb9352e1bba2c.jpg','公开版 ','面包面包111 公开版 ',0,0,0,0,1),
(342,70,'版本:换修无忧版',5.00,4.00,6,NULL,'2019-04-26 14:21:33','2019-04-26 14:21:33',NULL,NULL,NULL,'换修无忧版 ','面包面包111 换修无忧版 ',0,0,0,1,1),
(343,70,'',2.00,1.00,3,NULL,'2019-04-26 15:14:04','2019-04-26 15:14:04',NULL,NULL,NULL,'','面包面包',0,4,5,1,1),
(344,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 15:15:44','2019-04-26 15:15:44',NULL,NULL,NULL,'绿豆馅 ','面包面包 绿豆馅 ',0,0,0,1,1),
(345,70,'馅料:红豆馅',0.00,0.00,0,NULL,'2019-04-26 15:15:44','2019-04-26 15:15:44',NULL,NULL,NULL,'红豆馅 ','面包面包 红豆馅 ',0,0,0,1,1),
(346,72,'尺寸:40*50;外框类型:曜石黑金属画框;颜色分类:金色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,'2019/04/2ae8beb3fb7445589754ad959482c186.jpg','40*50 曜石黑金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 曜石黑金属画框 金色 ',0,0,0,1,0),
(347,72,'尺寸:50*60;外框类型:曜石黑金属画框;颜色分类:金色',0.00,175.00,6,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 曜石黑金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 曜石黑金属画框 金色 ',0,0,0,1,0),
(348,72,'尺寸:60*80;外框类型:曜石黑金属画框;颜色分类:金色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 曜石黑金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 曜石黑金属画框 金色 ',0,0,0,1,0),
(349,72,'尺寸:40*50;外框类型:流砂金金属画框;颜色分类:金色',0.00,169.00,3,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,'2019/04/26bf2e07ecbf41b6a67029acb8f22cfc.jpg','40*50 流砂金金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 流砂金金属画框 金色 ',0,0,0,1,0),
(350,72,'尺寸:50*60;外框类型:流砂金金属画框;颜色分类:金色',0.00,175.00,3,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 流砂金金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 流砂金金属画框 金色 ',0,0,0,1,0),
(351,72,'尺寸:60*80;外框类型:流砂金金属画框;颜色分类:金色',0.00,182.00,3,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 流砂金金属画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 流砂金金属画框 金色 ',0,0,0,1,0),
(352,72,'尺寸:40*50;外框类型:轻奢金画框;颜色分类:金色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 轻奢金画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 轻奢金画框 金色 ',0,0,0,1,0),
(353,72,'尺寸:50*60;外框类型:轻奢金画框;颜色分类:金色',0.00,175.00,6,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 轻奢金画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 轻奢金画框 金色 ',0,0,0,1,0),
(354,72,'尺寸:60*80;外框类型:轻奢金画框;颜色分类:金色',0.00,179.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 轻奢金画框 金色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 轻奢金画框 金色 ',0,0,0,1,0),
(355,72,'尺寸:40*50;外框类型:曜石黑金属画框;颜色分类:粉红色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 曜石黑金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 曜石黑金属画框 粉红色 ',0,0,0,1,0),
(356,72,'尺寸:50*60;外框类型:曜石黑金属画框;颜色分类:粉红色',0.00,175.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,'2019/04/8bd6d1e21886429f87f9964c338efee8.jpg','50*60 曜石黑金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 曜石黑金属画框 粉红色 ',0,0,0,1,0),
(357,72,'尺寸:60*80;外框类型:曜石黑金属画框;颜色分类:粉红色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 曜石黑金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 曜石黑金属画框 粉红色 ',0,0,0,1,0),
(358,72,'尺寸:40*50;外框类型:流砂金金属画框;颜色分类:粉红色',0.00,169.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 流砂金金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 流砂金金属画框 粉红色 ',0,0,0,1,0),
(359,72,'尺寸:50*60;外框类型:流砂金金属画框;颜色分类:粉红色',0.00,175.00,6,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 流砂金金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 流砂金金属画框 粉红色 ',0,0,0,1,0),
(360,72,'尺寸:60*80;外框类型:流砂金金属画框;颜色分类:粉红色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 流砂金金属画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 流砂金金属画框 粉红色 ',0,0,0,1,0),
(361,72,'尺寸:40*50;外框类型:轻奢金画框;颜色分类:粉红色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,'2019/04/41f7b496551149e185f92e36bb4aff79.jpg','40*50 轻奢金画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 轻奢金画框 粉红色 ',0,0,0,1,0),
(362,72,'尺寸:50*60;外框类型:轻奢金画框;颜色分类:粉红色',0.00,175.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 轻奢金画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 轻奢金画框 粉红色 ',0,0,0,1,0),
(363,72,'尺寸:60*80;外框类型:轻奢金画框;颜色分类:粉红色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 轻奢金画框 粉红色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 轻奢金画框 粉红色 ',0,0,0,1,0),
(364,72,'尺寸:40*50;外框类型:曜石黑金属画框;颜色分类:银色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 曜石黑金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 曜石黑金属画框 银色 ',0,0,0,1,0),
(365,72,'尺寸:50*60;外框类型:曜石黑金属画框;颜色分类:银色',0.00,175.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 曜石黑金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 曜石黑金属画框 银色 ',0,0,0,1,0),
(366,72,'尺寸:60*80;外框类型:曜石黑金属画框;颜色分类:银色',0.00,182.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 曜石黑金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 曜石黑金属画框 银色 ',0,0,0,1,0),
(367,72,'尺寸:40*50;外框类型:流砂金金属画框;颜色分类:银色',0.00,169.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 流砂金金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 流砂金金属画框 银色 ',0,0,0,1,0),
(368,72,'尺寸:50*60;外框类型:流砂金金属画框;颜色分类:银色',0.00,175.00,3,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 流砂金金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 流砂金金属画框 银色 ',0,0,0,1,0),
(369,72,'尺寸:60*80;外框类型:流砂金金属画框;颜色分类:银色',0.00,182.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 流砂金金属画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 流砂金金属画框 银色 ',0,0,0,1,0),
(370,72,'尺寸:40*50;外框类型:轻奢金画框;颜色分类:银色',0.00,169.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 轻奢金画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 轻奢金画框 银色 ',0,0,0,1,0),
(371,72,'尺寸:50*60;外框类型:轻奢金画框;颜色分类:银色',0.00,175.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 轻奢金画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 轻奢金画框 银色 ',0,0,0,1,0),
(372,72,'尺寸:60*80;外框类型:轻奢金画框;颜色分类:银色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 轻奢金画框 银色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 轻奢金画框 银色 ',0,0,0,1,0),
(373,72,'尺寸:40*50;外框类型:曜石黑金属画框;颜色分类:暗黑色',0.00,169.00,0,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 曜石黑金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 曜石黑金属画框 暗黑色 ',0,0,0,1,0),
(374,72,'尺寸:50*60;外框类型:曜石黑金属画框;颜色分类:暗黑色',0.00,175.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 曜石黑金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 曜石黑金属画框 暗黑色 ',0,0,0,1,0),
(375,72,'尺寸:60*80;外框类型:曜石黑金属画框;颜色分类:暗黑色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 曜石黑金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 曜石黑金属画框 暗黑色 ',0,0,0,1,0),
(376,72,'尺寸:40*50;外框类型:流砂金金属画框;颜色分类:暗黑色',0.00,169.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 流砂金金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 流砂金金属画框 暗黑色 ',0,0,0,0,0),
(377,72,'尺寸:50*60;外框类型:流砂金金属画框;颜色分类:暗黑色',0.00,175.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 流砂金金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 流砂金金属画框 暗黑色 ',0,0,0,1,0),
(378,72,'尺寸:60*80;外框类型:流砂金金属画框;颜色分类:暗黑色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 流砂金金属画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 流砂金金属画框 暗黑色 ',0,0,0,1,0),
(379,72,'尺寸:40*50;外框类型:轻奢金画框;颜色分类:暗黑色',0.00,169.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'40*50 轻奢金画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 40*50 轻奢金画框 暗黑色 ',0,0,0,1,0),
(380,72,'尺寸:50*60;外框类型:轻奢金画框;颜色分类:暗黑色',0.00,175.00,4,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'50*60 轻奢金画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 50*60 轻奢金画框 暗黑色 ',0,0,0,0,0),
(381,72,'尺寸:60*80;外框类型:轻奢金画框;颜色分类:暗黑色',0.00,182.00,5,NULL,'2019-04-26 17:01:57','2019-04-26 17:01:57',NULL,NULL,NULL,'60*80 轻奢金画框 暗黑色 ','餐厅装饰画现代简约饭厅挂画墙面壁画好好吃饭 60*80 轻奢金画框 暗黑色 ',0,0,0,1,0),
(382,71,'颜色:真爱粉;尺寸:3.5英寸',0.00,0.00,0,NULL,'2019-04-26 17:49:28','2019-04-26 17:49:28',NULL,NULL,'2019/04/e50714c046364201a49120774f12b664.jpg','真爱粉 3.5英寸 ','旗舰店官网 自拍神器 梵高定制 真爱粉 3.5英寸 ',0,0,0,1,1),
(383,71,'颜色:心水金;尺寸:3.5英寸',0.00,0.00,0,NULL,'2019-04-26 17:49:28','2019-04-26 17:49:28',NULL,NULL,'2019/04/55121264771049828f024c3d79070381.jpg','心水金 3.5英寸 ','旗舰店官网 自拍神器 梵高定制 心水金 3.5英寸 ',0,0,0,1,1),
(384,71,'颜色:心水金;尺寸:3.6 寸',0.00,6999.00,100,NULL,'2019-04-26 18:02:37','2019-04-26 18:02:37',NULL,NULL,'2019/04/a570dfccaedd45adbb91cbfb2e1c5553.jpg','心水金 3.6 寸 ','旗舰店官网 自拍神器 梵高定制 心水金 3.6 寸 ',0,0,0,1,0),
(385,71,'颜色:真爱粉;尺寸:3.6 寸',0.00,6998.00,79,NULL,'2019-06-24 09:36:33','2019-04-26 18:02:37',NULL,NULL,'2019/04/17ba70d217644839a381df0dc3682b11.jpg','真爱粉 3.6 寸 ','旗舰店官网 自拍神器 梵高定制 真爱粉 3.6 寸 ',11,0,0,1,0),
(386,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 18:03:32','2019-04-26 18:03:32',NULL,NULL,NULL,'绿豆馅 ','面包面包',0,0,0,1,1),
(387,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 18:04:39','2019-04-26 18:04:39',NULL,NULL,NULL,'绿豆馅 ','面包面包',0,0,0,1,1),
(388,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 18:05:18','2019-04-26 18:05:18',NULL,NULL,NULL,'绿豆馅 ','面包面包',0,0,0,1,1),
(389,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 18:06:04','2019-04-26 18:06:04',NULL,NULL,NULL,'绿豆馅 ','面包面包 绿豆馅 ',0,0,0,1,1),
(390,70,'馅料:111',0.00,0.00,0,NULL,'2019-04-26 18:06:04','2019-04-26 18:06:04',NULL,NULL,NULL,'111 ','面包面包 111 333',0,0,0,1,1),
(391,70,'馅料:222',0.00,0.00,0,NULL,'2019-04-26 18:06:04','2019-04-26 18:06:04',NULL,NULL,NULL,'222 ','面包面包 222 ',0,0,0,1,1),
(392,70,'馅料:绿豆馅',0.00,0.00,0,NULL,'2019-04-26 18:06:15','2019-04-26 18:06:15',NULL,NULL,NULL,'绿豆馅 ','面包面包',0,0,0,1,1),
(393,59,'化妆品含量:400ml',450.00,420.00,62,NULL,'2019-04-26 19:04:28','2019-04-26 19:04:28',NULL,NULL,'2019/04/8ba6bde1cce04d5fb235d700e4dcfbef.jpg','400ml ','兰蔻粉水清滢柔肤水400ml 爽肤水女保湿舒缓滋润嫩肤',0,0,0,1,0),
(394,69,'鞋码:41;颜色分类:原灰白/浅灰/蒸汽绿',0.00,1199.00,3,NULL,'2019-04-26 19:20:31','2019-04-26 19:20:31',NULL,NULL,'2019/04/ac107d12d30e4279b53957fcac7e12e2.jpg','41 原灰白/浅灰/蒸汽绿 ','阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956 41 原灰白/浅灰/蒸汽绿 ',0,0,0,1,0),
(395,69,'鞋码:42;颜色分类:原灰白/浅灰/蒸汽绿',0.00,1199.00,5,NULL,'2019-04-26 19:20:31','2019-04-26 19:20:31',NULL,NULL,'2019/04/73acdb3c367f4c66960d579b70da5b93.jpg','42 原灰白/浅灰/蒸汽绿 ','阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956 42 原灰白/浅灰/蒸汽绿 ',0,0,0,1,0),
(396,69,'鞋码:42.5;颜色分类:原灰白/浅灰/蒸汽绿',0.00,1199.00,5,NULL,'2019-04-26 19:20:31','2019-04-26 19:20:31',NULL,NULL,'2019/04/e57dcc9f1d74498cb11c0a3ceb3c9785.jpg','42.5 原灰白/浅灰/蒸汽绿 ','阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956 42.5 原灰白/浅灰/蒸汽绿 ',0,0,0,1,0),
(397,69,'鞋码:43;颜色分类:原灰白/浅灰/蒸汽绿',0.00,1199.00,7,NULL,'2019-04-26 19:20:31','2019-04-26 19:20:31',NULL,NULL,'2019/04/90ff942af7c949d2a00e4e83c4c24ce7.jpg','43 原灰白/浅灰/蒸汽绿 ','阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956 43 原灰白/浅灰/蒸汽绿 ',0,0,0,1,0),
(398,69,'鞋码:43.5;颜色分类:原灰白/浅灰/蒸汽绿',0.00,1199.00,7,NULL,'2019-04-26 19:20:31','2019-04-26 19:20:31',NULL,NULL,'2019/04/01c585d96f2b4e2b9693833e859afd49.jpg','43.5 原灰白/浅灰/蒸汽绿 ','阿迪达斯官方 adidas 三叶草 NITE JOGGER 男子经典鞋BD7956 43.5 原灰白/浅灰/蒸汽绿 ',0,0,0,1,0),
(399,70,'',0.00,38.00,15,NULL,'2019-04-26 20:56:19','2019-04-26 20:56:19',NULL,NULL,NULL,'','【Dole都乐】比利时Truval啤梨12只 进口水果新鲜梨 单果120g左右',0,0,0,1,0),
(400,73,'规格:芒果味 184克',0.00,22.00,82,10,'2019-06-24 14:52:28','2019-04-27 11:37:12',NULL,NULL,'2019/04/8de2ccaa66874d42986c73692b25f441.jpg','芒果味 184克 ','阴阳师寮办秘制 月见和菓子 184克',22,0,0,1,0),
(401,74,'',120.00,100.00,100,NULL,'2019-05-22 10:39:51','2019-05-22 10:39:51',NULL,NULL,NULL,'','旺仔牛奶',0,1,1,1,0),
(402,75,'',30.00,30.00,30,NULL,'2019-06-19 20:25:10','2019-05-23 17:23:12',NULL,NULL,NULL,'','测试商品A',5,0,0,1,0),
(403,76,'',40.00,40.00,40,NULL,'2019-06-24 14:53:48','2019-05-23 17:24:06',NULL,NULL,NULL,'','测试商品B',5,0,0,1,0),
(404,77,'',60.00,50.00,50,NULL,'2019-05-23 17:24:48','2019-05-23 17:24:48',NULL,NULL,NULL,'','测试商品C',0,0,0,1,0);

/*Table structure for table `tz_sms_log` */

DROP TABLE IF EXISTS `tz_sms_log`;

CREATE TABLE `tz_sms_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户id',
  `user_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号码',
  `content` varchar(100) NOT NULL DEFAULT '' COMMENT '短信内容',
  `mobile_code` varchar(50) NOT NULL DEFAULT '' COMMENT '手机验证码',
  `type` int(1) NOT NULL DEFAULT '0' COMMENT '短信类型  1:注册  2:验证',
  `rec_date` datetime NOT NULL COMMENT '发送时间',
  `response_code` varchar(50) DEFAULT NULL COMMENT '发送短信返回码',
  `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态  1:有效  0：失效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='短信记录表';

/*Table structure for table `tz_sys_config` */

DROP TABLE IF EXISTS `tz_sys_config`;

CREATE TABLE `tz_sys_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `param_key` varchar(50) DEFAULT NULL COMMENT 'key',
  `param_value` varchar(2000) DEFAULT NULL COMMENT 'value',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`param_key`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='系统配置信息表';

/*Data for the table `tz_sys_config` */

/*Table structure for table `tz_sys_log` */

DROP TABLE IF EXISTS `tz_sys_log`;

CREATE TABLE `tz_sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation` varchar(50) DEFAULT NULL COMMENT '用户操作',
  `method` varchar(200) DEFAULT NULL COMMENT '请求方法',
  `params` varchar(5000) DEFAULT NULL COMMENT '请求参数',
  `time` bigint(20) NOT NULL COMMENT '执行时长(毫秒)',
  `ip` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=846 DEFAULT CHARSET=utf8 COMMENT='系统日志';

/*Data for the table `tz_sys_log` */

insert  into `tz_sys_log`(`id`,`username`,`operation`,`method`,`params`,`time`,`ip`,`create_date`) values
(831,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','168',8,'127.0.0.1','2019-06-26 14:04:22'),
(832,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','229',1,'127.0.0.1','2019-06-26 14:31:31'),
(833,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','231',6,'127.0.0.1','2019-06-26 14:31:37'),
(834,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','232',8,'127.0.0.1','2019-06-26 14:31:38'),
(835,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','233',25,'127.0.0.1','2019-06-26 14:31:42'),
(836,'admin','删除菜单','com.yami.shop.sys.controller.SysMenuController.delete()','229',6,'127.0.0.1','2019-06-26 14:31:46'),
(837,'admin','删除分类','com.yami.shop.admin.controller.CategoryController.delete()','92',536,'127.0.0.1','2019-07-01 11:14:38'),
(838,'admin','保存角色','com.yami.shop.sys.controller.SysRoleController.save()','{\"roleId\":1,\"roleName\":\"管理员\",\"remark\":\"测试\",\"menuIdList\":[34,70,71,72,73,74,75,136,137,138,139,140,239,240,241,242,243,306,307,308,309,310,51,57,58,59,60,63,300,301,302,303,305,131,132,133,134,135,163,164,165,166,167,174,175,176,177,178,201,202,203,204,205,206,125,126,127,128,129,130,230,91,92,93,99,100,101,107,108,146,184,185,1,312,313,314,315,316,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,6,7,8,9,10,11,12,13,14,27,29,-666666],\"createTime\":\"2019-07-03T00:39:49.339+0000\"}',106,'127.0.0.1','2019-07-03 08:39:49'),
(839,'admin','保存配置','com.yami.shop.sys.controller.SysConfigController.save()','{\"id\":1,\"paramKey\":\"测试\",\"paramValue\":\"1\",\"remark\":\"1\"}',17,'127.0.0.1','2019-07-03 08:57:59'),
(840,'admin','删除配置','com.yami.shop.sys.controller.SysConfigController.delete()','[1]',18,'127.0.0.1','2019-07-03 08:58:20'),
(841,'admin','新增公告管理','com.yami.shop.admin.controller.NoticeController.save()','{\"id\":4,\"shopId\":1,\"title\":\"测试\",\"content\":\"<p>1111</p>\",\"status\":1,\"isTop\":1,\"publishTime\":\"2019-07-03T02:05:55.730+0000\",\"updateTime\":\"2019-07-03T02:05:55.730+0000\"}',26,'127.0.0.1','2019-07-03 10:05:56'),
(842,'admin','删除公告管理','com.yami.shop.admin.controller.NoticeController.removeById()','4',37,'127.0.0.1','2019-07-03 20:00:24');

/*Table structure for table `tz_sys_menu` */

DROP TABLE IF EXISTS `tz_sys_menu`;

CREATE TABLE `tz_sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父菜单ID，一级菜单为0',
  `name` varchar(50) DEFAULT NULL COMMENT '菜单名称',
  `url` varchar(200) DEFAULT NULL COMMENT '菜单URL',
  `perms` varchar(500) DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
  `type` int(11) DEFAULT NULL COMMENT '类型   0：目录   1：菜单   2：按钮',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `order_num` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=317 DEFAULT CHARSET=utf8 COMMENT='菜单管理';

/*Data for the table `tz_sys_menu` */

insert  into `tz_sys_menu`(`menu_id`,`parent_id`,`name`,`url`,`perms`,`type`,`icon`,`order_num`) values
(1,0,'系统管理','','',0,'system',3),
(2,1,'管理员列表','sys/user','',1,'admin',1),
(3,1,'角色管理','sys/role','',1,'role',2),
(4,1,'菜单管理','sys/menu','',1,'menu',3),
(15,2,'查看',NULL,'sys:user:page,sys:user:info',2,NULL,0),
(16,2,'新增','','sys:user:save,sys:role:list',2,'',1),
(17,2,'修改','','sys:user:update,sys:role:list',2,'',2),
(18,2,'删除','','sys:user:delete',2,'',3),
(19,3,'查看',NULL,'sys:role:page,sys:role:info',2,NULL,0),
(20,3,'新增',NULL,'sys:role:save,sys:menu:list',2,NULL,0),
(21,3,'修改',NULL,'sys:role:update,sys:menu:list',2,NULL,0),
(22,3,'删除',NULL,'sys:role:delete',2,NULL,0),
(23,4,'查看',NULL,'sys:menu:list,sys:menu:info',2,NULL,0),
(24,4,'新增',NULL,'sys:menu:save,sys:menu:select',2,NULL,0),
(25,4,'修改',NULL,'sys:menu:update,sys:menu:select',2,NULL,0),
(26,4,'删除',NULL,'sys:menu:delete',2,NULL,0),
(27,1,'参数管理','sys/config','sys:config:page,sys:config:info,sys:config:save,sys:config:update,sys:config:delete',1,'config',6),
(29,1,'系统日志','sys/log','sys:log:page',1,'log',7),
(34,0,'产品管理','','',0,'admin',0),
(51,34,'规格管理','prod/spec','',1,'',2),
(57,51,'查看','','prod:spec:page',2,'',0),
(58,51,'添加','','prod:spec:save',2,'',1),
(59,51,'修改','','prod:spec:update,prod:spec:info',2,'',2),
(60,51,'删除','','prod:spec:delete',2,'',3),
(63,0,'门店管理','','',0,'store',0),
(70,34,'产品管理','prod/prodList','',1,NULL,0),
(71,70,'产品管理','','prod:prod:page',2,NULL,0),
(72,70,'查看产品','','prod:prod:info',2,NULL,0),
(73,70,'新增产品','','prod:prod:save',2,NULL,0),
(74,70,'修改产品','','prod:prod:update',2,NULL,0),
(75,70,'删除产品','','prod:prod:delete',2,NULL,0),
(91,0,'订单管理','','',0,'order',2),
(92,91,'订单管理','order/order','',1,NULL,1),
(93,92,'查看','','order:order:page',2,NULL,0),
(99,92,'保存','','order:order:save',2,NULL,0),
(100,92,'修改','','order:order:update',2,NULL,0),
(101,92,'删除','','order:order:delete',2,NULL,0),
(107,92,'详情','','order:order:info',2,NULL,0),
(108,92,'支付','','order:order:pay',2,NULL,0),
(125,0,'会员管理','','',0,'vip',0),
(126,125,'会员管理','user/user','',1,NULL,0),
(127,126,'查看','','admin:user:page',2,NULL,0),
(128,126,'新增','','admin:user:save',2,NULL,0),
(129,126,'修改','','admin:user:update,admin:user:info',2,NULL,0),
(130,126,'删除','','admin:user:delete',2,NULL,0),
(131,63,'自提点管理','shop/pickAddr','',1,'',0),
(132,131,'查看','','shop:pickAddr:page',2,NULL,0),
(133,131,'保存','','shop:pickAddr:save',2,NULL,0),
(134,131,'修改','','shop:pickAddr:update,shop:pickAddr:info',2,NULL,0),
(135,131,'删除','','shop:pickAddr:delete',2,NULL,0),
(136,34,'分类管理','prod/category','',1,NULL,0),
(137,136,'查看','','prod:category:page',2,NULL,0),
(138,136,'新增','','prod:category:save',2,NULL,0),
(139,136,'修改','','prod:category:info,prod:category:update',2,NULL,0),
(140,136,'删除','','prod:category:delete',2,NULL,0),
(146,92,'发货','','order:order:delivery',2,'',0),
(163,63,'运费模板','shop/transport','',1,NULL,0),
(164,163,'查看','','shop:transport:page,shop:shopDetail:info,shop:transfee:info,admin:area:page,shop:transcity:info',2,'',0),
(165,163,'修改','','shop:transport:update,shop:transport:info,shop:transfee:update,admin:area:page',2,'',0),
(166,163,'新增','','shop:transport:save,shop:transfee:save',2,'',0),
(167,163,'删除','','shop:transport:delete,shop:transfee:delete',2,'',0),
(174,63,'轮播图管理','admin/indexImg','',1,'',0),
(175,174,'查看','','admin:indexImg:page',2,'',0),
(176,174,'新增','','admin:indexImg:save',2,'',0),
(177,174,'修改','','admin:indexImg:info,admin:indexImg:update',2,'',0),
(178,174,'删除','','admin:indexImg:delete',2,'',0),
(184,92,'导出待发货订单','','order:order:waitingConsignmentExcel',2,'',0),
(185,92,'导出销售记录','','order:order:soldExcel',2,'',0),
(201,63,'热搜管理','shop/hotSearch','',1,'',0),
(202,201,'查询热搜','','admin:hotSearch:page',2,'',0),
(203,201,'查询热搜详情','','admin:hotSearch:page',2,'',0),
(204,201,'添加热搜','','admin:hotSearch:save',2,'',0),
(205,201,'修改热搜','','admin:hotSearch:update',2,'',0),
(206,201,'删除热搜','','admin:hotSearch:delete',2,'',0),
(230,126,'添加','','user:addr:save',2,'',0),
(239,34,'分组管理','prod/prodTag','prod:prodTag',1,'',0),
(240,239,'添加商品分组','','prod:prodTag:save',2,'',0),
(241,239,'修改商品分组','','prod:prodTag:update',2,'',0),
(242,239,'删除商品分组','','prod:prodTag:delete',2,'',0),
(243,239,'查看商品分组','','prod:prodTag:info,prod:prodTag:page',2,'',0),
(300,63,'公告管理','shop/notice','',1,'',0),
(301,300,'添加公告','','shop:notice:save',2,'',0),
(302,300,'修改公告','','shop:notice:update',2,'',0),
(303,300,'查看公告','','shop:notice:info,shop:notice:page',2,'',0),
(305,300,'删除公告','','shop:notice:delete',2,'',0),
(306,34,'评论管理','prod/prodComm','',1,'',1),
(307,306,'查看','','prod:prodComm:page,prod:prodComm:info',2,'',0),
(308,306,'添加','','prod:prodComm:save',2,'',0),
(309,306,'修改','','prod:prodComm:update',2,'',0),
(310,306,'删除','','prod:prodComm:delete',2,'',0),
(312,1,'地址管理','sys/area','',1,'dangdifill',0),
(313,312,'新增地址','','admin:area:save',2,'',0),
(314,312,'修改地址','','admin:area:update',2,'',0),
(315,312,'删除地址','','admin:area:delete',2,'',0),
(316,312,'查看地址','','admin:area:info,admin:area:page,admin:area:list',2,'',0),
(317,1,'修改测试用户','','aTest:aTestUser:update',1,'',0),
(318,1,'新增测试用户','','aTest:aTestUser:save',1,'',0),
(319,1,'删除测试用户','','aTest:aTestUser:delete',1,'',0);

/*Table structure for table `tz_sys_role` */

DROP TABLE IF EXISTS `tz_sys_role`;

CREATE TABLE `tz_sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(100) DEFAULT NULL COMMENT '角色名称',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='角色';

/*Data for the table `tz_sys_role` */

insert  into `tz_sys_role`(`role_id`,`role_name`,`remark`,`create_user_id`,`create_time`) values
(1,'管理员','测试',NULL,'2019-07-03 08:39:49');

/*Table structure for table `tz_sys_role_menu` */

DROP TABLE IF EXISTS `tz_sys_role_menu`;

CREATE TABLE `tz_sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `menu_id` bigint(20) DEFAULT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8 COMMENT='角色与菜单对应关系';

/*Data for the table `tz_sys_role_menu` */

insert  into `tz_sys_role_menu`(`id`,`role_id`,`menu_id`) values
(1,1,34),
(2,1,70),
(3,1,71),
(4,1,72),
(5,1,73),
(6,1,74),
(7,1,75),
(8,1,136),
(9,1,137),
(10,1,138),
(11,1,139),
(12,1,140),
(13,1,239),
(14,1,240),
(15,1,241),
(16,1,242),
(17,1,243),
(18,1,306),
(19,1,307),
(20,1,308),
(21,1,309),
(22,1,310),
(23,1,51),
(24,1,57),
(25,1,58),
(26,1,59),
(27,1,60),
(28,1,63),
(29,1,300),
(30,1,301),
(31,1,302),
(32,1,303),
(33,1,305),
(34,1,131),
(35,1,132),
(36,1,133),
(37,1,134),
(38,1,135),
(39,1,163),
(40,1,164),
(41,1,165),
(42,1,166),
(43,1,167),
(44,1,174),
(45,1,175),
(46,1,176),
(47,1,177),
(48,1,178),
(49,1,201),
(50,1,202),
(51,1,203),
(52,1,204),
(53,1,205),
(54,1,206),
(55,1,125),
(56,1,126),
(57,1,127),
(58,1,128),
(59,1,129),
(60,1,130),
(61,1,230),
(62,1,91),
(63,1,92),
(64,1,93),
(65,1,99),
(66,1,100),
(67,1,101),
(68,1,107),
(69,1,108),
(70,1,146),
(71,1,184),
(72,1,185),
(73,1,1),
(74,1,312),
(75,1,313),
(76,1,314),
(77,1,315),
(78,1,316),
(79,1,2),
(80,1,15),
(81,1,16),
(82,1,17),
(83,1,18),
(84,1,3),
(85,1,19),
(86,1,20),
(87,1,21),
(88,1,22),
(89,1,4),
(90,1,23),
(91,1,24),
(92,1,25),
(93,1,26),
(94,1,6),
(95,1,7),
(96,1,8),
(97,1,9),
(98,1,10),
(99,1,11),
(100,1,12),
(101,1,13),
(102,1,14),
(103,1,27),
(104,1,29),
(105,1,-666666);

/*Table structure for table `tz_sys_user` */

DROP TABLE IF EXISTS `tz_sys_user`;

CREATE TABLE `tz_sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(100) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态  0：禁用   1：正常',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '用户所在的商城Id',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='系统用户';

/*Data for the table `tz_sys_user` */
/* 初始账号密码为 admin/123456*/
insert  into `tz_sys_user`(`user_id`,`username`,`password`,`email`,`mobile`,`status`,`create_user_id`,`create_time`,`shop_id`) values
(1,'admin','{bcrypt}$2a$10$AV9Xz.3ck4RsXiad5ArcBO5.ZKwlpcnJzs740BHY..fsSp0PnM/Zu','<EMAIL>','11111111111',1,1,'2016-11-11 11:11:11',1);

/*Table structure for table `tz_sys_user_role` */

DROP TABLE IF EXISTS `tz_sys_user_role`;

CREATE TABLE `tz_sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户与角色对应关系';

/*Data for the table `tz_sys_user_role` */

/*Table structure for table `tz_transcity` */

DROP TABLE IF EXISTS `tz_transcity`;

CREATE TABLE `tz_transcity` (
  `transcity_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfee_id` bigint(20) DEFAULT NULL COMMENT '运费项id',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市id',
  PRIMARY KEY (`transcity_id`),
  KEY `transfee_id` (`transfee_id`),
  KEY `city_id` (`city_id`)
) ENGINE=InnoDB AUTO_INCREMENT=667 DEFAULT CHARSET=utf8;

/*Data for the table `tz_transcity` */

insert  into `tz_transcity`(`transcity_id`,`transfee_id`,`city_id`) values
(655,119,440118000000),
(656,119,440117000000),
(657,119,440115000000),
(658,119,440114000000),
(659,119,440113000000),
(660,119,440112000000),
(661,119,440111000000),
(662,119,440106000000),
(663,119,440105000000),
(664,119,440104000000),
(665,119,440103000000),
(666,119,440101000000);

/*Table structure for table `tz_transcity_free` */

DROP TABLE IF EXISTS `tz_transcity_free`;

CREATE TABLE `tz_transcity_free` (
  `transcity_free_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '指定条件包邮城市项id',
  `transfee_free_id` bigint(20) DEFAULT NULL COMMENT '指定条件包邮项id',
  `free_city_id` bigint(20) DEFAULT NULL COMMENT '城市id',
  PRIMARY KEY (`transcity_free_id`),
  KEY `transfee_free_id` (`transfee_free_id`),
  KEY `city_id` (`free_city_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2326 DEFAULT CHARSET=utf8;

/*Data for the table `tz_transcity_free` */

insert  into `tz_transcity_free`(`transcity_free_id`,`transfee_free_id`,`free_city_id`) values
(18,NULL,52994),
(19,NULL,52995),
(339,32,2376),
(1351,49,72),
(1352,49,2800),
(1353,49,2801),
(1354,49,2802),
(1355,49,2803),
(1356,49,2804),
(1357,49,2805),
(1358,49,2806),
(1359,49,2807),
(1360,49,2808),
(1361,49,2809),
(1362,49,2810),
(1363,49,2812),
(1364,49,2814),
(1365,49,2816),
(1366,49,2901),
(1367,49,2953),
(1368,49,3065),
(1369,49,78),
(1370,49,2813),
(1371,49,2815),
(1372,49,2817),
(1373,49,2820),
(1374,49,2822),
(1375,49,2823),
(1376,49,2824),
(1377,49,2825),
(1378,49,2826),
(1379,49,2830),
(1380,49,2833),
(1381,49,2834),
(1382,49,2835),
(1383,49,2837),
(1384,49,2841),
(1385,49,2919),
(1386,49,51035),
(1387,49,51036),
(1388,49,51037),
(1389,49,51038),
(1390,49,51039),
(1391,49,51040),
(1392,49,51041),
(1393,49,51042),
(1394,49,51043),
(1395,49,51044),
(1396,49,51045),
(1397,49,51046),
(1398,49,51047),
(1399,49,51048),
(1400,49,51049),
(1401,49,51050),
(1402,49,51051),
(1403,49,51052),
(1404,49,113),
(1405,49,114),
(1406,49,115),
(1407,49,119),
(1408,49,123),
(1409,49,126),
(1410,49,128),
(1411,49,129),
(1412,49,130),
(1413,49,131),
(1414,49,132),
(1415,49,133),
(1416,49,134),
(1417,49,135),
(1418,49,136),
(1419,49,137),
(1420,49,138),
(1421,49,139),
(1422,49,140),
(1423,49,141),
(1424,49,4164),
(1425,49,48131),
(1426,49,48132),
(1427,49,48133),
(1428,49,48201),
(1429,49,48202),
(1430,49,48203),
(1431,49,48204),
(1432,49,48205),
(1433,49,48206),
(1434,49,48207),
(1435,49,50950),
(1436,49,50951),
(1437,49,50952),
(1438,49,50953),
(1439,49,50954),
(1440,49,50995),
(1441,49,51026),
(1442,49,51027),
(1443,49,51028),
(1444,49,142),
(1445,49,148),
(1446,49,164),
(1447,49,199),
(1448,49,224),
(1449,49,239),
(1450,49,248),
(1451,49,258),
(1452,49,264),
(1453,49,274),
(1454,49,275),
(1455,49,412),
(1456,49,420),
(1457,49,427),
(1458,49,438),
(1459,49,446),
(1460,49,454),
(1461,49,458),
(1462,49,468),
(1463,49,475),
(1464,49,482),
(1465,49,489),
(1466,49,495),
(1467,49,502),
(1468,49,517),
(1469,49,527),
(1470,49,538),
(1471,49,549),
(1472,49,2780),
(1473,49,904),
(1474,49,911),
(1475,49,919),
(1476,49,925),
(1477,49,933),
(1478,49,939),
(1479,49,951),
(1480,49,959),
(1481,49,965),
(1482,49,972),
(1483,49,978),
(1484,49,984),
(1485,49,988),
(1486,49,1114),
(1487,49,1116),
(1488,49,1121),
(1489,49,1124),
(1490,49,1127),
(1491,49,1132),
(1492,49,1137),
(1493,49,1140),
(1494,49,1151),
(1495,49,1159),
(1496,49,1167),
(1497,49,1174),
(1498,49,1180),
(1499,49,1201),
(1500,49,1206),
(1501,49,2971),
(1502,49,1158),
(1503,49,1213),
(1504,49,1233),
(1505,49,1243),
(1506,49,1250),
(1507,49,1255),
(1508,49,1262),
(1509,49,1273),
(1510,49,1280),
(1511,49,1290),
(1512,49,1298),
(1513,49,1303),
(1514,49,1315),
(1515,49,1317),
(1516,49,1329),
(1517,49,1332),
(1518,49,1341),
(1519,49,1352),
(1520,49,1362),
(1521,49,1370),
(1522,49,1381),
(1523,49,1387),
(1524,49,1396),
(1525,49,1405),
(1526,49,1413),
(1527,49,1421),
(1528,49,1432),
(1529,49,1441),
(1530,49,1458),
(1531,49,1466),
(1532,49,1475),
(1533,49,1477),
(1534,49,1479),
(1535,49,2922),
(1536,49,2980),
(1537,49,2983),
(1538,49,3154),
(1539,49,1482),
(1540,49,1488),
(1541,49,1495),
(1542,49,1501),
(1543,49,1511),
(1544,49,1522),
(1545,49,1530),
(1546,49,1540),
(1547,49,1544),
(1548,49,1555),
(1549,49,1560),
(1550,49,1574),
(1551,49,1586),
(1552,49,1592),
(1553,49,1601),
(1554,49,1607),
(1555,49,1609),
(1556,49,1611),
(1557,49,1617),
(1558,49,1627),
(1559,49,1634),
(1560,49,1643),
(1561,49,1650),
(1562,49,1655),
(1563,49,1657),
(1564,49,1659),
(1565,49,1666),
(1566,49,1672),
(1567,49,1677),
(1568,49,1684),
(1569,49,1690),
(1570,49,1698),
(1571,49,1704),
(1572,49,1705),
(1573,49,1709),
(1574,49,1715),
(1575,49,1720),
(1576,49,1726),
(1577,49,1740),
(1578,49,1746),
(1579,49,1749),
(1580,49,1753),
(1581,49,1757),
(1582,49,1761),
(1583,49,1792),
(1584,49,1806),
(1585,49,1818),
(1586,49,3044),
(1587,49,3168),
(1588,49,1827),
(1589,49,1832),
(1590,49,1836),
(1591,49,1842),
(1592,49,1845),
(1593,49,1857),
(1594,49,1861),
(1595,49,1874),
(1596,49,1885),
(1597,49,1898),
(1598,49,1911),
(1599,49,1930),
(1600,49,1946),
(1601,49,1950),
(1602,49,1954),
(1603,49,1960),
(1604,49,1962),
(1605,49,1977),
(1606,49,1983),
(1607,49,1988),
(1608,49,1993),
(1609,49,2005),
(1610,49,2016),
(1611,49,2022),
(1612,49,2033),
(1613,49,2042),
(1614,49,2047),
(1615,49,2058),
(1616,49,2065),
(1617,49,2070),
(1618,49,2084),
(1619,49,2103),
(1620,49,2121),
(1621,49,3034),
(1622,49,3115),
(1623,49,3137),
(1624,49,3173),
(1625,49,3690),
(1626,49,3698),
(1627,49,3699),
(1628,49,3701),
(1629,49,3702),
(1630,49,3703),
(1631,49,3704),
(1632,49,3705),
(1633,49,3706),
(1634,49,3707),
(1635,49,3708),
(1636,49,3709),
(1637,49,3710),
(1638,49,3711),
(1639,49,2144),
(1640,49,2150),
(1641,49,2155),
(1642,49,2169),
(1643,49,2180),
(1644,49,2189),
(1645,49,2196),
(1646,49,2205),
(1647,49,2222),
(1648,49,2235),
(1649,49,2247),
(1650,49,2258),
(1651,49,2270),
(1652,49,2281),
(1653,49,2291),
(1654,49,2298),
(1655,49,2304),
(1656,49,2309),
(1657,49,2318),
(1658,49,2332),
(1659,49,2336),
(1660,49,2347),
(1661,49,2360),
(1662,49,2366),
(1663,49,4108),
(1975,51,72),
(1976,51,2800),
(1977,51,2801),
(1978,51,2802),
(1979,51,2803),
(1980,51,2804),
(1981,51,2805),
(1982,51,2806),
(1983,51,2807),
(1984,51,2808),
(1985,51,2809),
(1986,51,2810),
(1987,51,2812),
(1988,51,2814),
(1989,51,2816),
(1990,51,2901),
(1991,51,2953),
(1992,51,3065),
(1993,51,78),
(1994,51,2813),
(1995,51,2815),
(1996,51,2817),
(1997,51,2820),
(1998,51,2822),
(1999,51,2823),
(2000,51,2824),
(2001,51,2825),
(2002,51,2826),
(2003,51,2830),
(2004,51,2833),
(2005,51,2834),
(2006,51,2835),
(2007,51,2837),
(2008,51,2841),
(2009,51,2919),
(2010,51,51035),
(2011,51,51036),
(2012,51,51037),
(2013,51,51038),
(2014,51,51039),
(2015,51,51040),
(2016,51,51041),
(2017,51,51042),
(2018,51,51043),
(2019,51,51044),
(2020,51,51045),
(2021,51,51046),
(2022,51,51047),
(2023,51,51048),
(2024,51,51049),
(2025,51,51050),
(2026,51,51051),
(2027,51,51052),
(2028,51,113),
(2029,51,114),
(2030,51,115),
(2031,51,119),
(2032,51,123),
(2033,51,126),
(2034,51,128),
(2035,51,129),
(2036,51,130),
(2037,51,131),
(2038,51,132),
(2039,51,133),
(2040,51,134),
(2041,51,135),
(2042,51,136),
(2043,51,137),
(2044,51,138),
(2045,51,139),
(2046,51,140),
(2047,51,141),
(2048,51,4164),
(2049,51,48131),
(2050,51,48132),
(2051,51,48133),
(2052,51,48201),
(2053,51,48202),
(2054,51,48203),
(2055,51,48204),
(2056,51,48205),
(2057,51,48206),
(2058,51,48207),
(2059,51,50950),
(2060,51,50951),
(2061,51,50952),
(2062,51,50953),
(2063,51,50954),
(2064,51,50995),
(2065,51,51026),
(2066,51,51027),
(2067,51,51028),
(2068,51,142),
(2069,51,148),
(2070,51,164),
(2071,51,199),
(2072,51,224),
(2073,51,239),
(2074,51,248),
(2075,51,258),
(2076,51,264),
(2077,51,274),
(2078,51,275),
(2079,51,412),
(2080,51,420),
(2081,51,427),
(2082,51,438),
(2083,51,446),
(2084,51,454),
(2085,51,458),
(2086,51,468),
(2087,51,475),
(2088,51,482),
(2089,51,489),
(2090,51,495),
(2091,51,502),
(2092,51,517),
(2093,51,527),
(2094,51,538),
(2095,51,549),
(2096,51,2780),
(2097,51,904),
(2098,51,911),
(2099,51,919),
(2100,51,925),
(2101,51,933),
(2102,51,939),
(2103,51,951),
(2104,51,959),
(2105,51,965),
(2106,51,972),
(2107,51,978),
(2108,51,984),
(2109,51,988),
(2110,51,1000),
(2111,51,1007),
(2112,51,1016),
(2113,51,1022),
(2114,51,1025),
(2115,51,1032),
(2116,51,1042),
(2117,51,1053),
(2118,51,1058),
(2119,51,1060),
(2120,51,1072),
(2121,51,1081),
(2122,51,1090),
(2123,51,1099),
(2124,51,1108),
(2125,51,1112),
(2126,51,2900),
(2127,51,1114),
(2128,51,1116),
(2129,51,1121),
(2130,51,1124),
(2131,51,1127),
(2132,51,1132),
(2133,51,1137),
(2134,51,1140),
(2135,51,1151),
(2136,51,1159),
(2137,51,1167),
(2138,51,1174),
(2139,51,1180),
(2140,51,1201),
(2141,51,1206),
(2142,51,2971),
(2143,51,1158),
(2144,51,1213),
(2145,51,1233),
(2146,51,1243),
(2147,51,1250),
(2148,51,1255),
(2149,51,1262),
(2150,51,1273),
(2151,51,1280),
(2152,51,1290),
(2153,51,1298),
(2154,51,1303),
(2155,51,1315),
(2156,51,1317),
(2157,51,1329),
(2158,51,1332),
(2159,51,1341),
(2160,51,1352),
(2161,51,1362),
(2162,51,1370),
(2163,51,1381),
(2164,51,1387),
(2165,51,1396),
(2166,51,1405),
(2167,51,1413),
(2168,51,1421),
(2169,51,1432),
(2170,51,1441),
(2171,51,1458),
(2172,51,1466),
(2173,51,1475),
(2174,51,1477),
(2175,51,1479),
(2176,51,2922),
(2177,51,2980),
(2178,51,2983),
(2179,51,3154),
(2180,51,1482),
(2181,51,1488),
(2182,51,1495),
(2183,51,1501),
(2184,51,1511),
(2185,51,1522),
(2186,51,1530),
(2187,51,1540),
(2188,51,1544),
(2189,51,1555),
(2190,51,1560),
(2191,51,1574),
(2192,51,1586),
(2193,51,1592),
(2194,51,1601),
(2195,51,1607),
(2196,51,1609),
(2197,51,1611),
(2198,51,1617),
(2199,51,1627),
(2200,51,1634),
(2201,51,1643),
(2202,51,1650),
(2203,51,1655),
(2204,51,1657),
(2205,51,1659),
(2206,51,1666),
(2207,51,1672),
(2208,51,1677),
(2209,51,1684),
(2210,51,1690),
(2211,51,1698),
(2212,51,1704),
(2213,51,1705),
(2214,51,1709),
(2215,51,1715),
(2216,51,1720),
(2217,51,1726),
(2218,51,1740),
(2219,51,1746),
(2220,51,1749),
(2221,51,1753),
(2222,51,1757),
(2223,51,1761),
(2224,51,1792),
(2225,51,1806),
(2226,51,1818),
(2227,51,3044),
(2228,51,3168),
(2229,51,1827),
(2230,51,1832),
(2231,51,1836),
(2232,51,1842),
(2233,51,1845),
(2234,51,1857),
(2235,51,1861),
(2236,51,1874),
(2237,51,1885),
(2238,51,1898),
(2239,51,1911),
(2240,51,1930),
(2241,51,1946),
(2242,51,1950),
(2243,51,1954),
(2244,51,1960),
(2245,51,1962),
(2246,51,1977),
(2247,51,1983),
(2248,51,1988),
(2249,51,1993),
(2250,51,2005),
(2251,51,2016),
(2252,51,2022),
(2253,51,2033),
(2254,51,2042),
(2255,51,2047),
(2256,51,2058),
(2257,51,2065),
(2258,51,2070),
(2259,51,2084),
(2260,51,2103),
(2261,51,2144),
(2262,51,2150),
(2263,51,2155),
(2264,51,2169),
(2265,51,2180),
(2266,51,2189),
(2267,51,2196),
(2268,51,2205),
(2269,51,2222),
(2270,51,2235),
(2271,51,2247),
(2272,51,2258),
(2273,51,2270),
(2274,51,2281),
(2275,51,2291),
(2276,51,2298),
(2277,51,2304),
(2278,51,2309),
(2279,51,2318),
(2280,51,2332),
(2281,51,2336),
(2282,51,2347),
(2283,51,2360),
(2284,51,2366),
(2285,51,4108),
(2286,58,24145),
(2287,58,24146),
(2288,58,24147),
(2289,58,24148),
(2290,58,24149),
(2291,58,24150),
(2292,58,24151),
(2293,58,24152),
(2294,58,24153),
(2295,58,24154),
(2296,58,24155),
(2297,58,24156),
(2298,58,24157),
(2299,58,24158),
(2300,58,24159),
(2301,58,24160),
(2302,58,24161),
(2303,58,24162),
(2304,58,24163),
(2305,58,24164),
(2306,58,24165),
(2307,58,24166),
(2308,58,24167),
(2309,58,24168),
(2310,58,24169),
(2311,58,24170),
(2312,58,24171),
(2313,58,24172),
(2314,58,51839),
(2315,59,5538),
(2316,60,440301000000),
(2317,60,440303000000),
(2318,60,440304000000),
(2319,60,440305000000),
(2320,60,440306000000),
(2321,60,440307000000),
(2322,60,440308000000),
(2323,60,440309000000),
(2324,60,440310000000),
(2325,60,440311000000);

/*Table structure for table `tz_transfee` */

DROP TABLE IF EXISTS `tz_transfee`;

CREATE TABLE `tz_transfee` (
  `transfee_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '运费项id',
  `transport_id` bigint(20) DEFAULT NULL COMMENT '运费模板id',
  `continuous_piece` decimal(15,2) DEFAULT NULL COMMENT '续件数量',
  `first_piece` decimal(15,2) DEFAULT NULL COMMENT '首件数量',
  `continuous_fee` decimal(15,2) DEFAULT NULL COMMENT '续件费用',
  `first_fee` decimal(15,2) DEFAULT NULL COMMENT '首件费用',
  PRIMARY KEY (`transfee_id`),
  KEY `transport_id` (`transport_id`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8;

/*Data for the table `tz_transfee` */

insert  into `tz_transfee`(`transfee_id`,`transport_id`,`continuous_piece`,`first_piece`,`continuous_fee`,`first_fee`) values
(101,50,0.02,0.01,1.00,2.00),
(115,47,1.00,1.00,0.00,0.00),
(118,48,7.00,7.00,7.00,7.00),
(119,48,1.00,1.00,1.00,1.00),
(120,51,4.00,7.00,6.00,8.00);

/*Table structure for table `tz_transfee_free` */

DROP TABLE IF EXISTS `tz_transfee_free`;

CREATE TABLE `tz_transfee_free` (
  `transfee_free_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '指定条件包邮项id',
  `transport_id` bigint(20) DEFAULT NULL COMMENT '运费模板id',
  `free_type` tinyint(2) DEFAULT NULL COMMENT '包邮方式 （0 满x件/重量/体积包邮 1满金额包邮 2满x件/重量/体积且满金额包邮）',
  `amount` decimal(15,2) DEFAULT NULL COMMENT '需满金额',
  `piece` decimal(15,2) DEFAULT NULL COMMENT '包邮x件/重量/体积',
  PRIMARY KEY (`transfee_free_id`),
  KEY `transport_id` (`transport_id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8;

/*Data for the table `tz_transfee_free` */

insert  into `tz_transfee_free`(`transfee_free_id`,`transport_id`,`free_type`,`amount`,`piece`) values
(32,53,1,88.00,NULL),
(49,67,0,NULL,1.75),
(51,68,0,NULL,3.50),
(58,56,0,NULL,10.00),
(59,56,1,10.00,NULL),
(60,51,0,300.00,1.00);

/*Table structure for table `tz_transport` */

DROP TABLE IF EXISTS `tz_transport`;

CREATE TABLE `tz_transport` (
  `transport_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '运费模板id',
  `trans_name` varchar(36) DEFAULT NULL COMMENT '运费模板名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `charge_type` tinyint(2) DEFAULT NULL COMMENT '收费方式（0 按件数,1 按重量 2 按体积）',
  `is_free_fee` tinyint(2) DEFAULT NULL COMMENT '是否包邮 0:不包邮 1:包邮',
  `has_free_condition` tinyint(2) DEFAULT NULL COMMENT '是否含有包邮条件 0 否 1是',
  PRIMARY KEY (`transport_id`),
  KEY `shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8;

/*Data for the table `tz_transport` */

insert  into `tz_transport`(`transport_id`,`trans_name`,`create_time`,`shop_id`,`charge_type`,`is_free_fee`,`has_free_condition`) values
(47,'包邮','2018-11-21 17:17:07',1,0,1,0),
(48,'广州单独计算运费','2018-11-21 17:20:09',1,0,0,0),
(51,'深圳包邮','2019-04-13 13:42:38',1,0,0,1);

/*Table structure for table `tz_user` */

DROP TABLE IF EXISTS `tz_user`;

CREATE TABLE `tz_user` (
  `user_id` varchar(36) NOT NULL DEFAULT '' COMMENT 'ID',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `user_mail` varchar(100) DEFAULT NULL COMMENT '用户邮箱',
  `login_password` varchar(255) DEFAULT NULL COMMENT '登录密码',
  `pay_password` varchar(50) DEFAULT NULL COMMENT '支付密码',
  `user_mobile` varchar(50) DEFAULT NULL COMMENT '手机号码',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `user_regtime` datetime NOT NULL COMMENT '注册时间',
  `user_regip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `user_lasttime` datetime DEFAULT NULL COMMENT '最后登录时间',
  `user_lastip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `user_memo` varchar(500) DEFAULT NULL COMMENT '备注',
  `sex` char(1) DEFAULT 'M' COMMENT 'M(男) or F(女)',
  `birth_date` char(10) DEFAULT NULL COMMENT '例如：2009-11-27',
  `pic` varchar(255) DEFAULT NULL COMMENT '头像图片路径',
  `status` int(1) NOT NULL DEFAULT '1' COMMENT '状态 1 正常 0 无效',
  `score` int(11) DEFAULT NULL COMMENT '用户积分',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `ud_user_mail` (`user_mail`),
  UNIQUE KEY `ud_user_unique_mobile` (`user_mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';

/*Data for the table `tz_user` */

insert  into `tz_user`(`user_id`,`nick_name`,`real_name`,`user_mail`,`login_password`,`pay_password`,`user_mobile`,`modify_time`,`user_regtime`,`user_regip`,`user_lasttime`,`user_lastip`,`user_memo`,`sex`,`birth_date`,`pic`,`status`,`score`) values
('51540df5255e4d22903b0f83921095ff','.','',NULL,NULL,NULL,NULL,'2019-06-26 14:37:04','2019-06-26 14:37:04',NULL,NULL,NULL,NULL,'M',NULL,'https://wx.qlogo.cn/mmopen/vi_32/krmdRqHOnNAFoGzW2Ssy6pmzAMQryWphiaDZU5AsAR0AMvuLzlcBLtjEe8VlUAMIiah8juTPWa839BnOn1C0Oykg/132',1,NULL),
('5f159317be5b4dc4bf3188f1a3da0369','Leo','',NULL,NULL,NULL,NULL,'2019-07-02 10:56:53','2019-07-02 10:56:53',NULL,NULL,NULL,NULL,'M',NULL,'https://wx.qlogo.cn/mmopen/vi_32/hOM3grD77lIBdbdY0UXg4ql4NI1BeyJxlh1gPxgdnMbGzWCicahV4Ukt0pRkmo068d5CaSlM76xDrDDAsYFje6Q/132',1,NULL);

/*Table structure for table `tz_user_addr` */

DROP TABLE IF EXISTS `tz_user_addr`;

CREATE TABLE `tz_user_addr` (
  `addr_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` varchar(36) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `receiver` varchar(50) DEFAULT NULL COMMENT '收货人',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省ID',
  `province` varchar(100) DEFAULT NULL COMMENT '省',
  `city` varchar(20) DEFAULT NULL COMMENT '城市',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `area` varchar(20) DEFAULT NULL COMMENT '区',
  `area_id` bigint(20) DEFAULT NULL COMMENT '区ID',
  `post_code` varchar(15) DEFAULT NULL COMMENT '邮编',
  `addr` varchar(1000) DEFAULT NULL COMMENT '地址',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机',
  `status` int(1) NOT NULL COMMENT '状态,1正常，0无效',
  `common_addr` int(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址 1是',
  `create_time` datetime NOT NULL COMMENT '建立时间',
  `version` int(5) NOT NULL DEFAULT '0' COMMENT '版本号',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`addr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='用户配送地址';

/*Data for the table `tz_user_addr` */

insert  into `tz_user_addr`(`addr_id`,`user_id`,`receiver`,`province_id`,`province`,`city`,`city_id`,`area`,`area_id`,`post_code`,`addr`,`mobile`,`status`,`common_addr`,`create_time`,`version`,`update_time`) values
(3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市','市辖区',110100000000,'东城区',110101000000,NULL,'b613','15000000000',1,1,'2019-07-01 18:05:53',0,'2019-07-01 18:05:53');

/*Table structure for table `tz_user_addr_order` */

DROP TABLE IF EXISTS `tz_user_addr_order`;

CREATE TABLE `tz_user_addr_order` (
  `addr_order_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `addr_id` bigint(20) unsigned NOT NULL COMMENT '地址ID',
  `user_id` varchar(36) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `receiver` varchar(50) DEFAULT NULL COMMENT '收货人',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省ID',
  `province` varchar(100) DEFAULT NULL COMMENT '省',
  `area_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
  `area` varchar(20) DEFAULT NULL COMMENT '区',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `city` varchar(20) DEFAULT NULL COMMENT '城市',
  `addr` varchar(1000) DEFAULT NULL COMMENT '地址',
  `post_code` varchar(15) DEFAULT NULL COMMENT '邮编',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机',
  `create_time` datetime NOT NULL COMMENT '建立时间',
  `version` int(5) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`addr_order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='用户订单配送地址';

/*Data for the table `tz_user_addr_order` */

insert  into `tz_user_addr_order`(`addr_order_id`,`addr_id`,`user_id`,`receiver`,`province_id`,`province`,`area_id`,`area`,`city_id`,`city`,`addr`,`post_code`,`mobile`,`create_time`,`version`) values
(1,3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市',110101000000,'东城区',110100000000,'市辖区','b613',NULL,'15000000000','2019-07-01 18:07:11',0),
(2,3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市',110101000000,'东城区',110100000000,'市辖区','b613',NULL,'15000000000','2019-07-03 17:13:06',0),
(3,3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市',110101000000,'东城区',110100000000,'市辖区','b613',NULL,'15000000000','2019-07-03 17:15:41',0),
(5,3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市',110101000000,'东城区',110100000000,'市辖区','b613',NULL,'15000000000','2019-07-03 17:16:36',0),
(6,3,'51540df5255e4d22903b0f83921095ff','user',110000000000,'北京市',110101000000,'东城区',110100000000,'市辖区','b613',NULL,'15000000000','2019-07-03 17:17:56',0);

/*Table structure for table `tz_user_collection` */

DROP TABLE IF EXISTS `tz_user_collection`;

CREATE TABLE `tz_user_collection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏表',
  `prod_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `user_id` varchar(36) NOT NULL COMMENT '用户id',
  `create_time` datetime DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Data for the table `tz_user_collection` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
