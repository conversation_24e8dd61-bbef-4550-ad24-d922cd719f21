## 商品分组

#### 商城应用

在mall4j精选商城首页中，可以看到有`每日上新`、`商城热卖`、`更多商品`等标签栏，在每一栏位中用来展示特定的商品列表，如下图：。

![1566266497255](.\img\小程序-分组商品.png)

在后台中，我们可以对分组标签进行管理

![1566358291520](.\img\后台分组位置.png)



![1566358576330](.\img\后台新增标签.png)

后台指定绑定商品所指定的标签![1566267024882](.\img\后台-商品分组.png)



店铺商品分组有**两种**分组类型：

- 系统内置

  更多宝贝：在新增商品的时候，如果用户没有新增任何的分组标签，系统默认提供了一个默认标签。系统内置的标签不能够被删除。

- 商家自定义分组标签

   用户可以通过自定义分组标签，在首页根据自定义的分组情况对商品的经行展示。

#### 数据库设计

整体实体类关系如下图：

![1566357194240](.\img\分组管理数据库设计2.png)

model  实体类

商品标签类:

```java
@Data
@TableName("tz_prod_tag")
public class ProdTag implements Serializable {
    private static final long serialVersionUID = 1991508792679311621L;
    /**
     * 分组标签id
     */
    @TableId
    private Long id;
    /**
     * 店铺Id
     */
    private Long shopId;
    /**
     * 分组标题
     */
    private String title;
    /**
     * 状态(1为正常,0为删除)
     */
    private Integer status;
    /**
     * 默认类型(0:商家自定义,1:系统默认类型)
     */
    private Integer isDefault;
    /**
     * 商品数量
     */
    private Long prodCount;
    /**
     * 排序
     */
    private Integer seq;
    /**
     * 列表样式(0:一列一个,1:一列两个,2:一列三个)
     */
    private Integer style;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除时间
     */
    private Date deleteTime;
}

```

- `id` ，商品分组编号，自增
- `shopId` ，店铺ID

​       用于取分每个店铺，可扩展为B2B2C模式

- `status` ，删除时，1为正常，0为删除
- `title`, 分组标题
- `isDefault` 是否为默认类型
  - 商家自定义：每日上新，商城热卖等
  - 系统内置：更多宝贝，默认内置的标签不能被删除，在用户
- `prodCount`，商品数量统计
- `seq` 排序顺序
- `style`列表样式(0:一列一个，1:一列两个，2:一列三个) ，用于扩展开发，用户可以根据自己喜欢的排版布局，对商品布局进行排版

商品分组引用：商品分组**引用**。一个商品可以有多个商品分组。

```java
@Data
@TableName("tz_prod_tag_reference")
public class ProdTagReference implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 分组引用id
     */
    @TableId
    private Long referenceId;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 标签id
     */
    private Long tagId;
    /**
     * 商品id
     */
    private Long prodId;
    /**
     * 状态(1:正常,0:删除)
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
}		
```

- `referenceId` ，分组引用ID
- `shopId`  ， 标识所属的店铺，用于取分每个店铺
- `tagId`， 所指向的标签ID
- `prodId`，所指向的商品ID
- `createTime` 创建时间

