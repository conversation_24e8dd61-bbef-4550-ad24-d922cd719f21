使用`mybatis plus` 进行分页的时候，是无法进行一对多、多对多的分页的。最主要的原因是因为，该框架无法清楚count的依据是什么，以哪个表算出来的行数为准，但是我们所有的分页格式已经统一好使用`IPage`对象了，那么该如何适配一对多、多对多分页呢？



## PageAdapter

使用分页时，前端传入的数据统一格式为`current`当前页，`size`每页大小。而我们在数据库中要将这两个数据变更为从第几行到第几行，所以我们需要简单的适配一下：

```java
@Data
public class PageAdapter{

    private int begin;

    private int end;

    public PageAdapter(Page page) {
        int[] startEnd = PageUtil.transToStartEnd((int) page.getCurrent(), (int) page.getSize());
        this.begin = startEnd[0];
        this.end = startEnd[1];
    }
}
```



## Count

在使用`mybatis plus` 进行分页的时候，该工具会自动为我们编写count的sql，而一对多进行分页时如：

1个订单有5个订单项，在使用`mybatis plus` 生成的`count sql` 会认为每行都是一条数据，导致最后认为会有5条订单信息，实际上应该只有1条订单信息。这个时候我们必须自己手写`count sql`，并区分`records sql`。

具体例子可以查看`OrderServiceImpl`

```java
@Override
public IPage<Order> pageOrdersDetialByOrderParam(Page<Order> page, OrderParam orderParam) {
    page.setRecords(orderMapper.listOrdersDetialByOrderParam(new PageAdapter(page), orderParam));
    page.setTotal(orderMapper.countOrderDetial(orderParam));
    return page;
}
```

