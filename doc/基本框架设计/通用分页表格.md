## 通用分页表格实现

前端基于VUE的轻量级表格插件 `avue`
后端分页组件使用Mybatis分页插件 `MybatisPlus`



> 分页实现流程，以【系统管理-管理员列表】为例

后台vue文件位置目录 `\src\views\modules\sys\user.vue` 

1、`avue`组件的几个通用配置

```html
<avue-crud ref="crud"
           :page="page"
           :data="dataList"
           :option="tableOption"
           @search-change="searchChange"
           @selection-change="selectionChange"
           @on-load="getDataList">
</avue-crud>
```

`avue`定义了很多的事件，其中一个为 `@on-load`当该组件加载的时候，将会调用该方法。同时也对很多数据进行了双向绑定如：`:page="page"` 分页参数、`:data="dataList"` 分页的具体列表数据、`:option="tableOption"` 表格显示的列



2、通用的列表、搜索

在`avue`规定，表格的构建，是通过JS对象，进行配置的，而不是通过dom，类似于传统的layui，还有一个主要的原因是这个表格，可以同时生成搜索、分页。

```javascript
import { tableOption } from '@/crud/sys/user'
```



我们查看下该类的代码：

```javascript
export const tableOption = {
  border: true,
  selection: true,
  index: false,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  menuWidth: 350,
  align: 'center',
  refreshBtn: true,
  searchSize: 'mini',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: false,
  props: {
    label: 'label',
    value: 'value'
  },
  column: [{
    label: '用户名',
    prop: 'username',
    search: true
  }, {
    label: '邮箱',
    prop: 'email'
  }, {
    label: '手机号',
    prop: 'mobile'
  }, {
    label: '创建时间',
    prop: 'createTime'
  }, {
    label: '状态',
    prop: 'status',
    type: 'select',
    dicData: [
      {
        label: '禁用',
        value: 0
      }, {
        label: '正常',
        value: 1
      }
    ]

  }]
}
```

这里的 `search: true` 也就是搜索框出现用户名搜索

```javascript
{
    label: '用户名',
    prop: 'username',
    search: true
}
```

具体可以通过[avue官网-crud文档](https://avuejs.com/doc/crud/crud-doc)获取文档进行查询



3、 通用的搜索和加载

```javascript
getDataList (page, params) {
    this.dataListLoading = true
    this.$http({
        url: this.$http.adornUrl('/sys/user/page'),
        method: 'get',
        params: this.$http.adornParams(
            Object.assign(
                {
                    current: page == null ? this.page.currentPage : page.currentPage,
                    size: page == null ? this.page.pageSize : page.pageSize
                },
                params
            )
        )
    }).then(({ data }) => {
        this.dataList = data.records
        this.page.total = data.total
        this.dataListLoading = false
    })
}
```



4、服务端`SysUserController` 

```java
@RestController
@RequestMapping("/sys/user")
public class SysUserController {
	@Autowired
	private SysUserService sysUserService;
	/**
	 * 所有用户列表
	 */
	@GetMapping("/page")
	@PreAuthorize("@pms.hasPermission('sys:user:page')")
	public ServerResponseEntity<IPage<SysUser>> page(String username,PageParam<SysUser> page){
		IPage<SysUser> sysUserPage = sysUserService.page(page, new LambdaQueryWrapper<SysUser>()
				.eq(SysUser::getShopId, SecurityUtils.getSysUser().getShopId())
				.like(StrUtil.isNotBlank(username), SysUser::getUsername, username));

		return ServerResponseEntity.success(sysUserPage);
	}
}
```
