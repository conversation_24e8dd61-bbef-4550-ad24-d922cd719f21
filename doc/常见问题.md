这里整理了一些经常会被问到的问题：

1. 为什么vue打包之后，或者修改url之后，无法登录？
答：你用chrome按f12看看console提示的信息如：`Access-Control-Allow-Origin` 那就是跨域了，再看看network的请求方法是不是`options`，但是返回不是200，这也是跨域了。

2. 跨域了怎么办？
跨域产生的原因是因为浏览器的同源策略，也就是说这个是浏览器的问题，你用`postman`去请求，都是没有问题，返回200的，浏览器才会出现这种奇怪的问题。要解决这个问题，就要清楚同源策略是啥，也就是浏览器认为：域名、协议、端口相同才是相同的源，也就是要想办法让前端的域名、协议、端口和接口的相同。而实际上前端和服务器怎么可以在一个端口呢？那就需要一些转发的工具，将同一个端口，不同路径的请求，转发到不同的端口，具体操作可以看 【生产环境nginx安装与跨域配置】

3. 上传图片后图片不显示
检查前端代码中的VUE_APP_RESOURCES_URL配置是否为上传图片的地址

4. 前端登录显示无权限
检查前端代码中配置的VUE_APP_BASE_API是否正确，h5页面配置的api服务的端口号，默认为8086
后台vue页面配置的是admin服务的端口号，默认为8085
