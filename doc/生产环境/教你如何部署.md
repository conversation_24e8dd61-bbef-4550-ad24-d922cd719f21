## 安装jdk

安装JDK,如果没有java-17-openjdk-devel就没有javac命令

```bash
yum  install  java-17-openjdk   java-17-openjdk-devel
```





## 编译打包项目

项目最终需要进行编译打包上传到服务器，生产环境上的配置与测试环境不同，需要独立配置一些东西，满足自己的需要



### 1. mall4j

1. 修改`yami-shop-admin\src\main\resources\application-prod.yml` 更改为生产环境的数据库账号密码，端口号等

2. 修改`yami-shop-admin\src\main\resources\logback\logback-prod.xml` 修改里面的`PROJECT_PATH` 将`/opt/projects/yami-shops` 改为自己生产环境的项目路径

3. 修改`yami-shop-api\src\main\resources\application-prod.yml` 更改为生产环境的数据库账号密码，端口号等

4. 修改`yami-shop-api\src\main\resources\logback\logback-prod.xml` 修改里面的`PROJECT_PATH` 将`/opt/projects/yami-shops` 改为自己生产环境的项目路径

以上 1、2 工程目录为 `yami-shop-admin` 而 3、4 工程目录为 `yami-shop-api` 请注意区分

6. 修改完毕后打包，使用`mvn clean package -DskipTests`  命令进行打包，最终会生成很多的jar，我们需要其中两个。

- 商城后台接口 `yami-shop-admin\target\yami-shop-admin-0.0.1-SNAPSHOT.jar`
- 商城前端接口`yami-shop-api\target\yami-shop-api-0.0.1-SNAPSHOT.jar`

7. 将两个jar上传到centos环境中

8. 在生产环境中运行时候，需要使用`-Dspring.profiles.active=prod` ，在使用admin这个工程项目于生产环境的时候要添加定时任务的配置如`-Dspring.profiles.active=prod`，运行：

```bash
nohup java -jar -Dspring.profiles.active=prod "${jarPath}/${jarName}" > "${jarPath}/log/${moduleName}-console.log" &

nohup java -jar -Dspring.profiles.active=prod "${jarPath}/${jarName}" > "${jarPath}/log/${moduleName}-console.log" &
```

- 替换`${jarPath}` 为`jar` 所在路径
- 替换`${jarName}` 为`jar` 所在路径
- 替换`${moduleName}` 为`admin`或`api`

9. 查看控制台日志输出

```bash
# 后台日志
tail -f ${PROJECT_PATH}/log/admin.log
# 前端接口日志
tail -f ${PROJECT_PATH}/log/api.log
```

- 替换`${PROJECT_PATH}` 为`logback-prod.xml` 里面修改的`PROJECT_PATH` 路径 

10. 使用nginx将请求指向特定的端口。

11. 关于定时任务，请使用xxl-job，看`XxlJobConfig`的配置说明

### 2.vue

> mall4v：v代表vue项目，是后台管理员界面使用的前端项目，因为前后端分离的
>
> mall4uni：用户前端h5项目
>
> mall4m：小程序项目

下面以mall4v为主进行讲解

##### 1. 安装nodejs + 淘宝npm镜像

如果不了解怎么安装nodejs的，可以参考   [菜鸟教程的nodejs相关](https://www.runoob.com/nodejs/nodejs-install-setup.html)


将npm的镜像源更改为淘宝的镜像源，回车（千万不要用cnpm，否则会出现不可预知的后果）：

```bash
npm config set registry https://registry.npmmirror.com
```



##### 2. 安装依赖启动项目

项目要求使用 [pnpm](https://www.pnpm.cn/)  包管理工具

使用编辑器打开项目，在根目录执行以下命令安装依赖

```bash
pnpm install
```

如果不想使用 pnpm，请删除 `package.json` 文件中 `pnpm` 相关内容后再进行安装

```json
{
    "scripts" : {
        "preinstall": "npx only-allow pnpm"  // 删除此行
    },
    "engines": {
        "pnpm": ">=7"  // 删除此行
     },
    "pnpm": { // 删除此项
        ...
    }
}
```

##### 3. 修改配置文件，连接后台

修改 `.env.production`  连接后台，

- `VUE_APP_BASE_API` ： `mall4v` 这个项目连接的是`admin.jar`提供的接口

- `VUE_APP_RESOURCES_URL` : 静态资源文件对应的url，比如七牛云之类的

```javascript
# just a flag
ENV = 'production'

// api接口请求地址
VUE_APP_BASE_API = 'https://mini-admin.mall4j.com/apis'

// 静态资源文件url
VUE_APP_RESOURCES_URL = 'https://img.mall4j.com/'
```



如果你仔细查看我们默认的`.env.production`的设置的话，会看到我们的url后面加了`/apis`，实际上这是我们为了少创建几个子域名做的操作，如果你看到`《nginx安装与跨域配置》` 就能看出这里其实做了转发。

其实如果创建的子域名足够多，也就不需要nginx进行转发了，此时直接填域名即可，无需再加`/apis` 两个后缀了。

如下所示：

```nginx
    location /apis {
		rewrite  ^/apis/(.*)$ /$1 break;
		proxy_pass   http://127.0.0.1:8111;
    }
```



##### 打包，上传到服务器

1. 使用 `npm run build` 命令对项目进行打包
2. 将步骤1中生成的`dist` 文件夹中的文件，压缩，上传到服务器nginx指定好的目录(`/usr/share/nginx/admin` )，解压



`mall4j-admin.conf`

```nginx
server {
    listen       80;
	server_name  mall4j-admin.gz-yami.com;
    root         /usr/share/nginx/admin;

    # Load configuration files for the default server block.
    include /etc/nginx/default.d/*.conf;

    location / {
    }
        
	# 跨域配置
	location /apis {
		rewrite  ^/apis/(.*)$ /$1 break;
		proxy_pass   http://127.0.0.1:8111;
    }
        
}
```

用户端api接口的nginx配置文件参考《nginx安装与跨域配置》这篇文章，修改的配置文件路径`/mall4uni/utils/config.js`，同样打包上传到服务器，配置nginx即可



##### 4. 初始账号密码
后台管理端账号：admin 密码：123456
