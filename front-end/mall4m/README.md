一个基于vue、element ui 的轻量级、前后端分离、拥有完整sku和下单流程的完全开源商城 小程序端



## 前言

`mall4j商城`项目致力于为中小企业打造一个完整、易于维护的开源的电商系统，采用现阶段流行技术实现。后台管理系统包含商品管理、订单管理、运费模板、规格管理、会员管理、运营管理、内容管理、统计报表、权限管理、设置等模块。


## 授权

Mall4j官网 https://www.mall4j.com

Mall4j 使用 AGPLv3 开源，请遵守 AGPLv3 的相关条款，或者联系作者获取商业授权(https://www.mall4j.com)


## 项目链接

java后台：https://gitee.com/gz-yami/mall4j

vue中后台：https://gitee.com/gz-yami/mall4v

小程序：https://gitee.com/gz-yami/mall4m



## 演示地址
商业版小程序演示

![输入图片说明](../screenshot/%E5%AE%87%E5%AE%99%E7%89%88%E5%B0%8F%E7%A8%8B%E5%BA%8F.png)



## 相关截图

![首页](https://gitee.com/gz-yami/mall4m/raw/master/screenshot/index.jpg)

![商品详情](https://gitee.com/gz-yami/mall4m/raw/master/screenshot/prodInfo.jpg)

![购物车](https://gitee.com/gz-yami/mall4m/raw/master/screenshot/shopCart.jpg)

![sku](https://gitee.com/gz-yami/mall4m/raw/master/screenshot/sku.jpg)

![我的](https://gitee.com/gz-yami/mall4m/raw/master/screenshot/my.jpg)







## 提交反馈

提问之前，请先阅读[提问的智慧](https://github.com/ryanhanwu/How-To-Ask-Questions-The-Smart-Way/blob/master/README-zh_CN.md)：

- QQ群：722835385

  ![QQ群](https://gitee.com/gz-yami/mall4j/raw/master/screenshot/qqGroup.png)

- 论坛：<http://bbs.mall4j.com>

- 商务邮箱：<EMAIL>