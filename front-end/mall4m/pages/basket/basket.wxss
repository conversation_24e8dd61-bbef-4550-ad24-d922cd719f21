/* pages/basket/basket.wxss */

page {
  width: 100%;
  background: #f4f4f4;
}
.container{
    padding-bottom:118rpx;
}
container .prod-list {
  width: 100%;
  background: #f8f8f8;
}

.prod-list .prod-block {
  background: #fff;
  margin-top: 15rpx;
}

.prod-list .prod-block .discount-tips {
  padding: 20rpx 0 20rpx 20rpx;
  border-bottom: 2rpx solid #f4f4f4;
  height: 40rpx;
  line-height: 40rpx;
}

.prod-list .prod-block .discount-tips .text-block {
  padding:3rpx 5rpx;
  /* padding: 0 5rpx; */
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #eb2444;
  border: 2rpx solid #eb2444;
}

.prod-list .prod-block .discount-tips .text-list {
  font-size: 24rpx;
  margin-left: 10rpx;
}

.prod-list .item {
  background: #fff;
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.prod-list .item .prodinfo {
  position: relative;
  color: #999;
  width:100%;
}

.prod-list .item .prodinfo::after {
  content: '';
  background-color: #f4f4f4;
  left: 0;
  height: 1px;
  transform-origin: 50% 100% 0;
  bottom: -20rpx;
  position: absolute;
  display: block;
  width: 642rpx;
  padding-left: 20rpx;
}

.prod-list .item:last-child .prodinfo::after {
  height: 0;
}

.prod-list .item .staus {
  text-align: center;
  background: rgb(196, 192, 192);
  font-size: 20rpx;
  width: 50rpx;
  color: #fff;
}

.prodinfo {
  display: flex;
  margin-left: 20rpx;
}

.prod-list .item .opt {
  font-size: 28rpx;
  margin-left: 20rpx;
  width:100%;

}

.prodinfo .opt .prod-name {
  color:#333;
  max-height: 72rpx;
  line-height: 36rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.prodinfo .opt .prod-info-text {
  color: #999;
  display: inline-block;
  -webkit-line-clamp: 1;
  height: 48rpx;
  line-height: 48rpx;
  background: #f9f9f9;
  padding: 0 10rpx 0 10rpx;
  border-radius: 4rpx;
  margin: 10rpx 0 0rpx 0;
  overflow: hidden;
  font-size: 24rpx;
  position: relative;
  font-family: arial;
}
.prodinfo .opt .prod-info-text.empty-n{
  padding:0;
}

/* .prod-info-text:before, .prod-info-text:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  border: 5px solid transparent;
  right: 5px;
  top: 10px;
} */

.prod-info-text:before {
  border-top: 5px solid #aaa;
}

.prod-info-text:after {
  border-top: 5px solid #f9f9f9;
  top: 9px;
}

.prod-list .item .prodinfo .pic {
  text-align: center;
  width: 180rpx;
  height: 180rpx;
  line-height: 180rpx;
  font-size: 0;
}

.prod-list .item .pic image {
  max-width: 100%;
  max-height: 100%;
  /* border-radius: 8rpx; */
  vertical-align: middle;
}

.prodinfo .opt .price-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.prodinfo .opt .price-count .price {
  color: #eb2444;
}

.lose-efficacy .prodinfo .opt .price-count .price {
  color: #999;
}

/* 加减框 */

.m-numSelector .minus, .m-numSelector input, .m-numSelector .plus {
  float: left;
  box-sizing: border-box;
  height: 56rpx;
  border: 2rpx solid #d9d9d9;
}

.m-numSelector {
  float: right;
}

.m-numSelector .minus, .m-numSelector .plus {
  position: relative;
  width: 56rpx;
}

.m-numSelector .minus {
  border-right: 0;
  border-top-left-radius: 4rpx;
  border-bottom-left-radius: 4rpx;
}

.m-numSelector input {
  width: 56rpx;
  text-align: center;
  color:#333;
}

.m-numSelector .plus {
  border-left: 0;
  border-top-right-radius: 4rpx;
  border-bottom-right-radius: 4rpx;
}

.m-numSelector .minus::before, .m-numSelector .plus::before,
.m-numSelector .plus::after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  content: ' ';
  width: 22rpx;
  height: 3rpx;
  background-color: #7f7f7f;
}

.m-numSelector .plus::after {
  transform: rotate(90deg);
}

.m-numSelector:not(.disabled) .minus:not(.disabled):active,
.m-numSelector:not(.disabled) .plus:not(.disabled):active {
  background-color: #f4f4f4;
}

/*checkbox 选项框大小  */

checkbox .wx-checkbox-input {
  border-radius: 50%;
  width: 35rpx;
  height: 35rpx;
}

/*checkbox选中后样式  */

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background: #eb2444;
  border-color: #eb2444;
}

/*checkbox选中后图标样式  */

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  text-align: center;
  font-size: 22rpx;
  color: #fff;
  background: transparent;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}

/* 购物车为空 */

.empty {
  font-size: 28rpx;
  color: #666;
}

.empty .txt {
  text-align: center;
  margin-top: 30rpx;
}

.empty .img {
  margin-top: 80rpx;
  text-align: center;
}

.empty .img image {
  width: 80rpx;
  height: 80rpx;
}

/* 失效商品 */

.lose-efficacy {
  margin-top: 20rpx;
  background: #fff;
}

.lose-efficacy .item {
  background: #f8f8f9;
}

.prod-list .lose-efficacy .discount-tips {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #ddd;
  height: 50rpx;
  line-height: 50rpx;
  margin-left: 20rpx;
}

.lose-efficacy .discount-tips .empty-prod {
  color: #777;
  font-size: 26rpx;
  border: 2rpx solid #999;
  padding: 0 10rpx;
  border-radius: 8rpx;
  float: right;
  margin-right: 20rpx;
}

.price-count .disable-price {
  color: #999;
}

.prod-list .lose-efficacy .discount-tips .text-list {
  font-size: 30rpx;
  margin-left: 10rpx;
}

/** 底部按钮 */

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  height: 98rpx;
  border-top: 2rpx solid #f4f4f4;
  z-index:999;
}

.cart-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fafafa;
  background:rgba(255,255,255,0.95);
  font-size: 28rpx;
}
.cart-footer .btn .total-msg{
  font-size: 20rpx;
}
.cart-footer .btn.total{
  display:flex;
flex-flow:column;
align-items:flex-start;

}
.cart-footer .btn.del {
  color:#eb2444;
width:70rpx;
font-size:22rpx;
text-align:left;
display:block;
line-height:102rpx;

}

.cart-footer .btn.all {
  width: 150rpx;
  font-size: 26rpx;
}

.cart-footer .btn.all label {
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
}

.cart-footer .btn.total {
  width: 300rpx;
}

.cart-footer .btn.total .price {
  color: #eb2444;
  font-size: 30rpx;
}

.cart-footer .btn.settle {
  width: 200rpx;
  background: #eb2444;
  color: #fff;
}

/** end 底部按钮 */
