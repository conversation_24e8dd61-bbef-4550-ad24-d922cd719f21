<!--pages/editAddress/editAddress.wxml-->

<view class='container'>
  <!--input列表 -->
  <view class='input-box'>
    <view class="section">
      <text>收 货 人</text>
      <input placeholder='姓名' type='text' maxlength='15' value="{{receiver}}" bindinput="onReceiverInput" ></input>
    </view>
    <view class="section">
      <text>手机号码</text>
      <input placeholder='11位手机号码' type='number' maxlength='11' value="{{mobile}}" bindinput="onMobileInput"></input>
    </view>
    <view class="section" bindtap="translate">
      <text>所在地区</text>
      <view class="pca">{{province}} {{city}} {{area}}</view>
      <view class="animation-element-wrapper" animation="{{animation}}" style="visibility:{{show ? 'visible':'hidden'}}" catchtap="hiddenFloatView">
        <view class="animation-element" catchtap="nono">
          <text class="right-bt" catchtap="hiddenFloatView">确定</text>
          <view class="line"></view>
          <picker-view indicator-style="height: 50rpx;" value="{{value}}" bindchange="bindChange" catchtap="nono">
            <!--省-->
            <picker-view-column>
              <view wx:for="{{provArray}}" wx:key="">
                {{item.areaName}}
              </view>
            </picker-view-column>
            <!--地级市-->
            <picker-view-column>
              <view wx:for="{{cityArray}}" wx:key="">
                {{item.areaName}}
              </view>
            </picker-view-column>
            <!--区县-->
            <picker-view-column>
              <view wx:for="{{areaArray}}" wx:key="">
                {{item.areaName}}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>

      <view class='arrow'>
        <image src='../../images/icon/more.png'></image>
      </view>
    </view>
    <view class="section">
      <text>详细地址</text>
      <input placeholder='如楼号/单元/门牌号' type='text' value="{{addr}}" bindinput="onAddrInput"></input>
    </view>
  </view>
  <!-- end input列表 -->
  <!-- 功能按钮 -->
  <view class='btn-box'>
    <view class='keep btn' bindtap='onSaveAddr'>
      <text>保存收货地址</text>
    </view>

    <view class='clear btn' bindtap="onDeleteAddr"  wx:if="{{addrId!=0}}">
      <text>删除收货地址</text>
    </view>
  </view>
  <!-- end 功能按钮 -->
</view>

