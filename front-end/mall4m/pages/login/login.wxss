/* pages/login/login.wxss */


image {
  display: block;
  width: 150rpx;
  height: 150rpx;
  margin: auto;
  margin-top: 100rpx;
  border-radius: 50%;
}

view.msg {
  font-size: 24rpx;
  color: #666;
  width: 100%;
  text-align: center;
  margin-top: 10rpx;
}

view.title {
  font-size: 28rpx;
  color: #333;
  width: 100%;
  text-align: center;
  margin: 20rpx 0;
}

button {
  margin-top: 30rpx;
  width: 450rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  color: #fff;
  background: #eb2444;
  border: 2rpx solid #eb2444;
}


.button-hover{
  background-color: #fff;
  color: #eb2444;
}

page{
  background: #fff;
  height: 100%;
}
.con{
  padding-top: 100rpx;
}

.logo {
  display: flex;
  justify-content: center;
  height: 150rpx;
  margin-bottom: 8%;
}
.logo image {
  display: block;
  width: 100%;
  height: 100%;
}

.login-form{
  width: 90%;
  margin: 0 auto;
  margin-bottom: 10%;
}
.authorized-btn {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  background-color: #0ab906;
  border: 1rpx solid #0ab906;
  color: #fff;
  border-radius: 6rpx;
  font-size: 26rpx;
  margin-top: 60rpx;
}
.to-idx-btn{
	width: 100%;
	margin: 0 auto;
	text-align: center;
  background-color: #eeeeee;
  border: 1rpx solid #eeeeee;
	color: #333;
	border-radius: 6rpx;
	font-size: 26rpx;
	margin-top: 30rpx;
}
.form-title {
  width: 100%;
  margin-bottom: 50rpx;
  font-size: 32rpx;
  text-align: center;
  color: #00a0e9;
}
.item {
	display: block;
	margin-bottom: 30rpx;
}
.account{
  display: flex;
  background: #f8f8f8;
  padding: 15rpx;
  box-sizing: border-box;
  font-size: 26rpx;
  align-items: center;
}
.account input{
  padding-left: 20rpx;
  width:75%;
}
.inp-palcehoder{
  font-size: 26rpx;
}
.account input.int-yzm {
  width: 410rpx;
  padding-right: 10rpx;
  box-sizing: border-box;
}
.input-btn {
  width: 152rpx;
  font-size: 26rpx;
  color: #00a0ea;
  text-align: center;
}

button::after{
  border: 0 !important;
}

/* 去注册 */
.operate {
	display: flex;
	justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.forgot-password,
.to-register {
	font-size: 28rpx;
	color: #00AAFF;
}
