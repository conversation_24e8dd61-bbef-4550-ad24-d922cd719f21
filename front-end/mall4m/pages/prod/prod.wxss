page {
  background: #f4f4f4;
  height: 100%;
}

.container {
  height: auto;
  padding-bottom: 150rpx;
}

swiper {
  height: 750rpx;
  width: 100%;
  border-bottom: 2rpx solid #f8f8f8;
}

swiper image {
  height: 750rpx;
  width: 100%;
}

/** 商品信息 */

.prod-info {
  padding: 30rpx 30rpx 0 30rpx;
  position: relative;
  background: #fff;
}

.tit-wrap {
  position: relative;
  line-height: 40rpx;
  padding-right: 104rpx;
}

.prod-tit {
  font-size: 32rpx;
  color: #333;
  padding-right: 20rpx;
}

.tit-wrap .col {
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  color: #666;
  font-size: 20rpx;
  padding-left: 20rpx;
  text-align: center;
}

.tit-wrap .col image {
  display: block;
  margin: auto;
  width: 40rpx;
  height: 40rpx;
}

.tit-wrap .col::after {
  content: "";
  display: block;
  width: 1px;
  height: auto;
  background: #f1f1f1;
  position: absolute;
  top: 0;
  bottom: 5px;
  left: 0;
}

.sales-p {
  background: #fff;
  line-height: 40rpx;
  color: #999;
  font-size: 24rpx;
  margin-top: 6rpx;
  margin-right: 104rpx;
}

.prod-price {
  font-size: 30rpx;
  height: 100rpx;
  line-height: 100rpx;
}

.price {
  color: #eb2444;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 50rpx;
}

.price-num {
  font-size: 46rpx;
  font-weight: 400;
}

.sales {
  color: #999;
}

.share-icon {
  position: absolute;
  right: 50rpx;
  top: 50rpx;
  background: none;
  line-height: 40rpx;
  border: none;
  outline: none;
  box-shadow: 0;
  padding: 0;
}

.share-icon::after {
  border: none;
}

.share-icon image {
  width: 60rpx;
  height: 60rpx;
}

.share-text {
  font-size: 26rpx;
  color: #999;
  line-height: 30rpx;
}

/** end 商品信息 */

/**优惠券*/

.coupon {
  padding: 28rpx 100rpx 14rpx 100rpx;
  background: #fff;
  position: relative;
  margin-top: 20rpx;
}

.coupon .coupon-tit {
  position: absolute;
  display: inline-block;
  width: 60rpx;
  left: 20rpx;
  font-size: 22rpx;
  top: 28rpx;
  line-height: 36rpx;
  color: #999;
}

.coupon-con .item {
  position: relative;
  display: inline-block;
  vertical-align: top;
  padding: 0 18rpx;
  background: #eb2444;
  height: 36rpx;
  line-height: 36rpx;
  color: #fff;
  font-size: 22rpx;
  margin: 0 16rpx 16rpx 0;
  font-family: arial;
}

.coupon-con .item:before, .coupon-con .item:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  border: 18rpx solid transparent;
}

.coupon-con .item:before {
  left: 0;
  border-left: 4rpx solid #fff;
}

.coupon-con .item:after {
  right: 0;
  border-right: 4rpx solid #fff;
}

.coupon .num {
  position: absolute;
  right: 80rpx;
  width: 80rpx;
  top: 28rpx;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  line-height: 36rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: arial;
}

.more {
  position: absolute;
  right: 20rpx;
  width: 60rpx;
  top: 10rpx;
  text-align: right;
  font-size: 40rpx;
  color: #999;
  letter-spacing: 1px;
}

/* 已选 */

.sku {
  padding: 20rpx;
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
}

.sku-tit {
  position: absolute;
  display: inline-block;
  width: 60rpx;
  left: 20rpx;
  font-size: 22rpx;
  top: 20rpx;
  color: #999;
}

.sku-con {
  margin: 0 80rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 28rpx;
  font-weight: bold;
}

/** 评价*/

.cmt-wrap {
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
}

.cmt-tit {
  font-size: 32rpx;
  position: relative;
  border-bottom: 1px solid #ddd;
  padding: 20rpx;
}

.cmt-t {
  width: 300rpx;
}

.cmt-good {
  color: #eb2444;
  font-size: 24rpx;
}

.cmt-count {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.cmt-more {
  width: 20rpx;
  height: 20rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
  display: inline-block;
}

.cmt-cont {
  padding: 0 20rpx;
}

.cmt-tag {
  position: relative;
  padding: 14px 3px 0 0;
  margin: 0;
}

.cmt-tag text {
  margin: 0 10px 10px 0;
  background: #fdf0f0;
  display: inline-block;
  padding: 0 10px;
  height: 25px;
  border-radius: 3px;
  line-height: 25px;
  font-size: 12px;
  font-family: -apple-system, Helvetica, sans-serif;
  color: #666;
}

.cmt-tag text.selected {
  color: #fff;
  background: #e93b3d;
}

.cmt-item {
  position: relative;
  padding: 10px 0;
}

.cmt-item::after {
  content: "";
  height: 0;
  display: block;
  border-bottom: 1px solid #ddd;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  right: -10px;
  border-bottom-color: #e5e5e5;
}

.cmt-user {
  line-height: 25px;
  margin-bottom: 8px;
  font-size: 12px;
}

.cmt-user-info {
  display: flex;
  align-items: center;
  width: 400rpx;
}

.cmt-user .user-img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  vertical-align: middle;
}

.cmt-user .nickname {
  margin-left: 10px;
  display: inline-block;
  color: #333;
  max-width: 8.2em;
  height: 25px;
  line-height: 27px;
}

.cmt-user .stars {
  display: flex;
  margin-left: 3px;
}

.cmt-user .stars image {
  width: 35rpx;
  height: 35rpx;
}

.cmt-user .date {
  float: right;
  color: #999;
  margin-left: -60px;
}

.cmt-cnt {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
  line-height: 1.5;
  font-size: 14px;
  margin: 5px 0;
  word-break: break-all;
  max-height: 126px;
}

.cmt-attr {
  height: 85px;
  width: 100%;
  white-space: nowrap;
}

.cmt-attr .img-wrap {
  width: 85px;
  height: 85px;
  display: inline-block;
}

.cmt-attr image {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
  border-radius: 2px;
  background: #f3f3f3;
}

.cmt-more-v {
  text-align: center;
  background-color: #fff;
  font-size: 12px;
}

.cmt-more-v text {
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  text-align: center;
  color: #333;
  padding: 0px 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 40px;
  display: inline-block;
}

/** 评价弹窗 */

.cmt-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 998;
  background-color: #fff;
  padding-bottom: 98rpx;
}

.cmt-popup .cmt-cont {
  height: calc(100% - 80rpx);
  overflow: auto;
}

.cmt-popup .cmt-cnt {
  -webkit-line-clamp: 20;
  max-height: 500px;
}

.cmt-reply {
  font-size: 14px;
  border-top: 1px dashed #ddd;
  padding: 5px 0;
}

.cmt-reply .reply-tit {
  color: #eb2444;
}

.cmt-popup .load-more {
  font-size: 14px;
  padding: 20px;
  text-align: center;
  margin-bottom: 10px;
}

.cmt-popup .load-more text {
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 10px;
  color: #666;
}

/** 商品详情 */

.prod-detail {
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
}

.det-tit {
  width: 300rpx;
}

.detail-tit {
  font-size: 32rpx;
  position: relative;
  border-bottom: 1px solid #ddd;
  padding: 20rpx;
}

.prod-detail image {
  width: 750rpx !important;
  display: block;
}

rich-text image {
  width: 100% !important;
}

img {
  width: 100% !important;
  display: block;
}

/** end 商品详情 */

/** 底部按钮 */

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  height: 98rpx;
  z-index: 999;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.cart-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fff;
  font-size: 28rpx;
  flex-flow: column;
}

.cart-footer .btn.icon {
  flex-grow: 0;
  flex-shrink: 0;
  width: 125rpx;
  font-size: 20rpx;
  color: #666;
}

.cart-footer .btn.icon image {
  width: 50rpx;
  height: 50rpx;
}

.cart-footer .btn.cart {
  background: #584e61;
  color: #fff;
}

.cart-footer .btn.buy {
  background: #eb2444;
  color: #fff;
}

.cart-footer.gray .btn.cart, .cart-footer.gray .btn.buy{
  background: #ddd;
}

.cart-footer .btn .badge {
  position: absolute;
  top: 20rpx;
  left: 62rpx;
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  border-radius: 14rpx;
  background-color: #eb2444;
  text-align: center;
  line-height: 28rpx;
  font-size: 18rpx;
  color: #fff;
}

.cart-footer .btn .badge-1 {
  width: 36rpx;
}

.cart-footer .btn .badge-2 {
  width: 48rpx;
  left: 52rpx;
}

/** end  底部按钮 */

/** 优惠券弹窗 **/

.popup-hide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.popup-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 475px;
  overflow: hidden;
  background-color: #fff;
}

.popup-tit {
  position: relative;
  height: 46px;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  background-color: #f7f7f7;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: "\2716";
}

.popup-cnt {
  max-height: 429px;
  overflow: auto;
  padding: 0 10px;
}

/** 规格弹窗**/

.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 475px;
  background-color: #fff;
}

.pup-sku-header {
  position: relative;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  height: 70px;
  padding: 0 0 10px 110px;
  background-color: #fff;
}

.pup-sku-img {
  position: absolute;
  left: 10px;
  top: -20px;
  border-radius: 2px;
  width: 90px;
  height: 90px;
  border: 0 none;
  vertical-align: top;
}

.pup-sku-price {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  color: #e4393c;
  font-size: 10px;
}

.pup-sku-price-int {
  font-size: 16px;
}

.pup-sku-prop {
  word-break: break-all;
  font-size: 12px;
  color: #333;
  line-height: 1.4em;
  padding-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.pup-sku-prop text {
  color: #999;
  margin-right: 5px;
}

.pup-sku-body {
  box-sizing: border-box;
  max-height: 379px;
  padding-bottom: 100px;
  overflow: auto;
}

.pup-sku-area .sku-kind {
  font-size: 12px;
  color: #999;
  margin: 0 10px;
  height: 40px;
  line-height: 40px;
}

.pup-sku-area .sku-choose {
  overflow: hidden;
  margin-bottom: 3px;
}

.sku-choose-item {
  display: inline-block;
  padding: 0 10px;
  min-width: 20px;
  max-width: 270px;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-left: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  color: #333;
  background-color: #f7f7f7;
  font-size: 14px;
  border:1px solid #aaa;
}

.sku-choose-item.active {
  background-color: #eb2444;
  border:1px solid #eb2444 !important;
  color: #fff;
}


.sku-choose-item.dashed {
  border:1px dashed #aaa;
}

.pup-sku-count {
  padding: 0 10px 13px;
  font-size: 12px;
}

.pup-sku-count .count-name {
  color: #999;
  height: 31px;
  line-height: 31px;
  width: 100rpx;
}

.pup-sku-count .num-wrap {
  position: relative;
  z-index: 0;
  width: 110px;
  float: right;
  vertical-align: middle;
  display: flex;
}

.num-wrap .minus, .num-wrap .plus {
  position: relative;
  max-width: 30px;
  min-width: 30px;
  height: 30px;
  line-height: 30px;
  background: #f7f7f7;
  text-align: center;
}

.num-wrap .minus {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.num-wrap .plus {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.num-wrap .row {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -1px;
  width: 14px;
  height: 2px;
  background-color: #ccc;
}

.num-wrap .col {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -7px;
  width: 2px;
  height: 14px;
  background-color: #999;
}

.pup-sku-count .text-wrap {
  position: relative;
  width: 45px;
  z-index: 0;
  margin: 0 1px;
}

.pup-sku-count .text-wrap input {
  height: 30px;
  width: 100%;
  color: #333;
  background: #fff;
  font-size: 12px;
  text-align: center;
  border: none;
  background: #f7f7f7;
}

.pup-sku-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  height: 98rpx;
  z-index: 999;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fff;
  font-size: 28rpx;
  flex-flow: column;
}

.pup-sku-footer .btn.cart {
  background: #584e61;
  color: #fff;
}

.pup-sku-footer .btn.buy {
  background: #eb2444;
  color: #fff;
}

.pup-sku-footer.gray .btn.cart, .pup-sku-footer.gray .btn.buy{
  background: #ddd;
}

