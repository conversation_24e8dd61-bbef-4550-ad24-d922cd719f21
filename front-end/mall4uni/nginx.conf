server {
    listen 80;
    server_name mini-h5.mall4j.com;

    gzip on;
    gzip_static on;

    location / {
        try_files $uri $uri/ /;
        root /usr/share/nginx/html/h5;
        index index.html;

        #### kill cache
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        if_modified_since off;
        expires off;
        etag off;
    }

    error_page 404 /404.html;
        location = /404-light.html {
    }

    error_page 500 502 503 504 /50x.html;
        location = /50x.html {
    }
}

