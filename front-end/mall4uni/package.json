{"name": "mall4uni-pro", "private": true, "version": "0.0.0", "scripts": {"preinstall": "npx only-allow pnpm", "dev:h5": "uni", "dev:h5-test": "uni --mode testing", "dev:mp-weixin": "uni -p mp-weixin", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:h5": "uni build", "build:h5-test": "uni build --outDir=./dist/test/h5 --mode testing", "build:mp-weixin": "uni build -p mp-weixin", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --fix --ext .js --ext .jsx --ext .vue src", "lint:staged": "lint-staged"}, "engines": {"node": ">=16", "pnpm": ">=6"}, "dependencies": {"@babel/eslint-parser": "7.21.3", "@dcloudio/uni-app": "3.0.0-3080720230703001", "@dcloudio/uni-app-plus": "3.0.0-3080720230703001", "@dcloudio/uni-components": "3.0.0-3080720230703001", "@dcloudio/uni-h5": "3.0.0-3080720230703001", "@dcloudio/uni-mp-alipay": "3.0.0-3080720230703001", "@dcloudio/uni-mp-baidu": "3.0.0-3080720230703001", "@dcloudio/uni-mp-jd": "3.0.0-3080720230703001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3080720230703001", "@dcloudio/uni-mp-lark": "3.0.0-3080720230703001", "@dcloudio/uni-mp-qq": "3.0.0-3080720230703001", "@dcloudio/uni-mp-toutiao": "3.0.0-3080720230703001", "@dcloudio/uni-mp-weixin": "3.0.0-3080720230703001", "@dcloudio/uni-quickapp-webview": "3.0.0-3080720230703001", "@uni-ui/code-ui": "1.5.3", "big.js": "6.2.1", "crypto-js": "4.1.1", "js-base64": "3.7.7", "uni-crazy-router": "1.1.3", "uni-vite-plugin-h5-prod-effect": "1.0.1", "vue": "3.2.47"}, "devDependencies": {"@dcloudio/types": "3.3.3", "@dcloudio/uni-automator": "3.0.0-3080720230703001", "@dcloudio/uni-cli-shared": "3.0.0-3080720230703001", "@dcloudio/uni-stacktracey": "3.0.0-3080720230703001", "@dcloudio/vite-plugin-uni": "3.0.0-3080720230703001", "eslint": "8.38.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-n": "15.7.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "9.10.0", "eslint-plugin-vue-scoped-css": "2.4.0", "lint-staged": "13.2.2", "sass": "1.61.0", "unplugin-auto-import": "0.15.2", "unplugin-vue-components": "0.24.1", "vite": "4.1.5", "vue-eslint-parser": "9.1.1"}, "lint-staged": {"*.{js,vue}": ["eslint --fix"]}}