/**app.wxss**/
.container {
  height: 100%;
  box-sizing: border-box;
  color: #333;
  font-family: helvetica,'Heiti SC',PingFangSC-Light;
}
.price{
  font-family: Arial;
  display: inline-block;
  color: #eb2444;
  padding-bottom:10rpx;
  padding-left:10rpx;
}

/* 价格数字显示不同大小 */
.symbol {
  font-size: 24rpx;
}

.big-num {
  font-size: 32rpx;
}

.small-num {
  font-size: 24rpx;
}

/*
*改变checkbox样式
*自定义样式
*/
/* reg */
uni-checkbox-group {
  width: 100% !important;
}
uni-checkbox-group uni-label{
  width: 33% !important;
  display: inline-flex;
  margin-bottom: 20rpx;
}
/*checkbox 选项框大小  */
uni-checkbox .uni-checkbox-input{
  width: 38rpx !important;
  height: 38rpx !important;
  border-radius: 50%!important;
}
/*checkbox选中后样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{
  background: #e43130;
  border: 1px solid transparent !important;
}
/*checkbox选中后图标样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before{
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
  line-height: 20rpx;
  text-align: center;
  font-size: 18rpx;
  color: #fff;
  background: transparent;
  transform: translate(-60%, -50%) scale(1);
  -webkit-transform: translate(-60%, -50%) scale(1);
}

/*
*改变radio样式
*自定义样式
*/
/* 未选中的 背景样式 */
uni-radio .uni-radio-input{
  height: 36rpx;
  width: 36rpx;
  border-radius: 50%;
  background: transparent;
  box-sizing: border-box;
}
/* 选中后的 背景样式 */
uni-radio .uni-radio-input.uni-radio-input-checked{
  border: none !important;
  background: #e43130 !important;
}
/* 选中后的 对勾样式 */
uni-radio .uni-radio-input.uni-radio-input-checked::before{
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  font-size: 20rpx;
  color:#fff;
  background: #e43130;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}

/* 底部按钮兼容 iPhone X以上 */
@media screen and (width: 375px) and (height: 812px){
  .container {
    padding-bottom: 70px;
  }
}
@media screen and (width: 414px) and (height: 736px){
  .container {
    padding-bottom: 70px;
  }
}
