.prod-items {
  width: 43%;
  background: #fff;
  margin-bottom: 40rpx;
  box-sizing: border-box;
  .hot-imagecont {
    border-radius: 8rpx;
    text-align: center;
    font-size: 0;
  }
  .hot-text {
    margin-top: 20rpx;
    .prod-info {
      font-size: 20rpx;
      color: #777;
      padding: 0 20rpx;
      margin-top: 8rpx;
    }
    .prod-text-info {
      position: relative;
      height: 50rpx;
      line-height: 70rpx;
      font-family: Arial;
      .price {
        color: #eb2444;
        padding-left: 20rpx;
      }
    }
  }
}
prod {
  &:nth-child(2n-1) {
    .prod-items {
      padding: 20rpx 10rpx 10rpx 20rpx;
    }
  }
  &:nth-child(2n) {
    .prod-items {
      padding: 20rpx 20rpx 10rpx 10rpx;
    }
  }
}
.hot-imagecont {
  .hotsaleimg {
    width: 100%;
    height: 345rpx;
  }
}
.hot-text {
  .hotprod-text {
    height: 76rpx;
    font-size: 28rpx;
    display: -webkit-box;
    word-break: break-all;
    padding: 0 20rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #000;
  }
}
.deadline-price {
  font-size: 22rpx;
  margin-right: 5rpx;
}
