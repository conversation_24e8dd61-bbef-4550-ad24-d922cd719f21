{
    "name" : "mall4j",
    "appid" : "__UNI__B751008",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "LivePusher" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            }
        }
    },
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx6fa71e69231a4fa4",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于地址信息新增、修改以及获取附近门店"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation" ]
    },
    "h5" : {
        "title" : "",
        "domain" : "https://mini-h5.mall4j.com",
        "router" : {
            "mode" : "history"
        },
        "uniStatistics" : {
            "enable" : false
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "template" : "index.html",
        "devServer" : {
            "disableHostCheck" : true,
            "port" : 80
        }
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3"
}
