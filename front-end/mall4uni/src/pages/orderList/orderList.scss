.container {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #f4f4f4;
  color: #333;
}
.order-tit {
  position: fixed;
  top: 0;
  display: flex;
  justify-content: space-around;
  z-index: 999;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #f4f4f4;
  text {
    display: block;
    font-size: 28rpx;
    color: 999;
    width: 100rpx;
    text-align: center;
  }
  text.on {
    border-bottom: 4rpx solid #eb2444;
    color: #eb2444;
  }
}
.main {
  padding-top: 15rpx;
}
.prod-item {
  background-color: #fff;
  margin-top: 15rpx;
  font-size: 28rpx;
  .item-cont {
    .prod-pic {
      image {
        width: 180rpx;
        height: 180rpx;
        width: 100%;
        height: 100%;
      }
      font-size: 0;
      display: inline-block;
      width: 160rpx;
      height: 160rpx;
      overflow: hidden;
      background: #fff;
      margin-right: 16rpx;
    }
    .categories {
      white-space: nowrap;
    }
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    border-radius: 10rpx;
    display: -webkit-flex;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    background: #fafafa;
    .prod-info {
      margin-left: 10rpx;
      font-size: 28rpx;
      width: 100%;
      position: relative;
      height: 160rpx;
      -webkit-flex: 1;
      -ms-flex: 1;
      -webkit-box-flex: 1;
      -moz-box-flex: 1;
      flex: 1;
      .prodname {
        font-size: 28rpx;
        line-height: 36rpx;
        max-height: 86rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .prod-info-cont {
        color: #999;
        line-height: 40rpx;
        margin-top: 10rpx;
        font-size: 22rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
  }
  .order-num {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
    .clear-btn {
      width: 32rpx;
      height: 32rpx;
      font-size: 0;
      vertical-align: top;
      margin-left: 42rpx;
      position: relative;
      &::after {
        content: " ";
        display: block;
        position: absolute;
        left: -10px;
        top: 0rpx;
        width: 1px;
        height: 32rpx;
        background: #ddd;
      }
      .clear-list-btn {
        width: 100%;
        height: 100%;
        vertical-align: middle;
      }
    }
  }
  .total-num {
    text-align: right;
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    .prodprice {
      display: inline-block;
      color: #333;
    }
    .prodcount {
      margin-right: 20rpx;
    }
  }
  .price-nums {
    .prodprice {
      color: #333;
      position: absolute;
      bottom: 0;
    }
    .prodcount {
      position: absolute;
      bottom: 5rpx;
      right: 0;
      color: #999;
      font-family: verdana;
    }
  }
  .prod-foot {
    border-top: 2rpx solid #e6e6e6;
    .total {
      font-size: 25rpx;
      margin-bottom: 20rpx;
      padding-bottom: 20rpx;
      border-bottom: 2rpx solid #e9eaec;
    }
    .btn {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
.order-state {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  .order-sts.red {
    color: #eb2444;
  }
  .order-sts.gray {
    color: #999;
  }
}
.other-button-hover {
  background-color: blue;
}
.button-hover {
  background-color: red;
  background-color: blue;
}
.button {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  margin-left: 10px;
  font-size: 26rpx;
  background: #fff;
  padding: 10rpx 30rpx;
  border-radius: 80rpx;
  border: 2rpx solid #e1e1e1;
  &:last-child {
    margin-right: 10rpx;
  }
}
.button.warn {
  color: #eb2444;
  border-color: #eb2444;
}
.empty {
  font-size: 24rpx;
  margin-top: 100rpx;
  text-align: center;
  color: #999;
  height: 300rpx;
  line-height: 300rpx;
}
