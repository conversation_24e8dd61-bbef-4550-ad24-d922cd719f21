.pay-sts {
  font-size: 40rpx;
  margin-top: 100rpx;
  padding: 30rpx 0;
  text-align: center;
}
.pay-sts.fail {
  color: #f43530;
}
.pay-sts.succ {
  color: #19be6b;
}
.btns {
  margin-top: 50rpx;
  text-align: center;
  .button {
    border-radius: 10rpx;
    font-size: 28rpx;
    background: #fff;
    color: #333;
    padding: 20rpx 35rpx;
    width: 300rpx;
    margin: 0 20rpx;
    text-align: center;
  }
  .button.checkorder {
    background: #19be6b;
    color: #fff;
    margin-bottom: 20rpx;
    border: 2rpx solid #19be6b;
  }
  .button.payagain {
    background: #fff;
    border: 2rpx solid #f90;
    color: #f90;
  }
  .button.shopcontinue {
    background: #fff;
    border: 2rpx solid #19be6b;
    color: #19be6b;
  }
}
.tips {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  .warn {
    color: #f43530;
  }
}
