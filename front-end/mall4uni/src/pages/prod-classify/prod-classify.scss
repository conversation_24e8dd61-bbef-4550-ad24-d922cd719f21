.container {
  background: #f4f4f4;
  min-height: 100vh;
  padding: 7px;
}
.line-fix {
  width: 100%;
  height: 2rpx;
  background: #e1e1e1;
  position: fixed;
  top: 0;
}
.tit-background {
  width: 100%;
  height: 20rpx;
  background: #f4f4f4;
}
.prod-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
:deep(.prod-items) {
  display: inline-block;
  width: 345rpx !important;
  &:nth-child(2n) {
    margin-left: 30rpx;
  }
}
/* 空 */
.empty {
  display: block;
  width: 100%;
  font-size: 26rpx;
  color: #999;
  margin-top: 20vh;
  text-align: center;
}
