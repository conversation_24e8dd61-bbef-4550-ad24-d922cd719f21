.recent-news {
  background: #fff;
  .news-item {
    padding: 20rpx 20rpx 0 20rpx;
    position: relative;
    &::after {
      content: " ";
      width: 100%;
      height: 2rpx;
      background-color: #e1e1e1;
      left: 20rpx;
      display: block;
      position: absolute;
    }
    .news-item-title {
      font-size: 28rpx;
      text-align: left;
    }
    .news-item-date {
      font-size: 24rpx;
      color: #999;
      text-align: right;
      margin-top: 10rpx;
      margin-bottom: 20rpx;
    }
  }
  .empty {
    display: block;
    padding-top: 200rpx;
    color: #999;
    font-size: 26rpx;
    text-align: center;
  }
}
