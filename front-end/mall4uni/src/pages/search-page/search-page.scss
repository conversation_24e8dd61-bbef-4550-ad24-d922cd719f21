.search-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  color: #777;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.07);
  z-index: 3;
  .search-box {
    position: relative;
    height: 60rpx;
    background: #f7f7f7;
    z-index: 999;
    width: 80%;
    margin-left: 70rpx;
    border-radius: 50rpx;
    margin: 20rpx 0 20rpx 20rpx;
    .search-img {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      left: 20rpx;
      top: 14rpx;
      display: block;
    }
  }
  .search-hint {
    font-size: 28rpx;
    position: absolute;
    right: 30rpx;
    top: 31rpx;
    color: #eb2444;
  }
}
.sear-input {
  height: 60rpx;
  border-radius: 50rpx;
  border: 0;
  margin: 0 30rpx 0 64rpx;
  line-height: 48rpx;
  vertical-align: top;
  background: #f7f7f7;
  font-size: 28rpx;
}
.search-display {
  background: #fff;
  padding: 20rpx;
  margin-top: 100rpx;
  .title-text {
    padding: 30rpx 0;
    font-size: 30rpx;
    color: #666;
  }
}
.hot-search {
  .hot-search-tags {
    overflow: hidden;
    font-size: 26rpx;
    text-align: center;
    padding-bottom: 30rpx;
    .tags {
      display: block;
      max-width: 100%;
      overflow: hidden;
      float: left;
      border-radius: 50rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      background-color: #f2f2f2;
      box-sizing: border-box;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      padding: 10rpx 30rpx;
    }
  }
}
.history-search {
  .title-text.history-line {
    position: relative;
    border-top: 2rpx solid #e1e1e1;
  }
  .his-search-tags {
    overflow: hidden;
    font-size: 26rpx;
    text-align: center;
    display: inline-block;
    .tags {
      max-width: 300rpx;
      overflow: hidden;
      float: left;
      border-radius: 50rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      background-color: #f2f2f2;
      box-sizing: border-box;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      padding: 10rpx 30rpx;
    }
  }
}
.clear-history {
  image {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 10rpx;
    top: 30rpx;
  }
}
.search-tit-empty {
  display: block;
  margin: 0 auto;
  text-align: center;
  width: 100%;
  font-size: 24rpx;
  color: #aaa;
}
