{"globals": {"$t": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "Debounce": true, "EffectScope": true, "InjectionKey": true, "PropType": true, "Ref": true, "VNode": true, "clearLoginInfo": true, "computed": true, "configDefInfo": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "encrypt": true, "flatten": true, "checkFileUrl": true, "formatConfigInfo": true, "getCurrentInstance": true, "getCurrentScope": true, "getLevels": true, "getUUID": true, "h": true, "http": true, "idList": true, "inject": true, "isAuth": true, "isEmail": true, "isHtmlNull": true, "isMobile": true, "isPhone": true, "isProxy": true, "isQq": true, "isReactive": true, "isReadonly": true, "isRef": true, "isURL": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "treeDataTranslate": true, "triggerRef": true, "unref": true, "uploadFile": true, "useAttrs": true, "useCommonStore": true, "scoreProdStore": true, "useCssModule": true, "useCssVars": true, "useLink": true, "useRoute": true, "useRouter": true, "useSlots": true, "useUserStore": true, "useWebConfigStore": true, "validHtmlLength": true, "validNoEmptySpace": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}