{"name": "mall4v", "private": true, "version": "0.0.0", "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "dev:test": "vite --mode testing", "build": "vite build", "build:test": "vite build --mode testing", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --fix --ext .js,.vue src", "preview": "vite preview", "lint:staged": "lint-staged"}, "engines": {"node": ">=16", "pnpm": ">=6"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "@smallwei/avue": "3.2.22", "axios": "1.3.4", "big.js": "6.2.1", "browser-image-compression": "2.0.2", "crypto-js": "4.1.1", "echarts": "5.4.1", "element-plus": "2.3.6", "element-resize-detector": "1.2.4", "js-base64": "3.7.5", "lodash": "4.17.21", "moment": "2.29.4", "pinia": "2.0.33", "qs": "6.11.1", "vue": "3.2.47", "vue-cookies": "1.8.3", "vue-draggable-next": "2.1.1", "vue-router": "4.1.6"}, "devDependencies": {"@babel/eslint-parser": "7.21.3", "@vitejs/plugin-vue": "4.1.0", "eslint": "8.38.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-n": "15.7.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "9.10.0", "eslint-plugin-vue-scoped-css": "2.4.0", "lint-staged": "13.2.2", "sass": "1.59.3", "unplugin-auto-import": "0.15.1", "unplugin-vue-components": "0.24.1", "vite": "4.3.9", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "1.8.1", "vite-plugin-svg-icons": "2.0.1", "vue-eslint-parser": "9.1.1"}, "lint-staged": {"*.{js,vue}": ["eslint --fix"]}}