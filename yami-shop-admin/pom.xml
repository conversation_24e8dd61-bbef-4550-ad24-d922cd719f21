<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yami-shop-admin</artifactId>

    <parent>
        <groupId>com.yami.shop</groupId>
        <artifactId>yami-shop</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>com.yami.shop</groupId>
            <artifactId>yami-shop-service</artifactId>
            <version>${yami.shop.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yami.shop</groupId>
            <artifactId>yami-shop-sys</artifactId>
            <version>${yami.shop.version}</version>
        </dependency>
       <dependency>
	       <groupId>org.apache.poi</groupId>
	       <artifactId>poi-ooxml</artifactId>
	       <version>${poi.version}</version>
       </dependency>
        <dependency>
            <groupId>com.yami.shop</groupId>
            <artifactId>yami-shop-security-admin</artifactId>
            <version>${yami.shop.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
	            	<execution>
	              		<goals>
	                		<goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
	              		</goals>
	            	</execution>
	          	</executions>
            </plugin>

        </plugins>
    </build>
</project>
