/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.admin.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.ATestUser;
import com.yami.shop.bean.model.ProdTag;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.CustomServerResponseEntity;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.admin.util.SecurityUtils;
import com.yami.shop.service.ATestUserService;
import com.yami.shop.service.ProdTagService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商品分组
 *
 * <AUTHOR>
 * @date 2019-04-18 09:08:36
 */
@RestController
@RequestMapping("/user/testUser")
public class ATestUserController {
    @Autowired
    private ProdTagService prodTagService;
    @Autowired
    private ATestUserService aTestUserService;

    // 添加一个简单的接口测试方法
    @GetMapping
    public String hello() {
        return "Hello from ATestUserController!";
    }

    @GetMapping("/page")
    public ServerResponseEntity<IPage<ATestUser>> getProdTagPage(PageParam<ATestUser> page, ATestUser aTestUser) {
        IPage<ATestUser> tagPage = aTestUserService.page(
                page, new LambdaQueryWrapper<ATestUser>()
                        .eq(aTestUser.getId() != null, ATestUser::getId, aTestUser.getId())
                        .like(aTestUser.getUserName() != null, ATestUser::getUserName, aTestUser.getUserName())
                        .orderByDesc(ATestUser::getId, ATestUser::getCreateTime));
        ServerResponseEntity<IPage<ATestUser>> response = ServerResponseEntity.success(tagPage);
        response.setMsg("成功");
        response.setMsg("/user/testUser/page 查询成功");
        return response;
    }

    /**
     * 根据用户ID获取用户信息
     * <p>
     * 该方法通过RESTful API接口，接收一个用户ID参数，
     * 调用服务层方法查询对应的用户信息，并将结果包装成统一响应格式返回。
     *
     * @param id 用户唯一标识ID，从URL路径中提取
     * @return ServerResponseEntity封装的ATestUser对象，包含请求处理结果和用户数据
     * <AUTHOR>
     */
    @GetMapping("/info/{id}")
    public ServerResponseEntity<ATestUser> getById(@PathVariable("id") Long id) {
        try {
            ATestUser aTestUser = aTestUserService.getById(id);
            if (null == aTestUser) {
                return ServerResponseEntity.showFailMsg("用户不存在");
            }
            if (null == aTestUser.getId()) {
                return ServerResponseEntity.showFailMsg("用户ID不存在");
            }
            return ServerResponseEntity.success(aTestUser);
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponseEntity.showFailMsg(e.getMessage());
        }
    }
    @GetMapping("/info/hobby/{id}")
    public ServerResponseEntity<Map<String, String>> getHobbyById(@PathVariable("id") Long id) {
        try {
            ATestUser aTestUser = aTestUserService.getById(id);
            if (null == aTestUser) {
                return CustomServerResponseEntity.fail("用户不存在");
            }
            if (null == aTestUser.getId()) {
                return CustomServerResponseEntity.fail("用户ID不存在");
            }
            Map<String, String> result = new HashMap<>();
            result.put("hobby", aTestUser.getHobby());
            return CustomServerResponseEntity.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return CustomServerResponseEntity.fail(e.getMessage());
        }
    }

    @SysLog("新增测试用户")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('aTest:aTestUser:save')")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid ATestUser aTestUser) {
        // 查看是否相同的标签
        List<ATestUser> list = aTestUserService.list(new LambdaQueryWrapper<ATestUser>().like(ATestUser::getId, aTestUser.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new YamiShopBindException("用户已存在");
        }
        return ServerResponseEntity.success(aTestUserService.save(aTestUser));
    }

    @SysLog("修改测试用户")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('aTest:aTestUser:update')")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid ATestUser aTestUser) {
        aTestUser.setUpdateTime(new Date());
        return ServerResponseEntity.success(aTestUserService.updateById(aTestUser));
    }

    @SysLog("删除测试用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('aTest:aTestUser:delete')")
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long id) {
        ATestUser atestUser = aTestUserService.getById(id);
        if (null == atestUser || null == atestUser.getId()) {
            throw new YamiShopBindException("用户不存在");
        }
//        aTestUserService.removeById(id);
        return ServerResponseEntity.success(aTestUserService.removeById(id));
    }

}