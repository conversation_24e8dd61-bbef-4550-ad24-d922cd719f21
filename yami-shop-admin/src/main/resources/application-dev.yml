server:
  port: 8085
spring:
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
logging:
  config: classpath:logback/logback-dev.xml
xxl-job:
  accessToken: default_token
  logPath: /data/applogs/xxl-job/jobhandler
  admin:
    addresses: http://localhost:8080/xxl-job-admin
