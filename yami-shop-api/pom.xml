<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>yami-shop</artifactId>
		<groupId>com.yami.shop</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>yami-shop-api</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.yami.shop</groupId>
			<artifactId>yami-shop-service</artifactId>
			<version>${yami.shop.version}</version>
		</dependency>
		<dependency>
			<groupId>com.yami.shop</groupId>
			<artifactId>yami-shop-security-api</artifactId>
			<version>${yami.shop.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
