server:
  port: 8086
spring:
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      connection-test-query: select 1
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
logging:
  config: classpath:logback/logback-dev.xml

