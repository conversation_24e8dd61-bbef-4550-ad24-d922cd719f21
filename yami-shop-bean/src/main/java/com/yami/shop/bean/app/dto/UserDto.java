/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
/**
 * <AUTHOR>
 */
public class UserDto {


	@Schema(description = "用户状态：0禁用 1正常" ,requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer status;
	@Schema(description = "token" ,requiredMode = Schema.RequiredMode.REQUIRED)
	private String token;
	
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	
}
