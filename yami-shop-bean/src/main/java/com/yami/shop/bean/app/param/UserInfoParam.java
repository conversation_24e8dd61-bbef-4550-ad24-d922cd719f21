/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "设置用户信息")
public class UserInfoParam {
	
	@Schema(description = "用户昵称" )
	private String nickName;
	
	@Schema(description = "用户头像" )
	private String avatarUrl;

}
