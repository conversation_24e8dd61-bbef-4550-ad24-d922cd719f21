/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.bean.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("a_test_user")
public class ATestUser implements Serializable {
    private static final long serialVersionUID = -6013320537436191451L;
    @TableId
    @Schema(description = "id" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "userName" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String userName;

    @Schema(description = "password" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @Schema(description = "hobby" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String hobby;

    @Schema(description = "createTime" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Date createTime;

    @Schema(description = "updateTime" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Date updateTime;

}
