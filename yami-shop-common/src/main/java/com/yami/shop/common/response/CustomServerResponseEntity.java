/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.common.response;

/**
 * 自定义服务器响应实体类，提供默认消息处理
 * <AUTHOR>
 */
public class CustomServerResponseEntity<T> extends ServerResponseEntity<T> {

    /**
     * 成功响应，默认消息为"处理成功"
     * @param data 数据
     * @param <T> 数据类型
     * @return 响应实体
     */
    public static <T> ServerResponseEntity<T> success(T data) {
        ServerResponseEntity<T> serverResponseEntity = new ServerResponseEntity<>();
        serverResponseEntity.setData(data);
        serverResponseEntity.setCode(ResponseEnum.OK.value());
        serverResponseEntity.setMsg("处理成功");
        return serverResponseEntity;
    }

    /**
     * 成功响应，默认消息为"处理成功"
     * @param <T> 数据类型
     * @return 响应实体
     */
    public static <T> ServerResponseEntity<T> success() {
        ServerResponseEntity<T> serverResponseEntity = new ServerResponseEntity<>();
        serverResponseEntity.setCode(ResponseEnum.OK.value());
        serverResponseEntity.setMsg("处理成功");
        return serverResponseEntity;
    }

    /**
     * 失败响应，默认消息为"处理失败"
     * @param <T> 数据类型
     * @return 响应实体
     */
    public static <T> ServerResponseEntity<T> fail() {
        ServerResponseEntity<T> serverResponseEntity = new ServerResponseEntity<>();
        serverResponseEntity.setCode(ResponseEnum.SHOW_FAIL.value());
        serverResponseEntity.setMsg("处理失败");
        return serverResponseEntity;
    }

    /**
     * 失败响应，使用自定义消息
     * @param msg 失败消息
     * @param <T> 数据类型
     * @return 响应实体
     */
    public static <T> ServerResponseEntity<T> fail(String msg) {
        ServerResponseEntity<T> serverResponseEntity = new ServerResponseEntity<>();
        serverResponseEntity.setCode(ResponseEnum.SHOW_FAIL.value());
        serverResponseEntity.setMsg(msg);
        return serverResponseEntity;
    }
}