# Yami-Shop 项目概述

## 项目简介

Yami-Shop 是一个基于 Spring Boot 的电商系统，采用前后端分离和模块化设计。该项目支持分布式锁，适用于生产环境的多实例部署，数据库设计为 B2B2C 模式，具备完整的 SKU 和下单流程。

## 技术栈

### 后端技术
- **核心框架**: Spring Boot 3.4.5, Spring Security
- **权限认证**: Sa-Token 权限认证框架
- **持久层**: MyBatis-Plus 3.5.10.1
- **缓存和分布式**: Redisson（分布式锁）
- **接口文档**: Knife4j
- **工具库**: Hutool 工具库
- **开发简化**: Lombok 简化开发
- **数据库**: MySQL, Redis

### 前端技术
- **管理后台**: Vue.js + Element Plus
- **跨平台应用**: Uni-app
- **微信小程序**: 微信小程序原生开发

## 项目结构

```
mall4j/
├── db/                    # 数据库脚本
├── doc/                   # 项目文档
├── front-end/             # 前端代码
│   ├── mall4m/            # 微信小程序
│   ├── mall4uni/          # Uni-app跨平台应用
│   └── mall4v/            # Vue管理后台
├── yami-shop-admin/       # 管理后台模块
├── yami-shop-api/         # API接口模块
├── yami-shop-bean/        # 实体类模块
├── yami-shop-common/      # 公共工具类模块
├── yami-shop-security/    # 安全认证模块
├── yami-shop-service/     # 业务逻辑模块
└── yami-shop-sys/         # 系统管理模块
```

## 模块化设计

项目采用模块化设计，将整个后端系统按照功能职责划分为多个独立的、可重用的模块，每个模块都有明确的职责和边界。

### 核心模块说明

#### 1. yami-shop-bean (实体类模块)
包含所有数据模型类，对应数据库表结构。

#### 2. yami-shop-common (公共工具类模块)
提供通用工具和基础服务，供其他模块共享使用。

#### 3. yami-shop-security (安全认证模块)
处理用户认证和授权，进一步细分为：
- yami-shop-security-common: 安全公共模块
- yami-shop-security-api: API安全模块
- yami-shop-security-admin: 管理后台安全模块

#### 4. yami-shop-service (业务逻辑模块)
实现核心业务功能，是系统的核心业务处理模块。

#### 5. yami-shop-sys (系统管理模块)
处理系统级别的功能，如用户、角色、权限等。

#### 6. yami-shop-api (API接口模块)
面向终端用户的移动端接口服务，主要功能包括：
- 商品浏览和搜索
- 购物车管理
- 订单创建和支付
- 个人中心管理
- 收货地址管理
- 商品评价等用户操作

#### 7. yami-shop-admin (管理后台模块)
面向商家/管理员的后台管理接口服务，主要功能包括：
- 商品管理（增删改查）
- 订单管理（查看、发货等操作）
- 用户管理
- 数据统计与分析
- 系统配置管理
- 营销活动管理

## 前端项目

### 1. mall4v (管理后台前端)
- **技术栈**: Vue 3、Element Plus、Vite
- **对应后端**: yami-shop-admin
- **面向用户**: 商家、管理员等后台管理人员

### 2. mall4m (微信小程序)
- **技术栈**: 微信小程序原生开发
- **对应后端**: yami-shop-api
- **面向用户**: 普通消费者

### 3. mall4uni (Uni-app跨平台应用)
- **技术栈**: Uni-app、Vue.js
- **对应后端**: yami-shop-api
- **面向用户**: 普通消费者

## 启动方式

项目中的各个模块是独立的Spring Boot应用，需要单独启动：
- `WebApplication` 用于启动管理后台(yami-shop-admin)
- `ApiApplication` 用于启动API服务(yami-shop-api)
- 启动一个模块不会自动启动其他模块

## 设计原则

1. **模块化设计**：按照功能职责划分为独立模块，每个模块具有明确的职责边界
2. **关注点分离**：管理功能和用户功能完全隔离
3. **可重用性**：公共功能可以被多个模块共享使用
4. **易于扩展**：添加新功能时只需在相应模块中实现
5. **独立部署**：各模块可以独立部署和扩展