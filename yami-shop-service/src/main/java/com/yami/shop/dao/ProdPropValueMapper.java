/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.bean.model.ProdPropValue;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProdPropValueMapper extends BaseMapper<ProdPropValue> {

	/**
	 * 插入商品属性数值
	 * @param propId
	 * @param prodPropValues
	 */
	void insertPropValues(@Param("propId") Long propId, @Param("prodPropValues") List<ProdPropValue> prodPropValues);

	/**
	 * 删除属性数值
	 * @param propId
	 */
	void deleteByPropId(@Param("propId") Long propId);
}