/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.model.ATestUser;
import com.yami.shop.bean.model.ProdTag;
import com.yami.shop.common.util.PageParam;

import java.util.List;

/**
 * <AUTHOR> on 2018/10/26.
 */
public interface ATestUserService extends IService<ATestUser> {

    /**
     * 获取商品分组标签列表
     * @return
     */
    List<ATestUser> listATestUser();


}
