/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.yami.shop.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.model.ProdTag;

import java.util.List;

/**
 * 商品分组标签
 *
 * <AUTHOR>
 * @date 2019-04-18 10:48:44
 */
public interface ProdTagService extends IService<ProdTag> {
    /**
     * 获取商品分组标签列表
     * @return
     */
    List<ProdTag> listProdTag();

    /**
     * 删除商品分组标签缓存
     */
    void removeProdTag();
}
