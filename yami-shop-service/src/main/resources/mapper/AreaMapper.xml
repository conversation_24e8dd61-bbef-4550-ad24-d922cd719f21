<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.AreaMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.Area">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
    </resultMap>

</mapper>
