<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.CategoryBrandMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.CategoryBrand">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
  </resultMap>
  
  <insert id="insertCategoryBrand">
  	insert into tz_category_brand (category_id,brand_id) values
  	<foreach collection="brandIds" item="brandId" separator=",">
  		(#{categoryId},#{brandId})
  	</foreach>
  </insert>
  <delete id="deleteByCategoryId">
  	delete from tz_category_brand where category_id = #{categoryId}
  </delete>
  
  <delete id="deleteByBrandId">
  	delete from tz_category_brand where brand_id = #{brandId}
  </delete>
</mapper>