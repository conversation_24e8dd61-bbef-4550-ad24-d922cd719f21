<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.MessageMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.Message">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="reply" jdbcType="LONGVARCHAR" property="reply" />
  </resultMap>
</mapper>
