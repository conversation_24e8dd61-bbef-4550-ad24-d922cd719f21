<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.dao.NoticeMapper">

    <resultMap id="noticeMap" type="com.yami.shop.bean.model.Notice">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="pageNotice" resultType="com.yami.shop.bean.app.dto.NoticeDto">
      SELECT n.id,n.shop_id,n.title,n.publish_time
      FROM tz_notice n
      WHERE n.`status` = 1
      ORDER BY n.is_top DESC, n.`publish_time` DESC
    </select>
</mapper>
