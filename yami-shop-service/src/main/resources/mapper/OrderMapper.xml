<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.OrderMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.Order">
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dvy_type" jdbcType="VARCHAR" property="dvyType"/>
        <result column="dvy_id" jdbcType="BIGINT" property="dvyId"/>
        <result column="dvy_flow_id" jdbcType="VARCHAR" property="dvyFlowId"/>
        <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount"/>
        <result column="addr_order_id" jdbcType="BIGINT" property="addrOrderId"/>
        <result column="product_nums" jdbcType="INTEGER" property="productNums"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="dvy_time" jdbcType="TIMESTAMP" property="dvyTime"/>
        <result column="finally_time" jdbcType="TIMESTAMP" property="finallyTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="is_payed" jdbcType="BIT" property="isPayed"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="refund_sts" jdbcType="INTEGER" property="refundSts"/>
    </resultMap>

    <resultMap id="MyOrderMap" type="com.yami.shop.bean.app.dto.MyOrderDto">
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <collection property="orderItemDtos" ofType="com.yami.shop.bean.app.dto.MyOrderItemDto">
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
            <result column="prod_count" jdbcType="INTEGER" property="prodCount"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        </collection>
    </resultMap>

    <resultMap id="OrderShopMap" type="com.yami.shop.bean.app.dto.OrderShopDto">
        <result column="shop_id" jdbcType="INTEGER" property="shopId"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="total_num" jdbcType="INTEGER" property="totalNum"/>
        <result column="transfee" jdbcType="DECIMAL" property="transfee"/>
        <result column="reduce_amount" jdbcType="DECIMAL" property="reduceAmount"/>
        <association property="userAddrDto" javaType="com.yami.shop.bean.app.dto.UserAddrDto">
            <result column="addr_id" jdbcType="INTEGER" property="addrId"/>
            <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
            <result column="province" jdbcType="VARCHAR" property="province"/>
            <result column="city" jdbcType="VARCHAR" property="city"/>
            <result column="area" jdbcType="VARCHAR" property="area"/>
            <result column="addr" jdbcType="VARCHAR" property="addr"/>
            <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        </association>
        <collection property="orderItemDtos" ofType="com.yami.shop.bean.app.dto.OrderItemDto">
            <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
            <result column="prod_count" jdbcType="INTEGER" property="prodCount"/>
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="product_total_amount" jdbcType="INTEGER" property="productTotalAmount"/>
            <result column="prod_id" jdbcType="INTEGER" property="prodId"/>
            <result column="sku_id" jdbcType="INTEGER" property="skuId"/>
        </collection>
    </resultMap>

    <resultMap type="com.yami.shop.bean.model.Order" id="orderAndOrderItemMap">
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dvy_type" jdbcType="VARCHAR" property="dvyType"/>
        <result column="dvy_id" jdbcType="BIGINT" property="dvyId"/>
        <result column="dvy_flow_id" jdbcType="VARCHAR" property="dvyFlowId"/>
        <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount"/>
        <result column="addr_order_id" jdbcType="BIGINT" property="addrOrderId"/>
        <result column="product_nums" jdbcType="INTEGER" property="productNums"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="dvy_time" jdbcType="TIMESTAMP" property="dvyTime"/>
        <result column="finally_time" jdbcType="TIMESTAMP" property="finallyTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="is_payed" jdbcType="BIT" property="isPayed"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="refund_sts" jdbcType="INTEGER" property="refundSts"/>
        <collection property="orderItems" ofType="com.yami.shop.bean.model.OrderItem">
            <id column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
            <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
            <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
            <result column="prod_id" jdbcType="BIGINT" property="prodId"/>
            <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
            <result column="prod_count" jdbcType="INTEGER" property="prodCount"/>
            <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="user_id" jdbcType="VARCHAR" property="userId"/>
            <result column="product_total_amount" jdbcType="DECIMAL" property="productTotalAmount"/>
            <result column="rec_time" jdbcType="TIMESTAMP" property="recTime"/>
            <result column="comm_sts" jdbcType="INTEGER" property="commSts"/>
        </collection>
    </resultMap>

    <resultMap type="com.yami.shop.bean.model.Order" id="orderAndOrderItemAndUserAddrMap">
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dvy_type" jdbcType="VARCHAR" property="dvyType"/>
        <result column="dvy_id" jdbcType="BIGINT" property="dvyId"/>
        <result column="dvy_flow_id" jdbcType="VARCHAR" property="dvyFlowId"/>
        <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount"/>
        <result column="addr_order_id" jdbcType="BIGINT" property="addrOrderId"/>
        <result column="product_nums" jdbcType="INTEGER" property="productNums"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="dvy_time" jdbcType="TIMESTAMP" property="dvyTime"/>
        <result column="finally_time" jdbcType="TIMESTAMP" property="finallyTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="is_payed" jdbcType="BIT" property="isPayed"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="refund_sts" jdbcType="INTEGER" property="refundSts"/>
        <association property="userAddrOrder" javaType="com.yami.shop.bean.model.UserAddrOrder">
            <id column="addr_order_id" jdbcType="BIGINT" property="addrOrderId"/>
            <result column="province" jdbcType="VARCHAR" property="province"/>
            <result column="city" jdbcType="VARCHAR" property="city"/>
            <result column="area" jdbcType="VARCHAR" property="area"/>
            <result column="addr" jdbcType="VARCHAR" property="addr"/>
            <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
            <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        </association>
        <collection property="orderItems" ofType="com.yami.shop.bean.model.OrderItem">
            <id column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
            <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
            <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
            <result column="prod_id" jdbcType="BIGINT" property="prodId"/>
            <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
            <result column="prod_count" jdbcType="INTEGER" property="prodCount"/>
            <result column="item_prod_name" jdbcType="VARCHAR" property="prodName"/>
            <result column="item_sku_name" jdbcType="VARCHAR" property="skuName"/>
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="user_id" jdbcType="VARCHAR" property="userId"/>
            <result column="product_total_amount" jdbcType="DECIMAL" property="productTotalAmount"/>
            <result column="rec_time" jdbcType="TIMESTAMP" property="recTime"/>
            <result column="comm_sts" jdbcType="INTEGER" property="commSts"/>
        </collection>
    </resultMap>

    <select id="getOrderByOrderNumber" resultType="com.yami.shop.bean.model.Order">
    select * from tz_order o where o.order_number = #{orderNumber}
  </select>

    <select id="listOrderAndOrderItems" resultMap="orderAndOrderItemMap">
    select o.*,oi.* from tz_order o left join tz_order_item oi on o.order_number = oi.order_number
    where o.status = #{orderStatus}
    and o.update_time &lt; #{lessThanUpdateTime}
  </select>

    <update id="cancelOrders">
        update tz_order set `status`=6,cancel_time = NOW() where is_payed = 0 and order_id in
        <foreach collection="orders" item="order" open="(" close=")" separator=",">
            #{order.orderId}
        </foreach>
    </update>

    <update id="confirmOrder">
        update tz_order set `status`=5,finally_time = NOW() where order_id in
        <foreach collection="orders" item="order" open="(" close=")" separator=",">
            #{order.orderId}
        </foreach>
    </update>

    <update id="updateByToPaySuccess">
    update tz_order set status = 2,is_payed =1,update_time=NOW(),pay_time=NOW(),pay_type =#{payType} where order_number in
    <foreach collection="orderNumbers" item="orderNumber" separator="," open="(" close=")">
        #{orderNumber}
    </foreach>
  </update>

    <select id="listOrdersDetailByOrder" resultMap="orderAndOrderItemAndUserAddrMap">
        select o.*,oi.*,oi.prod_name as item_prod_name,uao.* from tz_order o
        left join tz_order_item oi on o.order_number = oi.order_number
        left join tz_user_addr_order uao on o.addr_order_id = uao.addr_order_id
        where o.order_id in (
        select * from (
        select o.order_id from tz_order o
        where 1=1
        <if test="order.orderNumber != null and order.orderNumber != ''">
            and o.order_number like concat('%',#{order.orderNumber},'%')
        </if>
        <if test="order.status != null">
            and o.status = #{order.status}
        </if>
        <if test="order.shopId != null">
            and o.shop_id = #{order.shopId}
        </if>
        <if test="order.isPayed != null">
            and o.is_payed = #{order.isPayed}
            and o.status != 6
        </if>
        <if test="startTime != null">
            and o.create_time &gt; #{startTime}
        </if>
        <if test="endTime != null">
            and o.create_time &lt; #{endTime}
        </if>
        ORDER BY o.create_time DESC
        )
        AS limittable) ORDER BY o.create_time DESC
    </select>

    <select id="listOrdersDetailByOrderParam" resultMap="orderAndOrderItemAndUserAddrMap">
        SELECT *,oi.sku_name as item_sku_name,oi.prod_name as item_prod_name FROM
        (
        SELECT * FROM tz_order
        <where>
            <if test="orderParam.orderNumber != null and orderParam.orderNumber != ''">
                and order_number = #{orderParam.orderNumber}
            </if>
            <if test="orderParam.status != null">
                and status = #{orderParam.status}
            </if>
            <if test="orderParam.shopId != null">
                and shop_id = #{orderParam.shopId}
            </if>
            <if test="orderParam.isPayed != null">
                and is_payed = #{orderParam.isPayed}
                and status != 6
            </if>
            <if test="orderParam.startTime != null">
                and create_time &gt; #{orderParam.startTime}
            </if>
            <if test="orderParam.endTime != null">
                and create_time &lt; #{orderParam.endTime}
            </if>
        </where>
        ORDER BY update_time DESC
        LIMIT #{adapter.begin} , #{adapter.size}
        ) AS o
        LEFT JOIN tz_order_item oi
        ON o.order_number = oi.order_number
        LEFT JOIN tz_user_addr_order uao
        ON o.addr_order_id = uao.addr_order_id
    </select>

    <select id="countOrderDetail" resultType="long">
        SELECT COUNT(0) FROM tz_order
        <where>
            <if test="orderParam.orderNumber != null and orderParam.orderNumber != ''">
                and order_number = #{orderParam.orderNumber}
            </if>
            <if test="orderParam.status != null">
                and status = #{orderParam.status}
            </if>
            <if test="orderParam.shopId != null">
                and shop_id = #{orderParam.shopId}
            </if>
            <if test="orderParam.isPayed != null">
                and is_payed = #{orderParam.isPayed}
                and status != 6
            </if>
            <if test="orderParam.startTime != null">
                and create_time &gt; #{orderParam.startTime}
            </if>
            <if test="orderParam.endTime != null">
                and create_time &lt; #{orderParam.endTime}
            </if>
        </where>
    </select>

    <select id="listMyOrderByUserIdAndStatus" resultMap="MyOrderMap">
        SELECT o.actual_total,oi.pic,oi.price,oi.prod_name,oi.sku_name,oi.prod_count,o.status,o.order_number
        FROM (
        SELECT * FROM tz_order temp
        WHERE temp.user_id = #{userId} and temp.delete_status = 0
        <if test="status != null and status != 0">
            AND temp.status = #{status}
        </if>
        ORDER BY temp.create_time DESC
        LIMIT #{adapter.begin} , #{adapter.size}
        )AS o
        LEFT JOIN tz_order_item oi ON o.order_number = oi.order_number
    </select>


    <select id="countMyOrderByUserIdAndStatus" resultType="Long">
        select count(0) from tz_order o
        where o.user_id = #{userId} and o.delete_status = 0
        <if test="status != null and status != 0">
            and o.status = #{status}
        </if>
    </select>


    <update id="deleteOrders">
        UPDATE tz_order SET `delete_status`=2
        WHERE order_id IN
        <foreach collection="orders" item="order" open="(" close=")" separator=",">
            #{order.orderId}
        </foreach>
    </update>

    <select id="getOrderCount" resultType="com.yami.shop.bean.app.dto.OrderCountData">
        SELECT
        COUNT(o.order_id) all_count,
        COUNT( CASE WHEN o.status = 1 THEN o.order_id ELSE NULL END ) AS unPay,
        COUNT( CASE WHEN o.status = 2 THEN o.order_id ELSE NULL END ) AS payed,
        COUNT( CASE WHEN o.status = 3 THEN o.order_id ELSE NULL END ) AS consignment,
        COUNT( CASE WHEN o.status = 4 THEN o.order_id ELSE NULL END ) AS confirm,
        COUNT( CASE WHEN o.status = 5 THEN o.order_id ELSE NULL END ) AS success,
        COUNT( CASE WHEN o.status = 6 THEN o.order_id ELSE NULL END ) AS `close`
        FROM tz_order o
        WHERE o.user_id =#{userId}
    </select>

</mapper>
