<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.OrderSettlementMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.OrderSettlement">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="settlement_id" jdbcType="BIGINT" property="settlementId" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="biz_pay_no" jdbcType="VARCHAR" property="bizPayNo" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="is_clearing" jdbcType="INTEGER" property="isClearing" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="clearing_time" jdbcType="TIMESTAMP" property="clearingTime" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
  </resultMap>
  <update id="updateByOrderNumberAndUserId" parameterType="com.yami.shop.bean.model.OrderSettlement">
    update tz_order_settlement
	SET
	  <if test="orderSettlement.payNo != null ">
	   pay_no = #{orderSettlement.payNo},
	  </if>
	  <if test="orderSettlement.payType != null ">
       pay_type = #{orderSettlement.payType},
      </if>
      <if test="orderSettlement.isClearing != null ">
       is_clearing = #{orderSettlement.isClearing},
      </if>
      <if test="orderSettlement.clearingTime != null ">
       clearing_time = #{orderSettlement.clearingTime},
      </if>
      order_number = #{orderSettlement.orderNumber}
	WHERE order_number = #{orderSettlement.orderNumber} and user_id = #{orderSettlement.userId}
  </update>
  
  <select id="getSettlementsByPayNo" resultType="com.yami.shop.bean.model.OrderSettlement">
    select * from tz_order_settlement WHERE pay_no = #{payNo}
  </select>
  
  <update id="updateSettlementsByPayNo">
    update tz_order_settlement 
    <if test="outTradeNo !=null and transactionId != null">
        set 
            biz_pay_no = #{transactionId},
            is_clearing = 1,
            pay_status = 1
    </if>
    where pay_no = #{outTradeNo}
  </update>
  
  <update id="updateToPay">
  	update tz_order_settlement set pay_status = 1, version = version +1 where pay_no = #{payNo} and version = #{version}
  </update>
</mapper>