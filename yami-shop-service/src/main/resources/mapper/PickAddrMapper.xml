<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.PickAddrMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.PickAddr">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="addr_id" jdbcType="BIGINT" property="addrId" />
    <result column="addr_name" jdbcType="VARCHAR" property="addrName" />
    <result column="addr" jdbcType="VARCHAR" property="addr" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
  </resultMap>

</mapper>