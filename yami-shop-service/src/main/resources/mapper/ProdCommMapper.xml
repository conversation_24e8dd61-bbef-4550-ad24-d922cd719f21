<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.ProdCommMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.ProdComm">
        <id column="prod_comm_id" jdbcType="BIGINT" property="prodCommId"/>
        <result property="prodId" column="prod_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="replyContent" column="reply_content"/>
        <result property="recTime" column="rec_time"/>
        <result property="replyTime" column="reply_time"/>
        <result property="replySts" column="reply_sts"/>
        <result property="postip" column="postip"/>
        <result property="score" column="score"/>
        <result property="usefulCounts" column="useful_counts"/>
        <result property="pics" column="pics"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="status" column="status"/>
        <result property="evaluate" column="evaluate"/>
    </resultMap>


    <resultMap id="ProdCommData" type="com.yami.shop.bean.app.dto.ProdCommDataDto">
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="praise_number" jdbcType="INTEGER" property="praiseNumber"/>
        <result column="secondary_number" jdbcType="INTEGER" property="secondaryNumber"/>
        <result column="negative_number" jdbcType="INTEGER" property="negativeNumber"/>
        <result column="pic_number" jdbcType="INTEGER" property="picNumber"/>
    </resultMap>


    <select id="getProdCommDataByProdId" resultMap="ProdCommData">
        SELECT count(1)                                               AS number,
               count(CASE WHEN evaluate = 0 THEN 1 ELSE null END)     AS praise_number,
               count(CASE WHEN evaluate = 1 THEN 1 ELSE null END)     AS secondary_number,
               count(CASE WHEN evaluate = 2 THEN 1 ELSE null END)     AS negative_number,
               count(CASE WHEN pics is not null THEN 1 ELSE null END) AS pic_number
        FROM tz_prod_comm
        WHERE prod_id = #{prodId} and status = 1
    </select>


    <sql id="ProdCommDto_SQL">
        pc.prod_comm_id,
        pc.reply_content,
        pc.rec_time,
        pc.score,
        pc.is_anonymous,
        pc.pics,
        pc.user_id,
        pc.reply_sts,
        pc.evaluate,
        pc.content,
        u.nick_name,
        u.pic
    </sql>

    <resultMap id="ProdCommDto" type="com.yami.shop.bean.app.dto.ProdCommDto">
        <id column="prod_comm_id" jdbcType="BIGINT" property="prodCommId"/>
        <result column="reply_content" jdbcType="VARCHAR" property="replyContent"/>
        <result column="rec_time" jdbcType="TIMESTAMP" property="recTime"/>
        <result column="reply_time" jdbcType="TIMESTAMP" property="replyTime"/>
        <result column="score" jdbcType="TINYINT" property="score"/>
        <result column="is_anonymous" jdbcType="INTEGER" property="isAnonymous"/>
        <result column="pics" jdbcType="VARCHAR" property="pics"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="reply_sts" jdbcType="VARCHAR" property="replySts"/>
        <result property="evaluate" jdbcType="TINYINT" column="evaluate"/>
        <result property="content" jdbcType="VARCHAR" column="content"/>
    </resultMap>

    <select id="getProdCommDtoPageByProdId" resultMap="ProdCommDto">
        select
        <include refid="ProdCommDto_SQL"/>
        from
        tz_prod_comm pc
        left join tz_user u on u.user_id=pc.user_id
        where pc.prod_id = #{prodId} and pc.status = 1
        <if test="evaluate != -1 and evaluate != 3">
            and pc.evaluate = #{evaluate}
        </if>
        <if test="evaluate == 3">
            and pc.pics is not null
        </if>
        order by pc.rec_time desc
    </select>

    <select id="getProdCommDtoPageByUserId" resultMap="ProdCommDto">
        select
        <include refid="ProdCommDto_SQL"/>
        from
        tz_prod_comm pc
        left join tz_user u on u.user_id=pc.user_id
        where pc.user_id = #{userId} and pc.status = 1
    </select>

    <!--评论管理-->
    <resultMap id="ProdComm_Prod_User" type="com.yami.shop.bean.model.ProdComm">
        <id column="prod_comm_id" jdbcType="BIGINT" property="prodCommId"/>
        <result property="prodId" column="prod_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="replyContent" column="reply_content"/>
        <result property="recTime" column="rec_time"/>
        <result property="replyTime" column="reply_time"/>
        <result property="replySts" column="reply_sts"/>
        <result property="postip" column="postip"/>
        <result property="score" column="score"/>
        <result property="usefulCounts" column="useful_counts"/>
        <result property="pics" column="pics"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="status" column="status"/>
        <result property="prodName" column="prod_name"/>
        <result property="evaluate" column="evaluate"/>
        <association property="user" javaType="com.yami.shop.bean.vo.UserVO">
            <id column="user_id" jdbcType="VARCHAR" property="userId"/>
            <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
            <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        </association>
    </resultMap>
    <select id="getProdCommPage" resultMap="ProdComm_Prod_User">
        select
        pc.prod_comm_id,
        pc.score,
        pc.is_anonymous,
        pc.status,
        pc.rec_time,
        pc.reply_time,
        pc.evaluate,
        u.nick_name,
        u.user_mobile,
        p.prod_name
        from
        tz_prod_comm pc
        left join tz_prod p on p.prod_id=pc.prod_id
        left join tz_user u on pc.user_id=u.user_id
        <where>
            <if test="prodComm.status!=null">
                and pc.status=#{prodComm.status}
            </if>
            <if test="prodComm.prodName!=null and prodComm.prodName!= ''">
                and p.prod_name like concat('%',#{prodComm.prodName},'%')
            </if>
        </where>
    </select>

</mapper>
