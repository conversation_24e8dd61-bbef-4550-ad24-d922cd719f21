<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.ProdPropMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.ProdProp">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="prop_id" jdbcType="BIGINT" property="propId"/>
        <result column="prop_name" jdbcType="VARCHAR" property="propName"/>
        <result column="rule" jdbcType="TINYINT" property="rule"/>
    </resultMap>

    <resultMap id="propAndValueMap" type="com.yami.shop.bean.model.ProdProp">
        <id column="prop_id" jdbcType="BIGINT" property="propId"/>
        <result column="prop_name" jdbcType="VARCHAR" property="propName"/>
        <result column="rule" jdbcType="TINYINT" property="rule"/>

        <collection property="prodPropValues" column="prop_id" javaType="java.util.List"
                    ofType="com.yami.shop.bean.model.ProdPropValue">
            <id property="valueId" column="value_id"/>
            <result property="propValue" column="prop_value"/>
        </collection>
    </resultMap>

    <!-- 使用pageHelper进行关联分页时，需要的count sql -->
    <select id="countPropAndValue" parameterType="com.yami.shop.bean.model.ProdProp" resultType="Long">
    select count(0) from tz_prod_prop
    where rule = #{prodProp.rule} and shop_id = #{prodProp.shopId}
    <if test="prodProp.propName != null and prodProp.propName != ''">
    and prop_name like concat('%',#{prodProp.propName},'%')
    </if>
    </select>

    <select id="listPropAndValue" parameterType="com.yami.shop.bean.model.ProdProp" resultMap="propAndValueMap">
        SELECT * FROM (
            select * from tz_prod_prop
            where rule = #{prodProp.rule} and shop_id = #{prodProp.shopId}
            <if test="prodProp.propName != null and prodProp.propName != ''">
                and prop_name like concat('%',#{prodProp.propName},'%')
            </if>
            LIMIT #{adapter.begin} , #{adapter.size}
        ) pp
        left join tz_prod_prop_value ppv
        on pp.prop_id = ppv.prop_id
    </select>

    <delete id="deleteByPropId">
		delete from tz_prod_prop where prop_id = #{propId} and rule = #{rule} and shop_id = #{shopId}
	</delete>

    <select id="getProdPropByPropNameAndShopId" resultType="com.yami.shop.bean.model.ProdProp">
	   select * from tz_prod_prop where prop_name = #{propName} and shop_id = #{shopId} and rule = #{rule}
	</select>

</mapper>
