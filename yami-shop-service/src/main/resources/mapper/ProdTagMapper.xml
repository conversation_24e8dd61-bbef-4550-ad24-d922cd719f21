<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.dao.ProdTagMapper">

    <resultMap id="prodTagMap" type="com.yami.shop.bean.model.ProdTag">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="shopId" column="shop_id"/>
        <result property="status" column="status"/>
        <result property="isDefault" column="is_default"/>
        <result property="prodCount" column="prod_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>
</mapper>
