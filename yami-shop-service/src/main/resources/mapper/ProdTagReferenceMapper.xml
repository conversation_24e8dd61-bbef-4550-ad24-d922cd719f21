<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.dao.ProdTagReferenceMapper">

    <resultMap id="prodTagReferenceMap" type="com.yami.shop.bean.model.ProdTagReference">
        <id property="referenceId" column="reference_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="prodId" column="prod_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="insertBatch">
        INSERT INTO tz_prod_tag_reference (shop_id,tag_id,prod_id,`status`,create_time) VALUES
        <foreach collection="tagList" separator="," item="item">
            (#{shopId},#{item},#{prodId} ,1,NOW())
        </foreach>
    </select>

    <select id="listTagIdByProdId" resultType="long">
      SELECT tag_id FROM tz_prod_tag_reference WHERE prod_id = #{prodId}
    </select>
</mapper>
