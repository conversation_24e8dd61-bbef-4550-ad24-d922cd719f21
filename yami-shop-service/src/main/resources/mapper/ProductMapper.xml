<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.ProductMapper">

    <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.Product">
        <id property="prodId" column="prod_id"/>
        <result property="prodName" column="prod_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="oriPrice" column="ori_price"/>
        <result property="price" column="price"/>
        <result property="brief" column="brief"/>
        <result property="content" column="content"/>
        <result property="imgs" column="imgs"/>
        <result property="status" column="status"/>
        <result property="categoryId" column="category_id"/>
        <result property="soldNum" column="sold_num"/>
        <result property="totalStocks" column="total_stocks"/>
        <result property="pic" column="pic"/>
        <result property="deliveryMode" column="delivery_mode"/>
        <result property="deliveryTemplateId" column="delivery_template_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="version" column="version"/>
    </resultMap>

    <resultMap id="tagProductMap" type="com.yami.shop.bean.app.dto.TagProductDto">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="seq" column="seq"/>
        <result property="style" column="style"/>
        <collection property="productDtoList" javaType="list" ofType="com.yami.shop.bean.app.dto.ProductDto">
            <result property="prodId" column="prod_id"/>
            <result property="shopName" column="shop_name"/>
            <result property="prodName" column="prod_name"/>
            <result property="shopId" column="shop_id"/>
            <result property="price" column="price"/>
            <result property="brief" column="brief"/>
            <result property="imgs" column="imgs"/>
            <result property="oriPrice" column="ori_price"/>
            <result property="categoryId" column="category_id"/>
            <result property="totalStocks" column="total_stocks"/>
            <result property="pic" column="pic"/>
        </collection>

    </resultMap>

    <sql id="productDtoWithNoContent_SQL">
        p.`prod_id`,
        p.`shop_id`,
        p.`prod_name`,
        p.`pic`,
        p.`price`,
        p.`ori_price`,
        p.`brief`,
        p.`imgs`,
        p.`category_id`,
        p.`total_stocks`
    </sql>

    <sql id="prodAndShopNameWithNoContent_SQL">
        p.prod_id,
        p.pic,
        p.prod_name,
        p.price,
        p.brief,
        sd.`shop_name`
    </sql>

    <update id="updateStocks" parameterType="com.yami.shop.bean.model.Product">
		update tz_prod set total_stocks = total_stocks - #{prod.totalStocks} ,version = version + 1 where prod_id = #{prod.prodId} and #{prod.totalStocks} &lt;= total_stocks
	</update>

    <select id="pageByCategoryId" resultType="com.yami.shop.bean.app.dto.ProductDto">
        select tp.*,tc.category_name,tsd.shop_name from tz_prod tp
        left join tz_category tc on tp.category_id = tc.category_id
        left join tz_shop_detail tsd on tp.shop_id = tsd.shop_id
        where tp.status = 1
        <if test="categoryId != null">
            AND tp.category_id = #{categoryId}
        </if>
    </select>


    <update id="returnStock">
        <foreach collection="prodCollect" item="changeStocks" index="prodId" separator=";">
            update tz_prod set total_stocks = total_stocks + #{changeStocks} where prod_id = #{prodId}
        </foreach>
    </update>


    <select id="pageByTagId" resultType="com.yami.shop.bean.app.dto.ProductDto">
        SELECT
        <include refid="prodAndShopNameWithNoContent_SQL"/>
        FROM tz_prod p
        LEFT JOIN tz_prod_tag_reference ptr
        ON ptr.`prod_id` = p.`prod_id`
        LEFT JOIN tz_prod_tag pt
        ON pt.`id` = ptr.`tag_id`
        LEFT JOIN tz_shop_detail sd
        ON p.shop_id = sd.shop_id
        WHERE pt.`id` = #{tagId}
        AND p.status = 1
        ORDER BY p.`update_time` DESC
    </select>

    <select id="pageByPutAwayTime" resultType="com.yami.shop.bean.app.dto.ProductDto">
        SELECT
        <include refid="prodAndShopNameWithNoContent_SQL"/>
        FROM tz_prod p
        LEFT JOIN tz_shop_detail sd
        ON p.shop_id = sd.shop_id
        WHERE `status` = 1
        ORDER BY putaway_time DESC
    </select>

    <select id="moreBuyProdList" resultType="com.yami.shop.bean.app.dto.ProductDto">
        SELECT
        <include refid="prodAndShopNameWithNoContent_SQL"/>
        FROM tz_prod p
        LEFT JOIN tz_shop_detail sd
        ON p.shop_id = sd.shop_id
        WHERE p.`status` = 1
        ORDER BY p.`sold_num` DESC, p.`update_time` DESC
    </select>

    <select id="tagProdList" resultMap="tagProductMap">
        SELECT pt.*,
        <include refid="prodAndShopNameWithNoContent_SQL"/>
        FROM tz_prod_tag pt -- 获取分组信息
        LEFT JOIN
        (-- 分组获取各组前6个
        SELECT ptr.* FROM tz_prod_tag_reference ptr
        INNER JOIN tz_prod p3 ON p3.`prod_id` = ptr.`prod_id` AND p3.`status` =1
        WHERE
        (SELECT COUNT(0) FROM tz_prod_tag_reference prt2
        INNER JOIN tz_prod p2 ON p2.`prod_id` = prt2.`prod_id` AND p2.`status` =1
        WHERE prt2.`tag_id` = ptr.`tag_id` AND prt2.`create_time` > ptr.`create_time`)&lt;6
        ORDER BY ptr.`tag_id`,ptr.`create_time` DESC
        )AS temp
        ON temp.`tag_id` = pt.`id`
        LEFT JOIN tz_prod p -- 获取商品详细信息
        ON p.`prod_id` = temp.`prod_id`
        LEFT JOIN tz_shop_detail sd -- 获取店铺信息
        ON p.shop_id = sd.shop_id
        ORDER BY pt.`seq` DESC
    </select>

    <select id="getSearchProdDtoPageByProdName" resultType="com.yami.shop.bean.dto.SearchProdDto">
        select
        p.prod_id,
        p.pic,
        p.prod_name,
        p.price,
        count(pc.prod_comm_id) as prod_comm_number,
        count( CASE WHEN evaluate = 0 THEN prod_comm_id ELSE null END ) AS praise_number
        from
        tz_prod p
        left join tz_prod_comm pc on p.prod_id=pc.prod_id and pc.`status`=1
        where
        prod_name like concat('%',#{prodName} ,'%')
        GROUP BY p.prod_id
        <if test="sort == 0">
            ORDER BY p.update_time
        </if>
        <if test="sort == 1">
            ORDER BY p.sold_num
        </if>
        <if test="sort == 2">
            ORDER BY p.price
        </if>
        <if test="orderBy == 0">
            asc
        </if>
        <if test="orderBy == 1">
            desc
        </if>

    </select>


    <select id="collectionProds" resultType="com.yami.shop.bean.app.dto.ProductDto">
        SELECT
        <include refid="prodAndShopNameWithNoContent_SQL"/>
        FROM tz_prod p
        LEFT JOIN tz_shop_detail sd
        ON p.shop_id = sd.shop_id
        WHERE p.`prod_id` IN
        (SELECT uc.`prod_id` FROM tz_user_collection uc
        WHERE uc.user_id = #{userId})
    </select>

</mapper>
