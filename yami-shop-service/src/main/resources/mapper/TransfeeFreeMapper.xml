<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.TransfeeFreeMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.TransfeeFree">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="transfee_free_id" jdbcType="BIGINT" property="transfeeFreeId" />
    <result column="transport_id" jdbcType="BIGINT" property="transportId" />
    <result column="free_type" jdbcType="TINYINT" property="freeType" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="piece" jdbcType="DECIMAL" property="piece" />
  </resultMap>
</mapper>