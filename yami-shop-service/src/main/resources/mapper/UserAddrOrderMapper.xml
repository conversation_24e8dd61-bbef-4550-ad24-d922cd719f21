<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.UserAddrOrderMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.UserAddrOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="addr_order_id" jdbcType="BIGINT" property="addrOrderId" />
    <result column="addr_id" jdbcType="BIGINT" property="addrId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="addr" jdbcType="VARCHAR" property="addr" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="area_id" jdbcType="BIGINT" property="areaId" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
</mapper>
