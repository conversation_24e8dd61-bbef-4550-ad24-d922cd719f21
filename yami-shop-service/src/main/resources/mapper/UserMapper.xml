<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.bean.model.User">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="user_mail" jdbcType="VARCHAR" property="userMail" />
    <result column="login_password" jdbcType="VARCHAR" property="loginPassword" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="user_regtime" jdbcType="TIMESTAMP" property="userRegtime" />
    <result column="user_regip" jdbcType="VARCHAR" property="userRegip" />
    <result column="user_lasttime" jdbcType="TIMESTAMP" property="userLasttime" />
    <result column="user_lastip" jdbcType="VARCHAR" property="userLastip" />
    <result column="user_memo" jdbcType="VARCHAR" property="userMemo" />
    <result column="sex" jdbcType="CHAR" property="sex" />
    <result column="birth_date" jdbcType="CHAR" property="birthDate" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <select id="selectOneByUserName" resultType="com.yami.shop.bean.model.User">
    select * from `tz_user` where binary nick_name = #{userName} limit 1
  </select>
</mapper>
