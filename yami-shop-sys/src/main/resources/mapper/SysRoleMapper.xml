<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.sys.dao.SysRoleMapper">

	<delete id="deleteBatch"  parameterType="Long">
		delete from tz_sys_role where role_id in 
  		<foreach collection="roleIds" item="roleId" index="no" open="("  
            separator="," close=")">  
            #{roleId}  
        </foreach>
	</delete>
	
	<select id="listRoleIdByUserId" resultType="Long">
		select role_id from tz_sys_user_role where user_id = #{userId}
	</select>
</mapper>