<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.sys.dao.SysUserMapper">
	
	<!-- 查询用户的所有权限 -->
	<select id="queryAllPerms" resultType="string">
		select m.perms from tz_sys_user_role ur 
			LEFT JOIN tz_sys_role_menu rm on ur.role_id = rm.role_id 
			LEFT JOIN tz_sys_menu m on rm.menu_id = m.menu_id 
		where ur.user_id = #{userId}
	</select>
	
	<delete id="deleteBatch">
		delete from tz_sys_user where user_id in 
  		<foreach collection="userIds" item="userId" index="no" open="("  
            separator="," close=")">  
            #{userId}  
        </foreach>
        and shop_id = #{shopId}
	</delete>
	
	<select id="selectByUsername" resultType="com.yami.shop.sys.model.SysUser">
		select * from tz_sys_user where username = #{username}
	</select>
</mapper>