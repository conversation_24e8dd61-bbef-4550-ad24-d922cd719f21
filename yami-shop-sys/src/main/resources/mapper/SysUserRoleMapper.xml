<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.sys.dao.SysUserRoleMapper">

	<delete id="deleteBatch">
		delete from tz_sys_user_role where role_id in
		<foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>
	
	<delete id="deleteByUserId">
		delete from tz_sys_user_role where user_id = #{userId}
	</delete>
	
	<insert id="insertUserAndUserRole">
	  	insert into tz_sys_user_role (user_id,role_id) values
	  	<foreach collection="roleIdList" item="roleId" separator=",">
	  		(#{userId},#{roleId})
	  	</foreach>
  </insert>
</mapper>